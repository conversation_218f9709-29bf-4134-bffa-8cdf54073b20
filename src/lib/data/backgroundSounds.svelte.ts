import BirdsIcon from '$lib/components/Icons/BirdsIcon.svelte';
import CloudIcon from '$lib/components/Icons/RainIcon.svelte';
import FireIcon from '$lib/components/Icons/FireIcon.svelte';
import type { Attribution } from '$lib/models/Attribution';
import type { Component } from 'svelte';

export type BackgroundSoundResource = {
	url: string;
	attribution: Omit<Partial<Attribution>, 'work'> & { work: Partial<Attribution['work']> };
	icon: Component;
};

export const backgroundSounds: BackgroundSoundResource[] = [
	// {
	// 	url: 'https://static.lofiandgames.com/background-noises/airplane-cabin.mp3',
	// 	attribution: {
	// 		work: {
	// 			name: 'Airplane Cabin',
	// 		},
	// 	},
	// },
	// {
	// 	url: 'https://static.lofiandgames.com/background-noises/artic-wind.mp3',
	// 	attribution: {
	// 		work: {
	// 			name: 'Artic Wind',
	// 		},
	// 	},
	// },
	{
		url: 'https://static.lofiandgames.com/background-noises/birds.mp3',
		attribution: {
			work: {
				name: 'Birds',
			},
		},
		icon: BirdsIcon,
	},
	// {
	// 	url: 'https://static.lofiandgames.com/background-noises/city.mp3',
	// 	attribution: {
	// 		work: {
	// 			name: 'City',
	// 		},
	// 	},
	// },
	// {
	// 	url: 'https://static.lofiandgames.com/background-noises/clock.mp3',
	// 	attribution: {
	// 		work: {
	// 			name: 'Clock',
	// 		},
	// 	},
	// },
	// {
	// 	url: 'https://static.lofiandgames.com/background-noises/crowd.mp3',
	// 	attribution: {
	// 		work: {
	// 			name: 'Crowd',
	// 		},
	// 	},
	// },
	// {
	// 	url: 'https://static.lofiandgames.com/background-noises/fan.mp3',
	// 	attribution: {
	// 		work: {
	// 			name: 'Fan',
	// 		},
	// 	},
	// },
	{
		url: 'https://static.lofiandgames.com/background-noises/fireplace.mp3',
		attribution: {
			work: {
				name: 'Fireplace',
			},
		},
		icon: FireIcon,
	},
	// {
	// 	url: 'https://static.lofiandgames.com/background-noises/keyboard.mp3',
	// 	attribution: {
	// 		work: {
	// 			name: 'Keyboard',
	// 		},
	// 	},
	// },
	// {
	// 	url: 'https://static.lofiandgames.com/background-noises/night.mp3',
	// 	attribution: {
	// 		work: {
	// 			name: 'Night',
	// 		},
	// 	},
	// },
	// {
	// 	url: 'https://static.lofiandgames.com/background-noises/rain-and-thunder.mp3',
	// 	attribution: {
	// 		work: {
	// 			name: 'Rain and Thunder',
	// 		},
	// 	},
	// },
	{
		url: 'https://static.lofiandgames.com/background-noises/rain.mp3',
		attribution: {
			work: {
				name: 'Rain',
			},
		},
		icon: CloudIcon,
	},
	// {
	// 	url: 'https://static.lofiandgames.com/background-noises/restaurant.mp3',
	// 	attribution: {
	// 		work: {
	// 			name: 'Restaurant',
	// 		},
	// 	},
	// },
	// {
	// 	url: 'https://static.lofiandgames.com/background-noises/river.mp3',
	// 	attribution: {
	// 		work: {
	// 			name: 'River',
	// 		},
	// 	},
	// },
	// {
	// 	url: 'https://static.lofiandgames.com/background-noises/snowstorm.mp3',
	// 	attribution: {
	// 		work: {
	// 			name: 'Snowstorm',
	// 		},
	// 	},
	// },
	// {
	// 	url: 'https://static.lofiandgames.com/background-noises/street.mp3',
	// 	attribution: {
	// 		work: {
	// 			name: 'Street',
	// 		},
	// 	},
	// },
	// {
	// 	url: 'https://static.lofiandgames.com/background-noises/underwater.mp3',
	// 	attribution: {
	// 		work: {
	// 			name: 'Underwater',
	// 		},
	// 	},
	// },
	// {
	// 	url: 'https://static.lofiandgames.com/background-noises/waterfall.mp3',
	// 	attribution: {
	// 		work: {
	// 			name: 'Waterfall',
	// 		},
	// 	},
	// },
	// {
	// 	url: 'https://static.lofiandgames.com/background-noises/waves.mp3',
	// 	attribution: {
	// 		work: {
	// 			name: 'Waves',
	// 		},
	// 	},
	// },
	// {
	// 	url: 'https://static.lofiandgames.com/background-noises/whale.mp3',
	// 	attribution: {
	// 		work: {
	// 			name: 'Whale',
	// 		},
	// 	},
	// },
	// {
	// 	url: 'https://static.lofiandgames.com/background-noises/white-noise.mp3',
	// 	attribution: {
	// 		work: {
	// 			name: 'White Noise',
	// 		},
	// 	},
	// },
	// {
	// 	url: 'https://static.lofiandgames.com/background-noises/wind.mp3',
	// 	attribution: {
	// 		work: {
	// 			name: 'Wind',
	// 		},
	// 	},
	// },
];
