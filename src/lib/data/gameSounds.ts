import type { Attribution } from '$lib/models/Attribution';

export type GameSoundResource = {
	url: string;
	attribution?: Attribution;
};

export const menuSelect: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/menu-select.mp3',
	attribution: {
		creator: {
			name: 'Fupicat',
			url: 'https://freesound.org/people/Fupicat/',
		},
		work: {
			name: 'Gamey game Sounds » Videogame Menu Select',
			url: 'https://freesound.org/people/Fupicat/sounds/471937/',
		},
		license: {
			name: 'CC0 1.0',
			url: 'https://creativecommons.org/publicdomain/zero/1.0/',
		},
		modification: 'Changed gain from original',
	},
};

export const menuHighlight: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/menu-highlight.mp3',
	attribution: {
		creator: {
			name: 'Fupicat',
			url: 'https://freesound.org/people/Fupicat/',
		},
		work: {
			name: 'Gamey game Sounds » Videogame Menu Highlight',
			url: 'https://freesound.org/people/Fupicat/sounds/471936/',
		},
		license: {
			name: 'CC0 1.0',
			url: 'https://creativecommons.org/publicdomain/zero/1.0/',
		},
		modification: 'Changed gain from original',
	},
};

export const menuSoftSelect: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/menu-soft-select.mp3',
	attribution: {
		creator: {
			name: 'IENBA',
			url: 'https://freesound.org/people/IENBA/',
		},
		work: {
			name: 'UI Buttons',
			url: 'https://freesound.org/people/IENBA/sounds/762132/',
		},
		license: {
			name: 'CC0 1.0',
			url: 'https://creativecommons.org/publicdomain/zero/1.0/',
		},
		modification: 'Cropped and changed gain from original',
	},
};

export const menuSoftUnselect: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/menu-soft-unselect.mp3',
	attribution: {
		creator: {
			name: 'IENBA',
			url: 'https://freesound.org/people/IENBA/',
		},
		work: {
			name: 'UI Buttons',
			url: 'https://freesound.org/people/IENBA/sounds/762132/',
		},
		license: {
			name: 'CC0 1.0',
			url: 'https://creativecommons.org/publicdomain/zero/1.0/',
		},
		modification: 'Cropped and changed gain from original',
	},
};

export const congrats: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/congrats.mp3',
	attribution: {
		creator: {
			name: 'Fupicat',
			url: 'https://freesound.org/people/Fupicat/',
		},
		work: {
			name: 'Gamey game Sounds » Congrats',
			url: 'https://freesound.org/people/Fupicat/sounds/607207/',
		},
		license: {
			name: 'CC0 1.0',
			url: 'https://creativecommons.org/publicdomain/zero/1.0/',
		},
		modification: 'Changed gain from original',
	},
};

export const sadSynth: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/sad-synth.mp3',
	attribution: {
		creator: {
			name: 'Fupicat',
			url: 'https://freesound.org/people/Fupicat/',
		},
		work: {
			name: 'Gamey game Sounds » Sad Synth',
			url: 'https://freesound.org/people/Fupicat/sounds/538150/',
		},
		license: {
			name: 'CC0 1.0',
			url: 'https://creativecommons.org/publicdomain/zero/1.0/',
		},
		modification: 'Changed gain from original',
	},
};

export const yoink2: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/yoink-2.mp3',
	attribution: {
		creator: {
			name: 'Fupicat',
			url: 'https://freesound.org/people/Fupicat/',
		},
		work: {
			name: 'Gamey game Sounds » Yoink2',
			url: 'https://freesound.org/people/Fupicat/sounds/538156/',
		},
		license: {
			name: 'CC0 1.0',
			url: 'https://creativecommons.org/publicdomain/zero/1.0/',
		},
		modification: 'Changed gain from original',
	},
};

export const cardFlip: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/card-flip.mp3',
	attribution: {
		creator: {
			name: 'f4ngy',
			url: 'https://freesound.org/people/f4ngy/',
		},
		work: {
			name: 'Card Flip',
			url: 'https://freesound.org/people/f4ngy/sounds/240776/',
		},
		license: {
			name: 'CC BY 4.0',
			url: 'https://creativecommons.org/licenses/by/4.0/',
		},
		modification: 'Changed gain from original',
	},
};

export const deckShuffle: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/deck-shuffle.mp3',
	attribution: {
		creator: {
			name: 'ROBAMOS',
			url: 'https://freesound.org/people/ROBAMOS/',
		},
		work: {
			name: 'Index Card Flip Manipulation.aif',
			url: 'https://freesound.org/people/ROBAMOS/sounds/339015/',
		},
		license: {
			name: 'CC0 1.0',
			url: 'https://creativecommons.org/publicdomain/zero/1.0/',
		},
		modification: 'Cropped and changed gain from original',
	},
};

export const deckQuickShuffle: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/deck-quick-shuffle.mp3',
	attribution: {
		creator: {
			name: 'Pixabay',
			url: 'https://pixabay.com/users/pixabay-1/',
		},
		work: {
			name: 'Card Sounds',
			url: 'https://pixabay.com/sound-effects/card-sounds-35956/',
		},
		license: {
			name: 'Pixabay Content License',
			url: 'https://pixabay.com/service/license-summary/',
		},
		modification: 'Cropped, equalized, and changed gain from original',
	},
};

export const deckBack: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/deck-back.mp3',
	attribution: deckShuffle.attribution,
};

export const deckQuickBack: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/deck-quick-back.mp3',
	attribution: deckShuffle.attribution,
};

export const cardPlay: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/card-play.mp3',
	attribution: {
		creator: {
			name: 'BMacZero',
			url: 'https://freesound.org/people/BMacZero/',
		},
		work: {
			name: 'Card Game Collection » Contact1.wav',
			url: 'https://freesound.org/people/BMacZero/sounds/96127/',
		},
		license: {
			name: 'CC0 1.0',
			url: 'https://creativecommons.org/publicdomain/zero/1.0/',
		},
		modification: 'Changed gain from original',
	},
};

export const applause: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/applause.mp3',
	attribution: {
		creator: {
			name: 'RHumphries',
			url: 'https://freesound.org/people/RHumphries/',
		},
		work: {
			name: 'Applause » rbh Applause 02 big.WAV',
			url: 'https://freesound.org/people/RHumphries/sounds/1922/',
		},
		license: {
			name: 'CC BY 3.0',
			url: 'https://creativecommons.org/licenses/by/3.0/',
		},
		modification: 'Changed gain from original',
	},
};

export const gameWin: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/game-win.mp3',
	attribution: {
		creator: {
			name: 'MLaudio',
			url: 'https://freesound.org/people/MLaudio/',
		},
		work: {
			name: 'magic_game_win_success.wav',
			url: 'https://freesound.org/people/MLaudio/sounds/615099/',
		},
		license: {
			name: 'CC0 1.0',
			url: 'https://creativecommons.org/publicdomain/zero/1.0/',
		},
		modification: 'Changed gain from original',
	},
};

export const success1: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/success-1.mp3',
	attribution: {
		creator: {
			name: 'Leszek Szary',
			url: 'https://freesound.org/people/Leszek_Szary/',
		},
		work: {
			name: 'success 1',
			url: 'https://freesound.org/people/Leszek_Szary/sounds/171671/',
		},
		license: {
			name: 'CC0 1.0',
			url: 'https://creativecommons.org/publicdomain/zero/1.0/',
		},
		modification: 'Changed gain from original',
	},
};

export const success1Original: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/success-1-original.mp3',
	attribution: {
		creator: {
			name: 'Leszek Szary',
			url: 'https://freesound.org/people/Leszek_Szary/',
		},
		work: {
			name: 'success 1',
			url: 'https://freesound.org/people/Leszek_Szary/sounds/171671/',
		},
		license: {
			name: 'CC0 1.0',
			url: 'https://creativecommons.org/publicdomain/zero/1.0/',
		},
		modification: 'Changed gain from original',
	},
};

export const gameOver: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/game-over.mp3',
	attribution: {
		creator: {
			name: 'Leszek Szary',
			url: 'https://freesound.org/people/Leszek_Szary/',
		},
		work: {
			name: 'game over',
			url: 'https://freesound.org/people/Leszek_Szary/sounds/133283/',
		},
		license: {
			name: 'CC0 1.0',
			url: 'https://creativecommons.org/publicdomain/zero/1.0/',
		},
		modification: 'Changed gain from original',
	},
};

export const swoosh: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/swoosh.mp3',
	attribution: {
		creator: {
			name: 'lesaucisson',
			url: 'https://freesound.org/people/lesaucisson/',
		},
		work: {
			name: 'Swoosh » swoosh-2.mp3',
			url: 'https://freesound.org/people/lesaucisson/sounds/585256/',
		},
		license: {
			name: 'CC0 1.0',
			url: 'https://creativecommons.org/publicdomain/zero/1.0/',
		},
		modification: 'Changed gain from original',
	},
};

export const flappyWoosh: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/flappy-woosh.mp3',
	attribution: {
		creator: {
			name: 'combine2005',
			url: 'https://freesound.org/people/combine2005/',
		},
		work: {
			name: 'flappy_whoosh.mp3',
			url: 'https://freesound.org/people/combine2005/sounds/488275/',
		},
		license: {
			name: 'CC0 1.0',
			url: 'https://creativecommons.org/publicdomain/zero/1.0/',
		},
		modification: 'Changed gain, equalized, and cropped from original',
	},
};

export const impact: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/impact.mp3',
	attribution: {
		creator: {
			name: 'cryanrautha',
			url: 'https://freesound.org/people/cryanrautha/',
		},
		work: {
			name: 'CLARKS BIG Punches Hits Impacts',
			url: 'https://freesound.org/people/cryanrautha/sounds/369326/',
		},
		license: {
			name: 'CC0 1.0',
			url: 'https://creativecommons.org/publicdomain/zero/1.0/',
		},
		modification: 'Changed gain, equalized, and cropped from original',
	},
};

export const impact2: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/impact2.mp3',
	attribution: {
		creator: {
			name: 'djouppi10',
			url: 'https://freesound.org/people/djouppi10/',
		},
		work: {
			name: 'CINDER BLOCK THUD IN GRASS.wav',
			url: 'https://freesound.org/people/djouppi10/sounds/150491/',
		},
		license: {
			name: 'CC0 1.0',
			url: 'https://creativecommons.org/publicdomain/zero/1.0/',
		},
		modification: 'Changed gain, equalized, and cropped from original',
	},
};

export const coin: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/coin.mp3',
	attribution: {
		creator: {
			name: 'AceOfSpadesProduc100',
			url: 'https://freesound.org/people/AceOfSpadesProduc100/',
		},
		work: {
			name: 'Coins 1',
			url: 'https://freesound.org/people/AceOfSpadesProduc100/sounds/341695/',
		},
		license: {
			name: 'CC BY 4.0',
			url: 'https://creativecommons.org/licenses/by/4.0/',
		},
		modification: 'Changed gain, and equalized from original',
	},
};

export const fall: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/fall.mp3',
	attribution: {
		creator: {
			name: 'MATRIXXX_',
			url: 'https://freesound.org/people/MATRIXXX_/',
		},
		work: {
			name: 'Retro, Drop 01.wav',
			url: 'https://freesound.org/people/MATRIXXX_/sounds/415991/',
		},
		license: {
			name: 'CC0 1.0',
			url: 'https://creativecommons.org/publicdomain/zero/1.0/',
		},
		modification: 'Changed gain, and equalized from original',
	},
};

export const death: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/death.mp3',
	attribution: {
		creator: {
			name: 'Fupicat',
			url: 'https://freesound.org/people/Fupicat/',
		},
		work: {
			name: 'Gamey game Sounds » Videogame Death Sound',
			url: 'https://freesound.org/people/Fupicat/sounds/475347/',
		},
		license: {
			name: 'CC0 1.0',
			url: 'https://creativecommons.org/publicdomain/zero/1.0/',
		},
		modification: 'Changed gain from original',
	},
};

export const explosion: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/explosion.mp3',
	attribution: {
		creator: {
			name: 'ljudman',
			url: 'https://freesound.org/people/ljudman/',
		},
		license: {
			name: 'Sampling Plus 1.0',
			url: 'https://creativecommons.org/licenses/sampling+/1.0/',
		},
		work: {
			name: 'grenade.wav',
			url: 'https://freesound.org/people/ljudman/sounds/33245/',
		},
		modification: 'Changed gain, equalized, and cropped from original',
	},
};

export const dig: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/dig.mp3',
	attribution: {
		creator: {
			name: 'tanapistorius',
			url: 'https://freesound.org/people/tanapistorius/',
		},
		work: {
			name: 'Digging .wav',
			url: 'https://freesound.org/people/tanapistorius/sounds/367010/',
		},
		license: {
			name: 'CC BY-NC 3.0',
			url: 'https://creativecommons.org/licenses/by-nc/3.0/',
		},
		modification: 'Changed gain and cropped from original',
	},
};

export const flag: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/flag.mp3',
	attribution: {
		creator: {
			name: 'ZoviPoland',
			url: 'https://freesound.org/people/ZoviPoland/',
		},
		work: {
			name: 'Clothes » clothes drop 5',
			url: 'https://freesound.org/people/ZoviPoland/sounds/517729/',
		},
		license: {
			name: 'CC0 1.0',
			url: 'https://creativecommons.org/publicdomain/zero/1.0/',
		},
		modification: 'Changed gain, equalized, and cropped from original',
	},
};

export const flag2: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/flag-2.mp3',
	attribution: {
		creator: {
			name: 'MattRuthSound',
			url: 'https://freesound.org/people/MattRuthSound/',
		},
		work: {
			name: 'Cloth » Cloth, Bedding, brush, sweep-013.wav',
			url: 'https://freesound.org/people/MattRuthSound/sounds/561572/',
		},
		license: {
			name: 'CC BY 3.0',
			url: 'https://creativecommons.org/licenses/by/3.0/',
		},
		modification: 'Changed gain, equalized, and cropped from original',
	},
};

export const switch1: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/switch-1.mp3',
	attribution: {
		creator: {
			name: 'FractalStudios',
			url: 'https://freesound.org/people/FractalStudios/',
		},
		work: {
			name: 'Switches » Light Switch.wav',
			url: 'https://freesound.org/people/FractalStudios/sounds/363084/',
		},
		license: {
			name: 'CC0 1.0',
			url: 'https://creativecommons.org/publicdomain/zero/1.0/',
		},
		modification: 'Changed gain and cropped from original',
	},
};

export const switch2: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/switch-2.mp3',
	attribution: switch1.attribution,
};

export const switch3: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/switch-3.mp3',
	attribution: switch1.attribution,
};

export const chess1: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/chess-1.mp3',
	attribution: {
		creator: {
			name: 'simone_ds',
			url: 'https://freesound.org/people/simone_ds/',
		},
		work: {
			name: 'chess pieces.wav',
			url: 'https://freesound.org/people/simone_ds/sounds/366065/',
		},
		license: {
			name: 'CC0 1.0',
			url: 'https://creativecommons.org/publicdomain/zero/1.0/',
		},
		modification: 'Changed gain and cropped from original',
	},
};

export const chess2: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/chess-2.mp3',
	attribution: {
		creator: {
			name: 'simone_ds',
			url: 'https://freesound.org/people/simone_ds/',
		},
		work: {
			name: 'chess pieces.wav',
			url: 'https://freesound.org/people/simone_ds/sounds/366065/',
		},
		license: {
			name: 'CC0 1.0',
			url: 'https://creativecommons.org/publicdomain/zero/1.0/',
		},
		modification: 'Changed gain and cropped from original',
	},
};

export const chess3: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/chess-3.mp3',
	attribution: {
		creator: {
			name: 'simone_ds',
			url: 'https://freesound.org/people/simone_ds/',
		},
		work: {
			name: 'chess pieces.wav',
			url: 'https://freesound.org/people/simone_ds/sounds/366065/',
		},
		license: {
			name: 'CC0 1.0',
			url: 'https://creativecommons.org/publicdomain/zero/1.0/',
		},
		modification: 'Changed gain and cropped from original',
	},
};

export const chess4: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/chess-4.mp3',
	attribution: {
		creator: {
			name: 'simone_ds',
			url: 'https://freesound.org/people/simone_ds/',
		},
		work: {
			name: 'chess pieces.wav',
			url: 'https://freesound.org/people/simone_ds/sounds/366065/',
		},
		license: {
			name: 'CC0 1.0',
			url: 'https://creativecommons.org/publicdomain/zero/1.0/',
		},
		modification: 'Changed gain and cropped from original',
	},
};

export const chessPieces: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/chess-pieces.mp3',
	attribution: {
		creator: {
			name: 'IENBA',
			url: 'https://freesound.org/people/IENBA/',
		},
		work: {
			name: 'Chess Pieces Drop',
			url: 'https://freesound.org/people/IENBA/sounds/755250/',
		},
		license: {
			name: 'CC0 1.0',
			url: 'https://creativecommons.org/publicdomain/zero/1.0/',
		},
		modification: 'Changed gain, equalized, and cropped from original',
	},
};

export const staticNoise: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/static.mp3',
	attribution: {
		creator: {
			name: 'kyles',
			url: 'https://freesound.org/people/kyles/',
		},
		work: {
			name: 'static varied soft good for TV white noise.flac',
			url: 'https://freesound.org/people/kyles/sounds/637826/',
		},
		license: {
			name: 'CC0 1.0',
			url: 'https://creativecommons.org/publicdomain/zero/1.0/',
		},
		modification: 'Cropped from original',
	},
};

export const tape1: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/tape-1.mp3',
	attribution: {
		creator: {
			name: 'constructabeat',
			url: 'https://freesound.org/people/constructabeat/',
		},
		work: {
			name: 'Stop Start Tape. Player',
			url: 'https://freesound.org/people/constructabeat/sounds/258392/',
		},
		license: {
			name: 'CC BY 3.0',
			url: 'https://creativecommons.org/licenses/by/3.0/',
		},
		modification: 'Cropped from original',
	},
};

export const tape2: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/tape-2.mp3',
	attribution: {
		creator: {
			name: 'constructabeat',
			url: 'https://freesound.org/people/constructabeat/',
		},
		work: {
			name: 'Stop Start Tape. Player',
			url: 'https://freesound.org/people/constructabeat/sounds/258392/',
		},
		license: {
			name: 'CC BY 3.0',
			url: 'https://creativecommons.org/licenses/by/3.0/',
		},
		modification: 'Cropped from original',
	},
};

export const squareStart: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/square-start.mp3',
};

export const squareE: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/square-e.mp3',
};

export const squareD: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/square-d.mp3',
};

export const squareC: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/square-c.mp3',
};

export const squareF: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/square-f.mp3',
};

export const squareError: GameSoundResource = {
	url: 'https://static.lofiandgames.com/sounds/square-error.mp3',
};
