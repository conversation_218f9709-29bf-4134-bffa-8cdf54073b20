import type { Attribution } from '$lib/models/Attribution';

export type Wallpaper = {
	url: string;
	backgroundPosition?: string;
	attribution?: Attribution;
};

export const ghibliDarkWallpapers: Wallpaper[] = [
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/ponyo028.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: '<PERSON><PERSON><PERSON>',
				url: 'https://www.ghibli.jp/works/ponyo/',
			},
		},
		backgroundPosition: '40%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/howl011.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: `Howl's Moving Castle`,
				url: 'https://www.ghibli.jp/works/howl/',
			},
		},
		backgroundPosition: '30%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/howl041.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: `Howl's Moving Castle`,
				url: 'https://www.ghibli.jp/works/howl/',
			},
		},
		backgroundPosition: '30%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/chihiro011.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'Spirited Away',
				url: 'https://www.ghibli.jp/works/chihiro/',
			},
		},
		backgroundPosition: '70%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/porco007.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'Porco Rosso',
				url: 'https://www.ghibli.jp/works/porco/',
			},
		},
		backgroundPosition: '52%',
	},

	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/majo018.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: `Kiki's Delivery Service`,
				url: 'https://www.ghibli.jp/works/majo/',
			},
		},
		backgroundPosition: '75%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/majo011.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: `Kiki's Delivery Service`,
				url: 'https://www.ghibli.jp/works/majo/',
			},
		},
		backgroundPosition: '45%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/majo024.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: `Kiki's Delivery Service`,
				url: 'https://www.ghibli.jp/works/majo/',
			},
		},
		backgroundPosition: '45%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/totoro029.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'My Neighbor Totoro',
				url: 'https://www.ghibli.jp/works/totoro/',
			},
		},
		backgroundPosition: '68%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/totoro036.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'My Neighbor Totoro',
				url: 'https://www.ghibli.jp/works/totoro/',
			},
		},
		backgroundPosition: '50%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/totoro037.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'My Neighbor Totoro',
				url: 'https://www.ghibli.jp/works/totoro/',
			},
		},
		backgroundPosition: '58%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/laputa034.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'Castle in the Sky',
				url: 'https://www.ghibli.jp/works/laputa/',
			},
		},
		backgroundPosition: '53%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/nausicaa002.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'Nausicaä of the Valley of the Wind',
				url: 'https://www.ghibli.jp/works/nausicaa/',
			},
		},
		backgroundPosition: '50%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/nausicaa013.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'Nausicaä of the Valley of the Wind',
				url: 'https://www.ghibli.jp/works/nausicaa/',
			},
		},
		backgroundPosition: '50%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/onyourmark002.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'On Your Mark',
				url: 'https://www.ghibli.jp/works/onyourmark/',
			},
		},
		backgroundPosition: '70%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/kokurikozaka036.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'From Up on Poppy Hill',
				url: 'https://www.ghibli.jp/works/kokurikozaka/',
			},
		},
		backgroundPosition: '30%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/kokurikozaka038.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'From Up on Poppy Hill',
				url: 'https://www.ghibli.jp/works/kokurikozaka/',
			},
		},
		backgroundPosition: '50%',
	},
];

export const ghibliLightWallpapers: Wallpaper[] = [
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/kimitachi034.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'The Boy and the Heron',
				url: 'https://www.ghibli.jp/works/kimitachi/',
			},
		},
		backgroundPosition: '35%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/kimitachi016.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'The Boy and the Heron',
				url: 'https://www.ghibli.jp/works/kimitachi/',
			},
		},
		backgroundPosition: '55%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/marnie005.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'When Marnie Was There',
				url: 'https://www.ghibli.jp/works/marnie/',
			},
		},
		backgroundPosition: '85%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/marnie009.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'When Marnie Was There',
				url: 'https://www.ghibli.jp/works/marnie/',
			},
		},
		backgroundPosition: '55%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/marnie006.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'When Marnie Was There',
				url: 'https://www.ghibli.jp/works/marnie/',
			},
		},
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/marnie021.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'When Marnie Was There',
				url: 'https://www.ghibli.jp/works/marnie/',
			},
		},
		backgroundPosition: '54%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/marnie035.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'When Marnie Was There',
				url: 'https://www.ghibli.jp/works/marnie/',
			},
		},
		backgroundPosition: '63%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/marnie037.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'When Marnie Was There',
				url: 'https://www.ghibli.jp/works/marnie/',
			},
		},
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/marnie047.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'When Marnie Was There',
				url: 'https://www.ghibli.jp/works/marnie/',
			},
		},
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/kazetachinu023.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'The Wind Rises',
				url: 'https://www.ghibli.jp/works/kazetachinu/',
			},
		},
		backgroundPosition: '45%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/kazetachinu050.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'The Wind Rises',
				url: 'https://www.ghibli.jp/works/kazetachinu/',
			},
		},
		backgroundPosition: '35%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/kazetachinu020.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'The Wind Rises',
				url: 'https://www.ghibli.jp/works/kazetachinu/',
			},
		},
		backgroundPosition: '70%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/karigurashi002.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'Arrietty',
				url: 'https://www.ghibli.jp/works/karigurashi/',
			},
		},
		backgroundPosition: '50%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/karigurashi021.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'Arrietty',
				url: 'https://www.ghibli.jp/works/karigurashi/',
			},
		},
		backgroundPosition: '61%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/ponyo049.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'Arrietty',
				url: 'https://www.ghibli.jp/works/ponyo/',
			},
		},
		backgroundPosition: '52%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/howl005.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: `Howl's Moving Castle`,
				url: 'https://www.ghibli.jp/works/howl/',
			},
		},
		backgroundPosition: '52%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/chihiro042.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'Spirited Away',
				url: 'https://www.ghibli.jp/works/chihiro/',
			},
		},
		backgroundPosition: '52%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/chihiro043.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'Spirited Away',
				url: 'https://www.ghibli.jp/works/chihiro/',
			},
		},
		backgroundPosition: '50%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/porco015.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'Porco Rosso',
				url: 'https://www.ghibli.jp/works/porco/',
			},
		},
		backgroundPosition: '47%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/porco026.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'Porco Rosso',
				url: 'https://www.ghibli.jp/works/porco/',
			},
		},
		backgroundPosition: '19%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/majo001.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: `Kiki's Delivery Service`,
				url: 'https://www.ghibli.jp/works/majo/',
			},
		},
		backgroundPosition: '45%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/majo038.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: `Kiki's Delivery Service`,
				url: 'https://www.ghibli.jp/works/majo/',
			},
		},
		backgroundPosition: '55%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/totoro019.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'My Neighbor Totoro',
				url: 'https://www.ghibli.jp/works/totoro/',
			},
		},
		backgroundPosition: '85%',
	},
	{
		url: 'https://static.lofiandgames.com/wallpapers/ghibli/omoide008.jpg',
		attribution: {
			creator: { name: 'Studio Ghibli', url: 'https://www.ghibli.jp/' },
			work: {
				name: 'Only Yesterday',
				url: 'https://www.ghibli.jp/works/omoide/',
			},
		},
		backgroundPosition: '55%',
	},
];

export const darkWallpapers = ghibliDarkWallpapers;

export const lightWallpapers = ghibliLightWallpapers;

// export const darkWallpapers: Wallpaper[] = [
// 	{
// 		url: 'https://static.lofiandgames.com/wallpapers/city-top.avif',
// 		backgroundPosition: '25%',
// 	},
// 	{
// 		url: 'https://static.lofiandgames.com/wallpapers/city-middle.avif',
// 		backgroundPosition: '50%',
// 	},
// 	{
// 		url: 'https://static.lofiandgames.com/wallpapers/hot-cocoa.avif',
// 		backgroundPosition: '50%',
// 	},
// 	{
// 		url: 'https://static.lofiandgames.com/wallpapers/sleep.avif',
// 		backgroundPosition: '65%',
// 	},
// 	{
// 		url: 'https://static.lofiandgames.com/wallpapers/relax-window.avif',
// 		backgroundPosition: '65%',
// 	},
// 	{
// 		url: 'https://static.lofiandgames.com/wallpapers/coffee.avif',
// 		backgroundPosition: '40%',
// 	},
// 	{
// 		url: 'https://static.lofiandgames.com/wallpapers/dog-walk.avif',
// 		backgroundPosition: '55%',
// 	},
// 	{
// 		url: 'https://static.lofiandgames.com/wallpapers/sleepy.avif',
// 		backgroundPosition: '50%',
// 	},
// 	{
// 		url: 'https://static.lofiandgames.com/wallpapers/room.avif',
// 		backgroundPosition: '20%',
// 	},
// 	{
// 		url: 'https://static.lofiandgames.com/wallpapers/dog-walk-2.avif',
// 		backgroundPosition: '50%',
// 	},
// 	{
// 		url: 'https://static.lofiandgames.com/wallpapers/streetcar_by_klegs_ddxxwqr.jpeg',
// 		backgroundPosition: '57%',
// 		attribution: {
// 			creator: {
// 				name: 'Klegs',
// 				url: 'https://www.deviantart.com/klegs',
// 			},
// 			work: {
// 				name: 'Streetcar',
// 				url: 'https://www.deviantart.com/klegs/art/Streetcar-843069699',
// 			},
// 		},
// 	},
// ];

// export const lightWallpapers: Wallpaper[] = [
// 	{
// 		url: 'https://static.lofiandgames.com/wallpapers/horse.jpeg',
// 		backgroundPosition: '58%',
// 	},
// 	{
// 		url: 'https://static.lofiandgames.com/wallpapers/beach.webp',
// 		backgroundPosition: '65%',
// 	},
// 	{
// 		url: 'https://static.lofiandgames.com/wallpapers/aylopop-1.avif',
// 		backgroundPosition: '70%',
// 		attribution: {
// 			creator: {
// 				name: 'aylopop',
// 				url: 'https://ko-fi.com/aylopop',
// 			},
// 			work: {
// 				name: 'Riding into the city for the night',
// 				url: 'https://www.threads.net/@aylopop/post/DF00AiZzetF',
// 			},
// 		},
// 	},
// 	{
// 		url: 'https://static.lofiandgames.com/wallpapers/rainy-day-2.avif',
// 	},
// 	{
// 		url: 'https://static.lofiandgames.com/wallpapers/river.avif',
// 		backgroundPosition: '85%',
// 	},
// 	{
// 		url: 'https://static.lofiandgames.com/wallpapers/painting.avif',
// 		backgroundPosition: '50%',
// 	},
// 	{
// 		url: 'https://static.lofiandgames.com/wallpapers/rainy-day.avif',
// 		backgroundPosition: '60%',
// 	},
// 	{
// 		url: 'https://static.lofiandgames.com/wallpapers/bicycle.avif',
// 		backgroundPosition: '40%',
// 	},
// 	{
// 		url: 'https://static.lofiandgames.com/wallpapers/beautiful-view.avif',
// 		backgroundPosition: '55%',
// 	},
// 	{
// 		url: 'https://static.lofiandgames.com/wallpapers/pitcher_plant_by_klegs_dd6d7rq.jpeg',
// 		attribution: {
// 			creator: {
// 				name: 'Klegs',
// 				url: 'https://www.deviantart.com/klegs',
// 			},
// 			work: {
// 				name: 'Pitcher-plant',
// 				url: 'https://www.deviantart.com/klegs/art/Pitcher-plant-796754582',
// 			},
// 		},
// 	},
// 	{
// 		url: 'https://static.lofiandgames.com/wallpapers/dog_walk_by_klegs_dbyspgt.jpg',
// 		backgroundPosition: '75%',
// 		attribution: {
// 			creator: {
// 				name: 'Klegs',
// 				url: 'https://www.deviantart.com/klegs',
// 			},
// 			work: {
// 				name: 'Dog Walk',
// 				url: 'https://www.deviantart.com/klegs/art/Dog-Walk-723574253',
// 			},
// 		},
// 	},
// ];

export const wallpapers: Wallpaper[] = [...darkWallpapers, ...lightWallpapers];
