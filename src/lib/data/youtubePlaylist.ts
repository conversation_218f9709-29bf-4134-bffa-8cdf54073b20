import type { Creator } from '$lib/models/Attribution';
import type { PlaylistSong } from '$lib/models/Playlist';

export interface YouTubeSong extends PlaylistSong {
	id: string;
}

function yt({ id, title, creator }: { id: string; title: string; creator: Creator }): YouTubeSong {
	return {
		id,
		url: `https://www.youtube.com/watch?v=${id}`,
		attribution: {
			work: {
				name: title,
				url: `https://www.youtube.com/watch?v=${id}`,
			},
			creator,
			album: {
				picture: `https://img.youtube.com/vi/${id}/default.jpg`,
				url: `https://www.youtube.com/watch?v=${id}`,
			},
		},
	};
}

export const youtubePlaylist: YouTubeSong[] = [
	yt({
		id: 'jfKfPfyJRdk',
		title: 'lofi hip hop radio 📚 beats to relax/study to',
		creator: { name: 'Lofi Girl', url: 'https://www.youtube.com/@LofiGirl' },
	}),
	yt({
		id: 'HuFYqnbVbzY',
		title: 'jazz lofi radio 🎷 beats to chill/study to',
		creator: { name: 'Lo<PERSON> Girl', url: 'https://www.youtube.com/@LofiGirl' },
	}),
	yt({
		id: 'P6Segk8cr-c',
		title: 'sad lofi radio ☔ beats for rainy days',
		creator: { name: 'Lofi Girl', url: 'https://www.youtube.com/@LofiGirl' },
	}),
	yt({
		id: 'TtkFsfOP9QI',
		title: 'peaceful piano radio 🎹 music to focus/study to',
		creator: { name: 'Lofi Girl', url: 'https://www.youtube.com/@LofiGirl' },
	}),
	yt({
		id: '5yx6BWlEVcY',
		title: 'Chillhop Radio - jazzy & lofi hip hop beats 🐾',
		creator: { name: 'Chillhop Music', url: 'https://www.youtube.com/@ChillhopMusic' },
	}),
	yt({
		id: '7NOSDKb0HlU',
		title: 'lofi hip hop radio - beats to study/relax to 🐾',
		creator: { name: 'Chillhop Music', url: 'https://www.youtube.com/@ChillhopMusic' },
	}),
	yt({
		id: 'qH3fETPsqXU',
		title: '【24/7 CHILL LOFI HIP HOP RADIO】beats to sleep/relax/study to',
		creator: { name: 'Chill with Taiki', url: 'https://www.youtube.com/@ChillwithTaiki' },
	}),
	yt({
		id: 'WelpRyoV0UY',
		title: 'lofi hip hop radio 🌿 chill beats to relax/study to',
		creator: { name: 'Chill with Taiki', url: 'https://www.youtube.com/@ChillwithTaiki' },
	}),
];
