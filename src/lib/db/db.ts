import Dexie, { type EntityTable } from 'dexie';

interface Stat {
	game: string;
	gameVariant?: string;
	createdAt: Date;
	bestTime: number;
	totalTime: number;
	averageTime: number;
	totalGames: number;
	wonGames: number;
	customStats: Record<string, number>;
}

interface PinnedStats {
	game: string;
	stats: Array<string | null>;
}

interface DailyGame {
	game: string;
	gameVariant?: string;
	/** Played days in yyyy/mm/dd format */
	playedDays: string[];
}

const db = new Dexie('LofiAndGamesDB') as <PERSON><PERSON> & {
	stats: EntityTable<Stat>;
	pinnedStats: EntityTable<PinnedStats>;
	dailyGame: EntityTable<DailyGame>;
};

db.version(2).stores({
	stats: '[game+gameVariant]',
	pinnedStats: 'game',
	dailyGame: '[game+gameVariant]',
});

export type { Stat };
export { db };
