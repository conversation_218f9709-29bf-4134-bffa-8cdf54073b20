<script lang="ts" module>
	import type { Action } from 'svelte/action';

	export const clickOutside: Action<
		HTMLElement,
		undefined,
		{
			onclickoutside: (e: CustomEvent) => void;
		}
	> = (node) => {
		// the node has been mounted in the DOM
		const handleClick = (event: MouseEvent) => {
			if (node && !node.contains(event.target as HTMLElement) && !event.defaultPrevented) {
				node.dispatchEvent(
					new CustomEvent('clickoutside', {
						detail: event,
					}),
				);
			}
		};

		$effect(() => {
			document.addEventListener('click', handleClick, true);

			return () => {
				document.removeEventListener('click', handleClick, true);
			};
		});
	};
</script>
