import { page } from '$app/state';
import { untrack, type Component } from 'svelte';
import { toast } from 'svelte-sonner';

interface Notification {
	title: string;
	description: string;
	key: string;
	date: string;
	read?: boolean;
	link?: {
		name: string;
		url: string;
	};
	cta?: Component;
	feedback?: boolean;
	feedbackContext?: string;
	asToastOnPages?: string[];
}

let allNotifications: Notification[] = [
	{
		key: 'playlight',
		date: '2025-06-16',
		title: 'Discover More Games',
		description: `We've partnered with Playlight to bring you even more casual games to enjoy! Check out the "More games" section on our home page to find new titles to play!`,
		asToastOnPages: ['/', '/chill', '/daily'],
	},
	{
		key: 'favorites',
		date: '2025-05-30',
		title: 'Favorites',
		description:
			'You can now favorite games for quick access! Click on the star icon on the home page to edit your favorites!',
	},
	{
		key: 'fix-checkers-crashing',
		date: '2025-05-26',
		title: 'Fix Checkers Crashes',
		description:
			"We've fixed stability issues in the Checkers game that caused crashes during extended play, especially in Very Hard mode. This update also significantly reduces memory usage, ensuring smoother gameplay for everyone.",
	},
];

let notificationsWithReadState: Notification[] = $state([]);

const storageKey = 'notifications';

function readNotification(notification: Notification) {
	if (notification.read) {
		return;
	}

	notificationsWithReadState = notificationsWithReadState.map((n) => {
		if (n.key === notification.key) {
			return {
				...notification,
				read: true,
			};
		}

		return n;
	});

	localStorage.setItem(
		storageKey,
		JSON.stringify(
			notificationsWithReadState.map((notification) => {
				return {
					key: notification.key,
					date: notification.date,
					read: notification.read,
				};
			}),
		),
	);
}

function readAllNotifications() {
	allNotifications.forEach(readNotification);
}

let hasUnreadNotifications = $derived(
	notificationsWithReadState.some((notification) => !notification.read),
);

function handleStorageChange() {
	checkNotificationsFromStorage();
}

function checkNotificationsFromStorage() {
	const storedNotifications = JSON.parse(localStorage.getItem(storageKey) ?? '[]');

	notificationsWithReadState = allNotifications.map((n) => {
		const item = storedNotifications.find(
			(item: any) => item.key === n.key && item.date === n.date,
		);

		if (item) {
			return {
				...n,
				read: item.read,
			};
		}

		return n;
	});
}

/**
 * Must ignore first check because svelte assigns the pathname to / first,
 * then it changes to the actual page
 **/
let isFirstPageCheck = $state(true);
const toastInitWaitTime = 500;

$effect.root(() => {
	$effect.pre(() => {
		untrack(() => {
			checkNotificationsFromStorage();
		});
	});

	$effect(() => {
		untrack(() => {
			window.addEventListener('storage', handleStorageChange);
		});
	});

	$effect(function showToastOnPageView() {
		const pathname = page.url.pathname;

		if (isFirstPageCheck) {
			isFirstPageCheck = false;
			return;
		}

		untrack(() => {
			setTimeout(() => {
				const notificationsToShow = notifications.all
					.filter((notifications) => !notifications.read)
					.filter((notification) => notification.asToastOnPages?.includes(pathname))
					.reverse();

				notificationsToShow.forEach((notification) => {
					toast(notification.title, {
						id: notification.key,
						description: notification.description,
						duration: Number.POSITIVE_INFINITY,
						onDismiss() {
							notifications.readNotification(notification);
						},
						action: {
							label: 'Got it',
							onClick() {
								notifications.readNotification(notification);
							},
						},
					});
				});
			}, toastInitWaitTime);
		});
	});
});

export const notifications = {
	get all() {
		return notificationsWithReadState;
	},
	get hasUnreadNotifications() {
		return hasUnreadNotifications;
	},
	readNotification,
	readAllNotifications,
};
