import { Timer } from '$lib/util/Timer.svelte';
import { SettingsManager } from '$lib/util/SettingsManager.svelte';
import { untrack } from 'svelte';
import { Howl } from 'howler';
import { success1Original } from '$lib/data/gameSounds';
import { toast } from 'svelte-sonner';
import { page } from '$app/state';
import { goto } from '$app/navigation';

class _PomodoroTimer {
	readonly sound = new Howl({
		src: success1Original.url,
		/**
		 * Audio start is weird on safari when html5 is true and
		 * very small audios do not play
		 **/
		html5: false,
		preload: true,
		volume: 1,
	});
	readonly timer = new Timer({ countdownDuration: 1000 * 60 * 25 });
	readonly breakTimer = new Timer({ countdownDuration: 1000 * 60 * 5 });
	readonly settings = new SettingsManager({
		key: 'pomodoro-timer-settings',
		defaultSettings: {
			numberAnimations: true,
			finishedAnimations: true,
			switchTimersOnEnd: true,
			autoStartNextTimer: false,
			timer: 25,
			break: 5,
			volume: 1,
		},
	});
	private _intervalId = -1;
	private _resetTimerId = -1;
	timerEndedId = $state<Symbol | null>(null);

	private _effectiveTimer = $state(this.timer);

	get effectiveTimer() {
		return this._effectiveTimer;
	}

	set effectiveTimer(timer: Timer) {
		this._effectiveTimer = timer;

		clearTimeout(this._resetTimerId);
		this._resetTimerId = -1;
	}

	constructor() {
		this.settings.load();
		this.sound.volume(this.settings.settings.volume);
		this.timer.countdownDuration = 1000 * 60 * this.settings.settings.timer;
		this.breakTimer.countdownDuration = 1000 * 60 * this.settings.settings.break;

		$effect.root(() => {
			// reset break timer when pomodoro timer starts
			$effect(() => {
				if (this.timer.startedAt) {
					untrack(() => this.breakTimer).reset();
					this.effectiveTimer = this.timer;
				}
			});

			// reset pomodoro timer when break timer starts
			$effect(() => {
				if (this.breakTimer.startedAt) {
					untrack(() => this.timer).reset();
					this.effectiveTimer = this.breakTimer;
				}
			});

			// watch timers when they start
			$effect(() => {
				if (this.breakTimer.startedAt || this.timer.startedAt) {
					untrack(() => this.watchTimer());
				}
			});
		});
	}

	private handleTimerEnded() {
		clearInterval(this._intervalId);
		this.sound.play();
		this.timerEndedId = Symbol();

		if (page.url.pathname !== '/chill') {
			toast(`Your ${this.effectiveTimer === this.timer ? 'timer' : 'break'} has ended!`, {
				action: {
					label: 'Go back',
					onClick: () => {
						goto('/chill');
					},
				},
				duration: 10_000,
			});
		}

		const timerToReset = this.effectiveTimer;

		clearTimeout(this._resetTimerId);

		this._resetTimerId = setTimeout(() => {
			this._resetTimerId = -1;

			if (timerToReset.paused) {
				return;
			}

			this.timerEndedId = null;
			timerToReset.reset();
		}, 1500) as any as number;

		if (this.settings.settings.switchTimersOnEnd) {
			if (this.effectiveTimer === this.timer) {
				this.effectiveTimer = this.breakTimer;
			} else {
				this.effectiveTimer = this.timer;
			}

			if (this.settings.settings.autoStartNextTimer) {
				this.effectiveTimer.start();
			}
		}
	}

	private watchTimer() {
		clearInterval(this._intervalId);

		this._intervalId = setInterval(() => {
			if (!this.timer.startedAt && !this.breakTimer.startedAt) {
				clearInterval(this._intervalId);
			}

			if (this.timer.elapsedTime === 0 || this.breakTimer.elapsedTime === 0) {
				this.handleTimerEnded();
			}
		}, 500) as any as number;
	}
}

export const pomodoroTimer = new _PomodoroTimer();
