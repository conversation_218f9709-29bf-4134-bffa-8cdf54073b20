import { PlaylistPlayer } from '$lib/util/MusicPlayer/PlaylistPlayer.svelte';
import { playlist } from '$lib/data/playlist';
import { shuffle } from '$lib/functions/shuffle';
import { youtubePlaylist } from '$lib/data/youtubePlaylist';
import { YouTubePlayer } from '$lib/util/MusicPlayer/YouTubePlayer.svelte';
import { MusicPlayer } from '$lib/util/MusicPlayer/MusicPlayer.svelte';
import { PlaylistPlayerAdapter } from '$lib/util/MusicPlayer/PlaylistPlayerAdapter';
import { YouTubePlayerAdapter } from '$lib/util/MusicPlayer/YouTubePlayerAdapter';

export const youTubePlayer = new YouTubePlayer(shuffle(youtubePlaylist));

export const playlistPlayer = new PlaylistPlayer(shuffle(playlist));

export const musicPlayer = new MusicPlayer({
	playlistAdapter: new PlaylistPlayerAdapter(playlistPlayer),
	youtubeAdapter: new YouTubePlayerAdapter(youTubePlayer),
});

let _startedOnFirstUserInteraction = $state(false);

export const startedOnFirstUserInteraction = {
	get value() {
		return _startedOnFirstUserInteraction;
	},
	set value(value: boolean) {
		_startedOnFirstUserInteraction = value;
	},
};
