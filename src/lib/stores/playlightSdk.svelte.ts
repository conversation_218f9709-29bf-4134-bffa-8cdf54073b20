import type PlaylightSDK from 'https://sdk.playlight.dev/playlight-sdk.es.js';

let _playlightSDK = $state<PlaylightSDK>();

function downloadAndInit() {
	import('https://sdk.playlight.dev/playlight-sdk.es.js').then((module: any) => {
		_playlightSDK = module.default as PlaylightSDK;

		_playlightSDK.init({
			exitIntent: {
				enabled: false,
			},
			button: {
				visible: false,
			},
		});
	});
}

export const Playlight = {
	get sdk() {
		return _playlightSDK;
	},
	downloadAndInit,
};
