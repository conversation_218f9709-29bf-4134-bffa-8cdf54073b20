import { browser } from '$app/environment';
import { SettingsManager } from '$lib/util/SettingsManager.svelte';

export type IslandInitialTab = 'stats' | 'leaderboard';

export const islandSettings = new SettingsManager({
	key: 'island',
	defaultSettings: {
		leaderboards: true,
		showLeaderboardsOnGameOver: true,
		stats: true,
		animatedStats: true,
		dailyGames: true,
		initialTab: 'stats' as IslandInitialTab,
	},
});

if (browser) {
	islandSettings.load();
}
