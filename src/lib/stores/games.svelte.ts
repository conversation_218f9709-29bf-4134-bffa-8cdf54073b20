import { untrack, type Component } from 'svelte';
import BreakoutCover from '../../routes/(games)/breakout/BreakoutCover.svelte';
import CheckersCover from '../../routes/(games)/checkers/CheckersCover.svelte';
import ColorMemoryCover from '../../routes/(games)/color-memory/ColorMemoryCover.svelte';
import DinosaurCover from '../../routes/(games)/dinosaur-game/DinosaurCover.svelte';
import FlappyBirdieCover from '../../routes/(games)/flappy-birdie/FlappyBirdieCover.svelte';
import MinesweeperCover from '../../routes/(games)/minesweeper/MinesweeperCover.svelte';
import SnakeCover from '../../routes/(games)/snake/SnakeCover.svelte';
import SolitaireCover from '../../routes/(games)/solitaire/SolitaireCover.svelte';
import SudokuCover from '../../routes/(games)/sudoku/SudokuCover.svelte';
import TentsCover from '../../routes/(games)/tents/TentsCover.svelte';
import TicTacToeCover from '../../routes/(games)/tic-tac-toe/TicTacToeCover.svelte';
import TileSlidePuzzleCover from '../../routes/(games)/tile-slide-puzzle/TileSlidePuzzleCover.svelte';
import WordSearchCover from '../../routes/(games)/word-search/WordSearchCover.svelte';
import WordleCover from '../../routes/(games)/wordle/WordleCover.svelte';
import _2048Cover from '../../routes/(games)/2048/2048Cover.svelte';
import { SettingsManager } from '$lib/util/SettingsManager.svelte';
import { siteSounds } from './siteSounds.svelte';

// TODO: Receive it on Context and derive formatted game name from list below
export type GameKey =
	| 'solitaire'
	| 'tile-slide-puzzle'
	| 'checkers'
	| 'tents'
	| 'breakout'
	| 'flappy-birdie'
	| '2048'
	| 'color-memory'
	| 'word-search'
	| 'minesweeper'
	| 'snake'
	| 'dinosaur'
	| 'wordle'
	| 'tic-tac-toe'
	| 'sudoku';

type GameInfo = {
	key: GameKey;
	name: string;
	url: string;
	cover: Component;
	coverType?: 'default' | 'full';
	tag?: string;
	daily?: boolean;
};

const favoritesSettings = new SettingsManager({
	key: 'favorites',
	defaultSettings: {
		games: [] as GameKey[],
	},
});

export const favoriteGames = {
	add(game: GameKey) {
		favoritesSettings.settings.games.push(game);
		siteSounds.addFavorite.play();
	},
	remove(game: GameKey) {
		favoritesSettings.settings.games = favoritesSettings.settings.games.filter(
			(key) => key !== game,
		);
		siteSounds.removeFavorite.play();
	},
	isFavorite(game: GameKey) {
		return favoritesSettings.settings.games.includes(game);
	},
	load() {
		favoritesSettings.load();
	},
};

export const games: GameInfo[] = [
	{
		key: 'solitaire',
		name: 'Solitaire',
		cover: SolitaireCover,
		url: '/solitaire',
		daily: true,
	},
	{
		key: 'tile-slide-puzzle',
		name: 'Tile Slide Puzzle',
		cover: TileSlidePuzzleCover,
		url: '/tile-slide-puzzle',
		daily: true,
	},
	{
		key: 'checkers',
		name: 'Checkers',
		cover: CheckersCover,
		url: '/checkers',
	},
	{
		key: 'tents',
		name: 'Tents',
		cover: TentsCover,
		url: '/tents',
		daily: true,
	},
	{
		key: 'breakout',
		name: 'Breakout',
		cover: BreakoutCover,
		url: '/breakout',
	},
	{
		key: 'flappy-birdie',
		name: 'Flappy Birdie',
		cover: FlappyBirdieCover,
		url: '/flappy-birdie',
	},
	{
		key: '2048',
		name: '2048',
		cover: _2048Cover,
		url: '/2048',
	},
	{
		key: 'color-memory',
		name: 'Color Memory',
		cover: ColorMemoryCover,
		url: '/color-memory',
		daily: true,
	},
	{
		key: 'word-search',
		name: 'Word Search',
		cover: WordSearchCover,
		url: '/word-search',
	},
	{
		key: 'minesweeper',
		name: 'Minesweeper',
		cover: MinesweeperCover,
		url: '/minesweeper',
	},
	{
		key: 'snake',
		name: 'Snake',
		cover: SnakeCover,
		url: '/snake',
	},
	{
		key: 'dinosaur',
		name: 'Dinosaur',
		cover: DinosaurCover,
		url: '/dinosaur-game',
	},
	{
		key: 'wordle',
		name: 'Wordle',
		cover: WordleCover,
		url: '/wordle',
	},
	{
		key: 'tic-tac-toe',
		name: 'Tic Tac Toe',
		cover: TicTacToeCover,
		url: '/tic-tac-toe',
	},
	{
		key: 'sudoku',
		name: 'Sudoku',
		cover: SudokuCover,
		url: '/sudoku',
		daily: true,
	},
];
