import { browser } from '$app/environment';
import type { BackgroundSoundResource } from '$lib/data/backgroundSounds.svelte';
import { Howl } from 'howler';

export type BackgroundSoundOptions = {
	resource: BackgroundSoundResource;
};

export class BackgroundSound {
	private howl: Howl | null = null;
	private src: string[];
	private playId: number | undefined;
	private _volume = $state(0);
	private _loaded = $state(false);
	private _muted = $state(false);
	private _isPlaying = $state(false);
	private _resource: BackgroundSoundResource;
	private _playOnLoad = false;

	constructor({ resource }: BackgroundSoundOptions) {
		this._resource = resource;
		this.src = typeof resource.url === 'string' ? [resource.url] : resource.url;
		BackgroundSound._instances.push(this);
		this.loadSettings();
		this.load();
	}

	get resource() {
		return this._resource;
	}

	private get _volumeLocalStorageKey() {
		return `background-${this.name}-volume`;
	}

	private get _mutedLocalStorageKey() {
		return `background-${this.name}-muted`;
	}

	get name() {
		return this.resource.attribution.work.name ?? '';
	}

	get loaded() {
		return this._loaded;
	}

	duration() {
		return this.howl?.duration();
	}

	state() {
		return this.howl?.state();
	}

	play(id?: number) {
		if (BackgroundSound.muted) {
			return -1;
		}

		if (this.volume === 0) {
			return;
		}

		if (this.state() === 'unloaded') {
			this._playOnLoad = true;
			this.howl?.load();
		}

		/** @see https://github.com/goldfire/howler.js/issues/1564 */
		this.playId = this.howl?.play(id);
		return this.playId;
	}

	loop(loop: boolean) {
		this.howl?.loop(loop);
	}

	stopAndPlay() {
		this.stop();
		this.play(this.playId);
	}

	playIfNotPlaying() {
		if (!this.isPlaying) {
			this.play();
		}
	}

	pause() {
		return this.howl?.pause();
	}

	stop() {
		return this.howl?.stop();
	}

	fade(from: number, to: number, duration: number) {
		return this.howl?.fade(from, to, duration);
	}

	get effectiveVolume() {
		return BackgroundSound.masterVolume * this._volume;
	}

	get volume() {
		return this._volume;
	}

	syncMasterVolume() {
		this.howl?.volume(this.effectiveVolume);
	}

	set volume(volume: number) {
		this._volume = Math.min(1, Math.max(0, volume));
		this.howl?.volume(this.effectiveVolume);

		getStorage()?.setItem(this._volumeLocalStorageKey, `${this._volume}`);

		if (this.effectiveVolume !== 0 && !this.isPlaying) {
			this.play();
		}
		if (this.effectiveVolume === 0) {
			this.pause();
		}
	}

	get muted() {
		return this._muted;
	}

	set muted(muted: boolean) {
		this._muted = muted;

		getStorage()?.setItem(this._mutedLocalStorageKey, `${muted}`);

		if (muted) {
			this.stop();
		} else {
			this.play();
		}
	}

	get unmuted() {
		return !this.muted;
	}

	set unmuted(unmuted: boolean) {
		this.muted = !unmuted;
	}

	get isPlaying() {
		return this._isPlaying;
	}

	loadSettings() {
		if (!browser) {
			return;
		}

		this._volume = +(getStorage()?.getItem(this._volumeLocalStorageKey) ?? 0);
		this._muted = getStorage()?.getItem(this._mutedLocalStorageKey) === 'true';
	}

	private load() {
		if (!BackgroundSound._instances.includes(this)) {
			BackgroundSound._instances.push(this);
		}

		if (this.howl === null) {
			this.howl = new Howl({
				src: this.src,
				/**
				 * Audio start is weird on safari when html5 is true and
				 * very small audios do not play
				 **/
				html5: false,
				preload: false, // lazy load the sound
				volume: this.effectiveVolume,
				loop: true,
				onload: () => {
					this._loaded = true;

					if (this._playOnLoad) {
						this.play();
					}
				},
				onplay: () => {
					this._isPlaying = true;
				},
				onpause: () => {
					this._isPlaying = false;
				},
				onstop: () => {
					this._isPlaying = false;
				},
			});
		}
	}

	unload() {
		BackgroundSound._instances = BackgroundSound._instances.filter((instance) => {
			return instance !== this;
		});

		this._loaded = false;

		return this.howl?.unload();
	}

	static _instances: BackgroundSound[] = [];
	static muted = false;
	static masterVolume = 0.3;
}

const getStorage = () => {
	if (browser) {
		return localStorage;
	}

	return null;
};

export class BackgroundSoundManager {
	readonly defaultVolume = 0.3;
	private readonly _mutedLocalStorageKey = 'background-sound-muted';
	private readonly _volumeLocalStorageKey = 'background-sound-volume';
	private _muted = $state(false);
	private _volume = $state(1);

	constructor() {
		this.loadSettings();
	}

	loadSettings() {
		if (!browser) {
			return;
		}

		this._volume = +(getStorage()?.getItem(this._volumeLocalStorageKey) ?? 0.5);
		this._muted = getStorage()?.getItem(this._mutedLocalStorageKey) === 'true';
		BackgroundSound.masterVolume = this._volume;
		BackgroundSound.muted = this._muted;
	}

	get volume() {
		return this._volume;
	}

	set volume(volume: number) {
		this._volume = Math.min(1, Math.max(0, volume));

		getStorage()?.setItem(this._volumeLocalStorageKey, `${this._volume}`);

		BackgroundSound.masterVolume = this._volume;
		BackgroundSound._instances.forEach((instance) => instance.syncMasterVolume());
	}

	get muted() {
		return this._muted;
	}

	set muted(muted: boolean) {
		this._muted = muted;
		getStorage()?.setItem(this._mutedLocalStorageKey, `${muted}`);

		BackgroundSound.muted = muted;

		if (muted) {
			BackgroundSound._instances.forEach((instance) => instance.stop());
		} else {
			this.play();
		}
	}

	get unmuted() {
		return !this.muted;
	}

	set unmuted(unmuted: boolean) {
		this.muted = !unmuted;
	}

	play() {
		BackgroundSound._instances.forEach((instance) => instance.play());
	}
}
