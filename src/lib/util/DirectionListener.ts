import type { Point2D } from '$lib/models/Point2D';

export type Direction = 'left' | 'right' | 'up' | 'down';

export type OnDirectionChange = (direction: Direction) => void;

export type DirectionListenerOptions = {
	emit?: 'once' | 'always';
	throttle?: number;
};

const defaultOptions: Required<DirectionListenerOptions> = {
	emit: 'always',
	throttle: 0,
};

export class DirectionListener {
	currentTouchPoint: Point2D = { x: 0, y: 0 };
	previousTouchPoint: Point2D = { x: 0, y: 0 };
	touchMoveThreshold = 15;
	options: Required<DirectionListenerOptions>;
	notifiedAt = 0;
	targetElement: HTMLElement;
	onDirectionChange: OnDirectionChange;
	private _disabled = false;
	private _disableTouch = false;
	private canHandleTouch = true;

	constructor(
		targetElement: HTMLElement,
		callback: OnDirectionChange,
		options?: DirectionListenerOptions,
	) {
		this.onDirectionChange = callback;
		this.targetElement = targetElement;
		this.options = {
			...defaultOptions,
			...options,
		};
	}

	private notifyListener(direction: Direction) {
		if (this.options.throttle > 0) {
			if (performance.now() - this.notifiedAt >= this.options.throttle) {
				this.notifiedAt = performance.now();
				this.onDirectionChange(direction);
			}
		} else {
			this.onDirectionChange(direction);
			this.notifiedAt = performance.now();
		}
	}

	get disabled() {
		return this._disabled;
	}

	set disabled(newDisabled: boolean) {
		this._disabled = newDisabled;

		this.dispose();
		this.listen();
	}

	get disabledTouch() {
		return this._disableTouch;
	}

	set disabledTouch(newDisabledTouch: boolean) {
		this._disableTouch = newDisabledTouch;

		this.dispose();
		this.listen();
	}

	handleKeydown = (event: KeyboardEvent) => {
		if (['INPUT', 'TEXTAREA', 'SELECT'].includes((event.target as HTMLElement).tagName)) {
			return;
		}

		const nextDirection = DirectionListener.getDirectionFromKeyboardEvent(event);

		if (nextDirection) {
			if (this.options.emit === 'once' && event.repeat) {
				return;
			}
			this.notifyListener(nextDirection);
			event.preventDefault();
		}
	};

	moveOnTouchDirection() {
		const dx = this.currentTouchPoint.x - this.previousTouchPoint.x;
		const dy = this.currentTouchPoint.y - this.previousTouchPoint.y;

		if (Math.abs(dx) < this.touchMoveThreshold && Math.abs(dy) < this.touchMoveThreshold) {
			return;
		}

		let nextDirection: Direction | undefined;

		if (Math.abs(dy) > Math.abs(dx)) {
			if (this.currentTouchPoint.y < this.previousTouchPoint.y) {
				nextDirection = 'up';
			} else {
				nextDirection = 'down';
			}
		} else {
			if (this.currentTouchPoint.x > this.previousTouchPoint.x) {
				nextDirection = 'right';
			} else {
				nextDirection = 'left';
			}
		}

		if (nextDirection) {
			this.notifyListener(nextDirection);
			this.previousTouchPoint = this.currentTouchPoint;

			if (this.options.emit === 'once') {
				this.canHandleTouch = false;
			}
		}
	}

	handleTouchMove = (event: TouchEvent) => {
		if (!this.canHandleTouch) {
			return;
		}

		this.currentTouchPoint = {
			x: event.changedTouches[0].screenX,
			y: event.changedTouches[0].screenY,
		};

		if (this.previousTouchPoint === undefined) {
			this.previousTouchPoint = this.currentTouchPoint;
		}

		this.moveOnTouchDirection();
	};

	handleTouchStart = (event: TouchEvent) => {
		this.currentTouchPoint = {
			x: event.changedTouches[0].screenX,
			y: event.changedTouches[0].screenY,
		};

		this.previousTouchPoint = this.currentTouchPoint;
		this.canHandleTouch = true;
	};

	restoreTouchHandler = () => {
		this.canHandleTouch = true;
	};

	listen() {
		if (this.disabled) {
			return;
		}

		window.addEventListener('keydown', this.handleKeydown);

		if (!this.disabledTouch) {
			this.targetElement.addEventListener('touchstart', this.handleTouchStart);
			this.targetElement.addEventListener('touchend', this.restoreTouchHandler);
			this.targetElement.addEventListener('touchcancel', this.restoreTouchHandler);
			this.targetElement.addEventListener('touchmove', this.handleTouchMove);
			this.targetElement.style.touchAction = 'pinch-zoom';
		}
	}

	dispose() {
		window.removeEventListener('keydown', this.handleKeydown);
		this.targetElement.removeEventListener('touchstart', this.handleTouchStart);
		this.targetElement.removeEventListener('touchend', this.restoreTouchHandler);
		this.targetElement.removeEventListener('touchcancel', this.restoreTouchHandler);
		this.targetElement.removeEventListener('touchmove', this.handleTouchMove);
	}

	static getDirectionFromKeyboardEvent(event: KeyboardEvent): Direction | null {
		const key = event.key.toLocaleLowerCase();

		if (key === 'w' || key === 'arrowup') {
			return 'up';
		} else if (key === 'a' || key === 'arrowleft') {
			return 'left';
		} else if (key === 's' || key === 'arrowdown') {
			return 'down';
		} else if (key === 'd' || key === 'arrowright') {
			return 'right';
		}

		return null;
	}
}
