import { browser } from '$app/environment';
import { Howl } from 'howler';

export type GameSoundOptions = {
	src: string | string[];
};

export class GameSound {
	private howl: Howl | null = null;
	private src: string[];
	private playId: number | undefined;
	private _isPlaying = $state(false);

	constructor({ src }: GameSoundOptions) {
		this.src = typeof src === 'string' ? [src] : src;
		GameSound._instances.push(this);
	}

	duration() {
		return this.howl?.duration();
	}

	state() {
		return this.howl?.state();
	}

	play(id?: number) {
		if (GameSound.muted) {
			return -1;
		}

		/** @see https://github.com/goldfire/howler.js/issues/1564 */
		this.playId = this.howl?.play(id);
		return this.playId;
	}

	loop(loop: boolean) {
		this.howl?.loop(loop);
	}

	stopAndPlay() {
		this.stop();
		this.play(this.playId);
	}

	playIfNotPlaying() {
		if (!this.playing()) {
			this.play();
		}
	}

	pause() {
		return this.howl?.pause();
	}

	stop() {
		return this.howl?.stop();
	}

	playing() {
		return this._isPlaying;
	}

	fade(from: number, to: number, duration: number) {
		return this.howl?.fade(from, to, duration);
	}

	volume(newVolume: number) {
		return this.howl?.volume(newVolume);
	}

	load() {
		if (!GameSound._instances.includes(this)) {
			GameSound._instances.push(this);
		}

		if (this.howl === null) {
			this.howl = new Howl({
				src: this.src,
				/**
				 * Audio start is weird on safari when html5 is true and
				 * very small audios do not play
				 **/
				html5: false,
				preload: true,
				volume: GameSound.volume,
			});

			this.howl.on('play', () => {
				this._isPlaying = true;
			});

			this.howl.on('pause', () => {
				this._isPlaying = false;
			});

			this.howl.on('stop', () => {
				this._isPlaying = false;
			});

			this.howl.on('end', () => {
				this._isPlaying = false;
			});
		}
	}

	unload() {
		GameSound._instances = GameSound._instances.filter((instance) => {
			return instance !== this;
		});
		return this.howl?.unload();
	}

	static _instances: GameSound[] = [];
	static volume = 1;
	static muted = false;
}

const getStorage = () => {
	if (browser) {
		return localStorage;
	}

	return null;
};

export class GameSoundManager {
	readonly defaultVolume = 0.7;
	private readonly _mutedLocalStorageKey = 'game-muted';
	private readonly _volumeLocalStorageKey = 'game-volume';

	private _volume = $state(1);
	private _muted = $state(false);

	constructor() {
		this.loadSettings();
	}

	loadSettings() {
		if (!browser) {
			return;
		}

		this._volume = +(getStorage()?.getItem(this._volumeLocalStorageKey) ?? 1);
		this._muted = getStorage()?.getItem(this._mutedLocalStorageKey) === 'true';
		GameSound.volume = this._volume;
		GameSound.muted = this._muted;
	}

	get volume() {
		return this._volume;
	}

	set volume(volume: number) {
		this._volume = Math.min(1, Math.max(0, volume));

		getStorage()?.setItem(this._volumeLocalStorageKey, `${this._volume}`);

		GameSound.volume = this._volume;
		GameSound._instances.forEach((instance) => instance.volume(this._volume));
	}

	get muted() {
		return this._muted;
	}

	mute(muted: boolean) {
		this._muted = muted;
		getStorage()?.setItem(this._mutedLocalStorageKey, `${muted}`);

		if (muted) {
			GameSound._instances.forEach((instance) => instance.stop());
		}

		GameSound.muted = muted;
	}

	get unmuted() {
		return !this.muted;
	}

	set unmuted(unmuted: boolean) {
		this.mute(!unmuted);
	}
}
