const characters = [
	'qpofhnamzu',
	'pitujgdxbm',
	'0697845312',
	'nvbzxjklgs',
	'quotyurgsa',
	'1973450286',
	'mpubctesqw',
	'ybcueazsdx',
	'fhikjrlvwc',
	'8095763421',
];

const stringSize = characters[0].length;

/** Generates an 10-sized string from a number */
function generateRandomStringFromNumber(num: number): string {
	const digits = `${num}`
		.padStart(stringSize)
		.split('')
		.map((char) => +char);

	return Array(stringSize)
		.fill(0)
		.map((_, i) => {
			const digit = digits[i];
			const char = characters[i][digit];

			return char;
		})
		.join('');
}

function getNumberFromRandomString(randomString: string): number | null {
	const numberAsString = Array(stringSize)
		.fill(0)
		.map((_, i) => {
			const char = randomString[i];
			const digit = characters[i].indexOf(char);

			return digit;
		})
		.join('');

	if (numberAsString.length !== stringSize) {
		return null;
	}

	return +numberAsString;
}

export const DeterministicIdGenerator = {
	generateRandomStringFromNumber,
	getNumberFromRandomString,
};
