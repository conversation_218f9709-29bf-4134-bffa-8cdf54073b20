import { browser } from '$app/environment';
import cloneDeep from 'lodash/cloneDeep';
import { handleError } from '../../hooks.client';

export interface SettingsManagerProps<Data extends Record<string, any>> {
	key: string;
	defaultSettings: Data;
}

export class SettingsManager<Data extends Record<string, any>> {
	private key: string;
	private _ready = $state(false);
	private cleanUpListener?: () => void;
	readonly defaultSettings: Data;
	settings = $state<Data>({} as Data);
	hasError = $state(false);

	constructor({ key, defaultSettings }: SettingsManagerProps<Data>) {
		this.key = key;
		this.defaultSettings = defaultSettings;
		this.settings = cloneDeep(defaultSettings);

		this.cleanUpListener = $effect.root(() => {
			$effect(() => {
				if (this.settings) {
					this.save();
				}
			});
		});
	}

	get ready() {
		return this._ready;
	}

	load() {
		if (browser && !this._ready) {
			this.hasError = false;

			try {
				const item = localStorage.getItem(`${this.key}-settings`);
				const itemObject = item ? JSON.parse(item) : {};
				this.settings = {
					...this.defaultSettings,
					...itemObject,
				};

				this._ready = true;
			} catch (error: any) {
				this.hasError = true;
				handleError(error);
			}
		}
	}

	save() {
		if (!this._ready) {
			return;
		}

		this.hasError = false;

		try {
			localStorage.setItem(`${this.key}-settings`, JSON.stringify(this.settings));
		} catch (error: any) {
			this.hasError = true;
			handleError(error);
		}
	}

	dispose() {
		this.cleanUpListener?.();
	}
}
