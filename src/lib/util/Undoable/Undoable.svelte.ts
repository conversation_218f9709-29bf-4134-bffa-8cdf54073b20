type Timeline<State> = {
	past: State[];
	present: State;
	future: State[];
};

export class Undoable<State> {
	timeline = $state.raw() as Timeline<State>;

	constructor(initialValue: State) {
		this.timeline = {
			past: [],
			present: initialValue,
			future: [],
		};
	}

	get state() {
		return this.timeline.present;
	}

	add(state: State) {
		const { past, present } = this.timeline;

		this.timeline = {
			past: [...past, present],
			present: state,
			future: [],
		};
	}

	undo() {
		if (!this.canUndo()) {
			return;
		}

		const { past, present, future } = this.timeline;

		this.timeline = {
			past: past.slice(0, past.length - 1),
			present: past[past.length - 1],
			future: [present, ...future],
		};
	}

	redo() {
		if (!this.canRedo()) {
			return;
		}
		const { past, present, future } = this.timeline;

		this.timeline = {
			past: [...past, present],
			present: future[0],
			future: future.slice(1),
		};
	}

	replace(state: State) {
		const { past, future } = this.timeline;

		this.timeline = {
			past,
			present: state,
			future,
		};
	}

	reset(state?: State) {
		const { past, present } = this.timeline;

		this.timeline = {
			past: [],
			present: state ?? past[0] ?? present,
			future: [],
		};
	}

	canUndo() {
		return this.timeline.past.length > 0;
	}

	canRedo() {
		return this.timeline.future.length > 0;
	}
}
