import type { Point2D } from '$lib/models/Point2D';

export type Rect = {
	x: number;
	y: number;
	w: number;
	h: number;
};

export type Line = {
	start: Point2D;
	end: Point2D;
};

export type Side = 'left' | 'right' | 'top' | 'bottom';

export type SideCollision = {
	side: Side;
	amount: number;
};

export class HitBox {
	rect: Rect;
	enabled = true;
	color: string;
	position: Point2D;
	responsive: boolean;

	constructor(
		rect: Rect = {
			x: 0,
			y: 0,
			w: 0,
			h: 0,
		},
		position: Point2D = { x: 0, y: 0 },
		responsive = false,
		color = '#ff0000',
	) {
		this.rect = rect;
		this.color = color;
		this.position = position;
		this.responsive = responsive;
	}

	// getCollision(other: Rect): SideCollision | null {
	// 	return null;
	// }

	get scale() {
		if (this.responsive) {
			return window.devicePixelRatio;
		}

		return 1;
	}

	get x() {
		return this.rect.x * this.scale + this.position.x;
	}

	get w() {
		return this.rect.w * this.scale;
	}

	get h() {
		return this.rect.h * this.scale;
	}

	get y() {
		return this.rect.y * this.scale + this.position.y;
	}

	get top() {
		return this.y;
	}

	get bottom() {
		return this.y + this.h;
	}

	get left() {
		return this.x;
	}

	get right() {
		return this.x + this.w;
	}

	get centerX() {
		return this.x + this.w / 2;
	}

	get centerY() {
		return this.y + this.h / 2;
	}

	collidesWith(other: Rect | HitBox) {
		if (!this.enabled) {
			return false;
		}

		return (
			this.left < other.x + other.w &&
			this.right > other.x &&
			this.top < other.y + other.h &&
			this.bottom > other.y
		);
	}

	// getCollisionPoint(line: Line): Point2D | null {
	// 	// TODO: Implement
	// 	return null;
	// }

	draw(context: CanvasRenderingContext2D) {
		context.save();
		context.globalAlpha = 0.8;
		context.lineWidth = window.devicePixelRatio;
		context.strokeStyle = this.color;
		context.translate(this.position.x, this.position.y);
		context.scale(this.scale, this.scale);
		context.strokeRect(this.rect.x, this.rect.y, this.rect.w, this.rect.h);
		context.restore();
	}
}
