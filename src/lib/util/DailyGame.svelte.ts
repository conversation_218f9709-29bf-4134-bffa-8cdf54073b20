import { goto } from '$app/navigation';
import { page } from '$app/state';
import { db } from '$lib/db/db';
import { handleError } from '../../hooks.client';
import { isNumeric } from '$lib/functions/isNumeric';
import { getDateDifferenceInDays } from '$lib/functions/date/getDateDifferenceInDays';
import { formatDateToYearMonthDay } from '$lib/functions/date/formatDateToYearMonthDay';

export interface DailyGameProps<EncodedGame> {
	type: 'seed' | 'fetch';
	game: string;
	gameVariant?: string;
	firstAvailableGameDate: Date;
	fetchGame?: (date: Date) => Promise<EncodedGame>;
	onError: () => void;
	onPlay: (seedOrEncoded: EncodedGame) => void;
}

const dailyGameCache: Record<string, any> = {};

export class DailyGame<EncodedGame = number> {
	readonly game: string;
	readonly gameVariant: string;
	readonly firstAvailableGameDate: Date;
	isFetching = $state(false);
	/** Played days in yyyy/mm/dd format */
	playedDays: string[] = $state([]);
	mustRefreshPageDueToError = $state(false);
	error: Error | undefined = $state();
	private readonly type: 'seed' | 'fetch';
	private _dateOfLastSuccessfullyPlayedGame: Date | null = null;
	private _fetchGame?: (date: Date) => Promise<EncodedGame>;
	private onPlay: ((encoded: EncodedGame) => void) | ((hash: number) => void);
	private onError: () => void;

	constructor(props: DailyGameProps<EncodedGame>) {
		this.game = props.game;
		this.gameVariant = props.gameVariant ?? '';
		this.firstAvailableGameDate = new Date(
			props.firstAvailableGameDate.getFullYear(),
			props.firstAvailableGameDate.getMonth(),
			props.firstAvailableGameDate.getDate(),
		);
		this.type = props.type;
		this._fetchGame = (props as DailyGameProps<EncodedGame>).fetchGame;
		this.onPlay = props.onPlay;
		this.onError = props.onError;
	}

	get dateOfLastSuccessfullyPlayedGame() {
		return this._dateOfLastSuccessfullyPlayedGame;
	}

	private getSeed(date: Date) {
		const days = Math.floor(
			(date.getTime() - this.firstAvailableGameDate.getTime()) / (24 * 60 * 60 * 1000),
		);

		// Give 1000 units of seed space so algorithms have plenty of room to
		// increment the seed
		return days * 1000;
	}

	static getFormattedDate(date: Date) {
		return formatDateToYearMonthDay(date);
	}

	private getFormattedDateSearchParam(date: Date) {
		return DailyGame.getFormattedDate(date).split('/').join('-');
	}

	private getCacheKey(date: Date) {
		return `${this.type === 'seed' ? 'seed' : this.game}-${DailyGame.getFormattedDate(date)}`;
	}

	async fetchAndPlay(date: Date): Promise<void> {
		try {
			const now = new Date();
			const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
			date = new Date(date.getFullYear(), date.getMonth(), date.getDate());

			if (getDateDifferenceInDays(date, today) > 3) {
				throw new Error('Cannot fetch daily game in the future');
			}

			if (date < this.firstAvailableGameDate) {
				throw new Error('Cannot fetch daily game before the first available date');
			}

			this.isFetching = true;
			const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());
			const cacheKey = this.getCacheKey(date);

			const game =
				dailyGameCache[cacheKey] ??
				(this._fetchGame ? await this._fetchGame(startOfDay) : this.getSeed(startOfDay));

			dailyGameCache[cacheKey] = game;
			this._dateOfLastSuccessfullyPlayedGame = startOfDay;
			this.isFetching = false;
			this.updateUrlWithDailyGame(date);

			return this.onPlay(game as any);
		} catch (error: any) {
			this.error = error;
			this.onError();
		} finally {
			this.isFetching = false;
		}
	}

	async markAsPlayed(date = this._dateOfLastSuccessfullyPlayedGame) {
		if (!date) {
			return;
		}

		const formattedDate = DailyGame.getFormattedDate(date);

		if (!this.playedDays.includes(formattedDate)) {
			this.playedDays.push(formattedDate);
		}

		return this.save();
	}

	async load() {
		try {
			const record = await db.dailyGame
				.where({
					game: this.game,
					gameVariant: this.gameVariant,
				})
				.first();

			if (record) {
				this.playedDays = record.playedDays;
			}
		} catch (error: any) {
			this.mustRefreshPageDueToError = true;
			handleError(error);
		}
	}

	async save() {
		try {
			await db.dailyGame.put({
				game: this.game,
				gameVariant: this.gameVariant,
				playedDays: $state.snapshot(this.playedDays),
			});
		} catch (error: any) {
			this.mustRefreshPageDueToError = true;
			handleError(error);
		}
	}

	private updateUrlWithDailyGame(date: Date) {
		page.url.searchParams.set('daily', this.getFormattedDateSearchParam(date));

		goto(page.url.toString().replace(/=(?=&|$)/gm, ''), { replaceState: true });
	}

	playDailyGameFromUrl() {
		const dailyParam = page.url.searchParams.get('daily');

		if (dailyParam !== null) {
			try {
				const date = DailyGame.getDateFromUrl()!;

				this.fetchAndPlay(date);
			} catch (_) {
				this.mustRefreshPageDueToError = true;
				// Ignore
			}
		}
	}

	urlHasDailyGame() {
		try {
			const date = DailyGame.getDateFromUrl();

			return !!date;
		} catch (_) {
			return false;
		}
	}

	private static getDateFromUrl() {
		const dailyParam = page.url.searchParams.get('daily');

		if (dailyParam !== null) {
			let date = new Date();
			const [year, month, day] = dailyParam.split('-');

			if (isNumeric(year) && isNumeric(month) && isNumeric(day)) {
				date = new Date(+year, +month - 1, +day);
			}

			return date;
		}
	}
}
