import { authClient } from '$lib/auth/client';
import { formatDateToYearMonthDay } from '$lib/functions/date/formatDateToYearMonthDay';
import { getDateDifferenceInDays } from '$lib/functions/date/getDateDifferenceInDays';
import { Cache } from './Cache';

export interface LeaderboardRecord {
	name: string;
	image: string;
	score: number;
	time: number;
	moves: number;
	rank: number;
}

export interface LeaderboardData {
	records: LeaderboardRecord[];
	user: LeaderboardRecord | null;
}

export type LeaderboardRange = 'top-20' | 'around-player';

export type LeaderboardOrder = 'lower-first' | 'higher-first';

interface LeaderboardProps {
	game: string;
	gameVariant?: string;
	range?: LeaderboardRange;
	firstAvailableDate: Date;
	/** @default false */
	hasMoves?: boolean;
	order: LeaderboardOrder;
}

const leaderboardCache = new Cache<LeaderboardData>({ defaultDuration: 60_000 });
export const maxLeaderboardDays = 30;

export class Leaderboard {
	private _game: string;
	private _gameVariant: string;
	private _board: LeaderboardData = $state({ records: [], user: null });
	private _range: LeaderboardRange;
	private _loadState: 'loading' | 'success' | 'error' | 'idle' = $state('idle');
	private _date = $state(new Date());
	private _firstAvailableDate: Date;
	private _abortController: AbortController | null = null;
	private _hasMoves = false;
	private _order: LeaderboardOrder;
	private _sendSuccessAt = $state<Date>();
	private _lastPlayerRank = $state<number | null>(null);

	constructor({
		game,
		range = 'around-player',
		gameVariant = 'default',
		firstAvailableDate,
		hasMoves = false,
		order,
	}: LeaderboardProps) {
		this._game = game;
		this._range = range;
		this._firstAvailableDate = firstAvailableDate;
		this._hasMoves = hasMoves;
		this._gameVariant = gameVariant;
		this._order = order;

		const url = this.getUrl({ date: new Date(), range: this._range });

		const cached = leaderboardCache.get(url);

		if (cached) {
			const rank = cached.value.user?.rank;

			if (rank !== undefined && rank !== null) {
				this._lastPlayerRank = rank;
			}
		}
	}

	get sendSuccessAt() {
		return this._sendSuccessAt;
	}

	get order() {
		return this._order;
	}

	get gameVariant() {
		return this._gameVariant;
	}

	get game() {
		return this._game;
	}

	get hasMoves() {
		return this._hasMoves;
	}

	get range() {
		return this._range;
	}

	get board() {
		return this._board;
	}

	get loadState() {
		return this._loadState;
	}

	get date() {
		return this._date;
	}

	private get _isLoggedIn() {
		const session = authClient.useSession();

		return !!session.get().data?.user;
	}

	set date(newDate: Date) {
		this._date = newDate;
		this.load();
	}

	get firstAvailableDate() {
		const today = new Date();

		if (getDateDifferenceInDays(today, this._firstAvailableDate) > 30) {
			const thirtyDaysAgo = new Date(
				today.getFullYear(),
				today.getMonth(),
				today.getDate() - maxLeaderboardDays,
			);
			return thirtyDaysAgo;
		}

		if (this._firstAvailableDate > today) {
			return today;
		}

		return this._firstAvailableDate;
	}

	get wasPlayerRankUpdated() {
		const user = this._board.user;

		if (user) {
			return user.rank !== this._lastPlayerRank;
		}

		return false;
	}

	mockBoard() {
		const playerPlace = Math.floor(0 + Math.random() * 40);
		const playerRecord = {
			rank: playerPlace,
			score: 12312,
			time: 1230312,
			moves: 123,
			name: 'Bruno Alves',
			image: '',
		};
		this._board = {
			user: playerRecord,
			records:
				this._range === 'around-player'
					? [
							{
								rank: playerPlace - 1,
								score: 34534,
								time: 34511,
								moves: 123,
								name: 'player-above',
								image: '',
							},
							playerRecord,
							{
								rank: playerPlace + 1,
								score: 5674,
								time: 567,
								moves: 123,
								name: 'player-below',
								image: '',
							},
						]
					: Array.from({ length: 20 })
							.fill(0)
							.map((_, rank) => {
								return {
									rank: rank,
									score: rank * 10,
									time: rank * 1e5,
									moves: rank * 10,
									name: `Player ${rank} with a very large name`,
									image: '',
								};
							}),
		};
		this._loadState = 'success';
	}

	get url() {
		return this.getUrl({
			date: this._date,
			range: this.range,
			order: this.order,
		});
	}

	private getUrl({
		date,
		range,
		order = this._order,
	}: {
		date: Date;
		range?: LeaderboardRange;
		order?: LeaderboardOrder;
	}): string {
		const day = formatDateToYearMonthDay(date, '-');

		const url = new URL(
			`${import.meta.env.VITE_API_URL}/leaderboard/game/${this._game}/variant/${this._gameVariant}/day/${day}`,
		);

		url.searchParams.append('order', order);

		if (range) {
			url.searchParams.append('range', range);
		}

		return url.toString();
	}

	async load(date = this._date) {
		if (!this._isLoggedIn) {
			return;
		}

		if (this._board.user) {
			this._lastPlayerRank = this._board.user.rank;
		}

		this._abortController?.abort();
		this._abortController = new AbortController();
		this._date = date;

		const url = this.getUrl({ date, range: this._range });

		const cachedBoard = leaderboardCache.get(url);

		if (cachedBoard) {
			this._board = cachedBoard.value;
			this._loadState = 'success';
			return cachedBoard;
		}

		this._loadState = 'loading';

		try {
			const res = await fetch(url, {
				credentials: 'include',
				signal: this._abortController.signal,
			});

			if (res.status >= 300) {
				throw new Error('Error loading leaderboard');
			}

			const board = (await res.json()) as LeaderboardData;

			leaderboardCache.set({
				key: url,
				value: board,
			});

			this._board = board;

			this._loadState = 'success';
		} catch (_) {
			this._loadState = 'error';
		} finally {
			this._abortController = null;
		}
	}

	async sendNewPlayerScore({
		score,
		moves,
		time,
	}: {
		score?: number | null;
		moves?: number;
		/** time in ms */
		time: number;
	}): Promise<boolean> {
		if (!this._isLoggedIn) {
			return false;
		}

		if ([undefined, null, Infinity, -Infinity].includes(score)) {
			return false;
		}

		if (this._board.user) {
			this._lastPlayerRank = this._board.user.rank;
		}

		this._loadState = 'loading';

		const date = new Date();

		try {
			const res = await fetch(this.getUrl({ date }), {
				method: 'PUT',
				credentials: 'include',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({ score, moves, time }),
			});

			if (res.status >= 300) {
				throw new Error('Failed to send player score');
			}

			const aroundPlayerBoard = (await res.json()) as LeaderboardData;

			leaderboardCache.invalidate(this.getUrl({ date, range: 'top-20', order: this._order }));

			const aroundPlayerUrl = this.getUrl({ date, range: 'around-player', order: this._order });
			leaderboardCache.set({
				key: aroundPlayerUrl,
				value: aroundPlayerBoard,
			});

			if (this._range === 'around-player') {
				this._board = aroundPlayerBoard;
			}

			this._loadState = 'success';
			this._sendSuccessAt = new Date();
		} catch (_) {
			this._loadState = 'error';

			return false;
		}

		return true;
	}

	clone(props?: { range?: LeaderboardRange }) {
		const variant = props?.range ?? this.range;

		return new Leaderboard({
			game: this._game,
			firstAvailableDate: this.firstAvailableDate,
			gameVariant: this.gameVariant,
			hasMoves: this.hasMoves,
			range: variant,
			order: this._order,
		});
	}
}
