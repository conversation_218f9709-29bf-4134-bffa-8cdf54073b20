import type { PlaylistSong } from '$lib/models/Playlist';

export interface MusicPlayerAdapter {
	play: () => void;
	pause: () => void;
	next: () => void;
	previous: () => void;
	resetVolume: () => void;
	dispose: () => void;
	restore: () => void;
	/** From 0 to 1 */
	get volume(): number;
	set volume(newVolume: number);
	get isPlaying(): boolean;
	get isReady(): boolean;
	get currentSong(): PlaylistSong | undefined;
}
