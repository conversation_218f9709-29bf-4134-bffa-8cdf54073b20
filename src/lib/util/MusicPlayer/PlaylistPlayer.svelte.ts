import type { Playlist, PlaylistSong } from '$lib/models/Playlist';
import { Howl } from 'howler';
import { browser } from '$app/environment';
import { SvelteSet } from 'svelte/reactivity';
import { canUseHTML5Audio } from '$lib/functions/canUseHTML5Audio';

const getStorage = () => {
	if (browser) {
		return localStorage;
	}

	return null;
};

export class PlaylistPlayer {
	private _playlist!: Playlist;
	private _isPlaying = $state(false);
	private _currentSongIndex = $state(-1);
	private _howl!: Howl;
	private readonly _volumeLocalStorageKey = 'music-volume';
	private readonly _excludedSongsLocalStorageKey = 'music-excluded';
	private readonly _defaultVolume = 0.3;
	private _volume = $state(0);
	private _excludedSongs = new SvelteSet<string>();

	constructor(playlist: Playlist) {
		this._playlist = playlist;
		this._volume = +(getStorage()?.getItem(this._volumeLocalStorageKey) ?? this._defaultVolume);
		this._howl = this._createHowl(playlist[0].url);

		(getStorage()?.getItem(this._excludedSongsLocalStorageKey)?.split(',') ?? []).forEach((id) =>
			this._excludedSongs.add(id),
		);

		if (browser) {
			let index = 0;
			let song = this._playlist[index];

			while (song && this._excludedSongs.has(this.getSongId(song))) {
				index += 1;
				song = this._playlist[index];
			}

			if (song) {
				this._currentSongIndex = index;
			}
		}
	}

	get playlist() {
		return this._playlist;
	}

	get currentSong(): PlaylistSong | undefined {
		return this._playlist[this.currentSongIndex];
	}

	get isPlaying() {
		return this._isPlaying;
	}

	isSongExcluded(song: PlaylistSong) {
		return this._excludedSongs.has(this.getSongId(song));
	}

	excludeSong(song: PlaylistSong) {
		this._excludedSongs.add(this.getSongId(song));
		this.storeExcludedSongs();

		if (this.currentSong && this.getSongId(this.currentSong) === this.getSongId(song)) {
			this.next();
		}
	}

	toggleSongExclusion(song: PlaylistSong) {
		if (this.isSongExcluded(song)) {
			this.includeSong(song);
		} else {
			this.excludeSong(song);
		}
	}

	includeSong(song: PlaylistSong) {
		this._excludedSongs.delete(this.getSongId(song));
		this.storeExcludedSongs();

		if (!this.currentSong) {
			this.currentSongIndex = this.playlist.findIndex(
				(s) => this.getSongId(s) === this.getSongId(song),
			);
			this.play();
		}
	}

	get excludedSongs() {
		return this._excludedSongs;
	}

	private storeExcludedSongs() {
		getStorage()?.setItem(
			this._excludedSongsLocalStorageKey,
			Array.from(this._excludedSongs.values()).join(','),
		);
	}

	private set isPlaying(newIsPlaying: boolean) {
		const howler = this._howl;

		if (!howler) {
			return;
		}

		this._isPlaying = newIsPlaying;

		if (this._isPlaying) {
			if (howler.state() === 'unloaded') {
				howler.load();
			}
			howler.play();
		} else {
			howler.pause();
		}
	}

	get currentSongIndex() {
		return this._currentSongIndex;
	}

	set currentSongIndex(newIndex: number) {
		let newIndexToUse = newIndex;

		if (newIndex >= this._playlist.length) {
			newIndexToUse = 0;
		} else if (newIndex < 0) {
			newIndexToUse = this._playlist.length - 1;
		}

		const nextSong = this._playlist[newIndexToUse];

		if (this._excludedSongs.has(this.getSongId(nextSong))) {
			this.pause();
		} else {
			this._howl?.stop();
			this._howl.unload();
			this._howl = this._createHowl(this._playlist[newIndexToUse].url);
			this._currentSongIndex = newIndexToUse;
			this.isPlaying = true;
		}
	}

	get volume() {
		return this._volume;
	}

	set volume(newVolume: number) {
		this._volume = Math.min(1, Math.max(0, newVolume));
		this._howl.volume(this._volume);

		getStorage()?.setItem(this._volumeLocalStorageKey, `${this._volume}`);

		if (this._volume === 0) {
			this.pause();
		}
	}

	play() {
		if (this.isPlaying) {
			return;
		}

		if (!this.currentSong || this.excludedSongs.has(this.getSongId(this.currentSong))) {
			return;
		}

		if (this._volume === 0) {
			this.resetVolume();
		}

		this.isPlaying = true;
	}

	resetVolume() {
		this.volume = this._defaultVolume;
	}

	pause() {
		this.isPlaying = false;
	}

	togglePlay() {
		if (this.isPlaying) {
			this.pause();
		} else {
			this.play();
		}
	}

	next() {
		let currentIndex = Math.max(0, this._currentSongIndex);
		let nextIndex = currentIndex;

		do {
			nextIndex += 1;

			if (nextIndex >= this._playlist.length) {
				nextIndex = 0;
			}
		} while (
			nextIndex !== currentIndex && // performed a full search
			this._excludedSongs.has(this.getSongId(this._playlist[nextIndex]))
		);

		if (this._excludedSongs.has(this.getSongId(this.playlist[nextIndex]))) {
			this.pause();
			this._currentSongIndex = -1;
			return;
		}

		this.currentSongIndex = nextIndex;
	}

	previous() {
		let currentIndex = Math.max(0, this._currentSongIndex);
		let nextIndex = currentIndex;

		do {
			nextIndex -= 1;

			if (nextIndex < 0) {
				nextIndex = this._playlist.length - 1;
			}
		} while (
			nextIndex !== currentIndex && // performed a full search
			this._excludedSongs.has(this.getSongId(this._playlist[nextIndex]))
		);

		if (this._excludedSongs.has(this.getSongId(this.playlist[nextIndex]))) {
			this.pause();
			this._currentSongIndex = -1;
			return;
		}

		this.currentSongIndex = nextIndex;
	}

	dispose() {
		this._howl?.unload();
	}

	private _createHowl(src: string) {
		const canUseHtml5 = canUseHTML5Audio();

		const howl = new Howl({
			volume: this._volume,
			src,
			preload: false,
			/**
			 * If set to true, the volume stop working on Safari and
			 * do not work at all on Firefox
			 */
			html5: canUseHtml5,
			onend: () => {
				this.next();
			},
			onload: () => {
				if (!canUseHtml5) {
					return;
				}

				/** @see https://github.com/goldfire/howler.js/issues/1262#issuecomment-576708079 */
				const sounds = (howl as any)._sounds;
				const node = sounds[0]?._node;

				if (node) {
					node.onpause = function () {
						// find sound by node
						var s, sound;
						for (s = 0; s < sounds.length; s++) {
							if (sounds[s]._node === (this as any)) {
								sound = sounds[s];
							}
						}
						if (!sound) return;
						sound._paused = true;
						/* don't do the next thing if this is a live stream!
						 * otherwise howler will fire an end event */
						sound._seek = this.currentTime;
					}.bind(node);
				}
			},
		});

		/** @see https://github.com/goldfire/howler.js/issues/1262#issuecomment-576708079 */

		return howl as Howl;
	}

	private getSongId(song: PlaylistSong): string {
		return song.url;
	}
}
