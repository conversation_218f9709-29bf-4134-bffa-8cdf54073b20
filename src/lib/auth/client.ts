import { stripeClient } from '@better-auth/stripe/client';
import { adminClient, usernameClient } from 'better-auth/client/plugins';
import { createAuthClient } from 'better-auth/svelte';

export const authClient = createAuthClient({
	baseURL: import.meta.env.VITE_BETTER_AUTH_URL,
	basePath: '/auth',
	plugins: [
		usernameClient(),
		adminClient(),
		stripeClient({
			subscription: true,
		}),
	],
});

export type Session = typeof authClient.$Infer.Session;
export type User = Session['user'];
