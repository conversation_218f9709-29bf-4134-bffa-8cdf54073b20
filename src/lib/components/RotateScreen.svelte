<script lang="ts">
	import type { Snippet } from 'svelte';
	import PhoneRotateLandscape from './Icons/PhoneRotateLandscape.svelte';

	interface Props {
		class?: string;
		children?: Snippet;
	}

	let { class: classFromProps = '', children }: Props = $props();
</script>

<div
	class="flex-center absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 flex-col gap-4 text-lg lg:hidden portrait:hidden"
>
	<PhoneRotateLandscape width="64" height="64" />

	<span>Rotate your device </span>
</div>

<div class="hidden w-full lg:flex portrait:flex {classFromProps}">
	{@render children?.()}
</div>
