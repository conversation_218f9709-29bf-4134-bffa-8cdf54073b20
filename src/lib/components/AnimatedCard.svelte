<script lang="ts">
	import { wait } from '$lib/functions/wait';
	import { cn } from '$lib/util/cn';
	import { onDestroy, type Snippet } from 'svelte';

	interface Props {
		class?: string;
		cardClass?: string;
		noZoomAnimation?: boolean;
		noHoverAnimation?: boolean;
		children: Snippet;
		onclick?: () => void;
	}

	let {
		class: classFromProps,
		cardClass,
		noZoomAnimation = false,
		noHoverAnimation = false,
		children,
		onclick,
	}: Props = $props();
	let card: HTMLButtonElement | null = $state(null);
	let animatingZoom = false;

	function reset() {
		card?.style?.setProperty('--transitionDuration', '300ms');
		card?.style?.setProperty('--glare-opacity', '0');
		card?.style?.setProperty('--rotateX', '0');
		card?.style?.setProperty('--rotateY', '0');
		card?.style?.setProperty('--rotateZ', '0');
		card?.style?.setProperty('--scale', '1');
		card?.style?.setProperty('--opacity', '1');
		card?.style?.removeProperty('z-index');
	}

	function handlePointerMove(event: PointerEvent) {
		if (animatingZoom || event.pointerType === 'touch' || noHoverAnimation) {
			return;
		}

		const rect = (event.currentTarget as HTMLElement)?.getBoundingClientRect();

		if (rect) {
			const x = event.clientX - rect.left;
			const y = event.clientY - rect.top;

			const xPercentage = 2 * (1 - (rect.width - x) / rect.width) - 1;
			const yPercentage = 2 * (1 - (rect.height - y) / rect.height) - 1;

			card?.style?.setProperty('--rotateX', `${yPercentage * 15}deg`);
			card?.style?.setProperty('--rotateY', `${-xPercentage * 15}deg`);

			card?.style?.setProperty('--mouseX', `${x}px`);
			card?.style?.setProperty('--mouseY', `${y}px`);
		}
	}

	function handlePointerOut(event: PointerEvent) {
		if (animatingZoom || event.pointerType === 'touch' || noHoverAnimation) {
			return;
		}

		reset();
	}

	function handleBlur() {
		if (animatingZoom) {
			return;
		}

		reset();
	}

	function handlePointerEnter(event: PointerEvent) {
		if (animatingZoom || event.pointerType === 'touch' || noHoverAnimation) {
			return;
		}

		card?.style?.setProperty('--glare-opacity', '0.2');
		card?.style?.setProperty('--transitionDuration', '300ms');

		setTimeout(() => {
			card?.style?.setProperty('--transitionDuration', '0ms');
		}, 200);
	}

	async function handleClick() {
		onclick?.();

		if (noZoomAnimation) {
			return;
		}

		animatingZoom = true;
		reset();
		card?.style?.setProperty('--transitionDuration', '500ms');
		card?.style?.setProperty('--scale', '2');
		card?.style?.setProperty('z-index', '99');

		await wait(50);

		card?.style?.setProperty('--opacity', '0');

		await wait(1000);

		reset();

		await wait(1000);

		animatingZoom = false;
	}

	onDestroy(() => {
		reset();
	});
</script>

<button
	class={cn('card-wrapper cursor-pointer', classFromProps)}
	bind:this={card}
	onpointermove={handlePointerMove}
	onpointerout={handlePointerOut}
	onpointerenter={handlePointerEnter}
	onblur={handleBlur}
	onclick={handleClick}
>
	<div class={cn('card', cardClass)}>
		{@render children()}
	</div>
</button>

<style>
	.card-wrapper {
		--scale: 1;
		--rotateX: 0;
		--rotateY: 0;
		--rotateZ: 0;
		--transitionDuration: 0;
		--mouseX: 0px;
		--mouseY: 0px;
		--glare-opacity: 0;
		--opacity: 1;
		position: relative;
	}

	/* Glare */
	.card::after {
		content: '';
		background-image: radial-gradient(farthest-side, white, transparent);
		width: 400px;
		height: 400px;
		opacity: var(--glare-opacity);
		position: absolute;
		top: 0;
		left: 0;
		transform: translate(calc(-50% + var(--mouseX)), calc(-50% + var(--mouseY)));
		transition: opacity 300ms ease-in-out;
	}

	.card {
		pointer-events: none;
		transition:
			transform var(--transitionDuration) ease-in-out,
			opacity var(--transitionDuration) ease-in-out;
		transform: perspective(800px) rotateX(var(--rotateX)) rotateY(var(--rotateY))
			rotateZ(var(--rotateZ)) scale(var(--scale));
		opacity: var(--opacity);
		overflow: hidden;
	}
</style>
