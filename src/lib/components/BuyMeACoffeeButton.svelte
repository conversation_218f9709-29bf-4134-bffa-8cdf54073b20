<script lang="ts">
	import { twMerge } from 'tailwind-merge';
	import CoffeeIcon from './Icons/CoffeeIcon.svelte';

	interface Props {
		class?: string;
		variant?: 'normal' | 'small' | 'icon';
	}

	let { class: classFromProps = '', variant = 'small' }: Props = $props();
</script>

{#if variant === 'small'}
	<a
		class={twMerge('btn-outline btn btn-sm rounded-full uppercase', classFromProps)}
		target="_blank"
		rel="noreferrer noopener"
		href="https://donate.stripe.com/aEU7t72mG6Cke1a000"
	>
		<CoffeeIcon class="mr-2 size-6" /> Buy me a coffee
	</a>
{:else if variant === 'normal'}
	<a
		class={twMerge('btn btn-primary relative', classFromProps)}
		target="_blank"
		rel="noreferrer noopener"
		href="https://donate.stripe.com/aEU7t72mG6Cke1a000"
	>
		<CoffeeIcon class="mr-2 size-6" /> Buy me a coffee
	</a>
{:else}
	<a
		class={twMerge('btn-ghost btn btn-circle rounded-full p-1', classFromProps)}
		target="_blank"
		rel="noreferrer noopener"
		href="https://donate.stripe.com/aEU7t72mG6Cke1a000"
		aria-label="Buy me a coffee"
	>
		<CoffeeIcon class="size-6" />
	</a>
{/if}
