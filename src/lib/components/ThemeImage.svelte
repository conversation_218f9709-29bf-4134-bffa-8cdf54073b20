<script lang="ts">
	import { theme } from '$lib/stores/theme.svelte';

	interface Props {
		lightAvif: string;
		lightPng: string;
		darkAvif: string;
		darkPng: string;
		alt: string;
		caption: string;
	}

	let { lightAvif, lightPng, darkAvif, darkPng, alt, caption }: Props = $props();
</script>

<figure>
	{#if theme.loaded}
		{#if theme.brightness === 'light'}
			<picture>
				<source srcset={lightAvif} />
				<img
					draggable="false"
					src={lightPng}
					{alt}
					loading="lazy"
					class="max-w-sm w-full mx-auto"
				/>
			</picture>
		{:else}
			<picture>
				<source srcset={darkAvif} />
				<img draggable="false" src={darkPng} {alt} loading="lazy" class="max-w-sm w-full mx-auto" />
			</picture>
		{/if}
	{/if}

	<figcaption class="text-center">
		{caption}
	</figcaption>
</figure>
