<script lang="ts">
	import { fade } from 'svelte/transition';

	interface Props {
		variant?: 'both' | 'vertical' | 'horizontal' | 'up';
	}

	let { variant = 'both' }: Props = $props();
	let isAlternateKey = $state(false);

	$effect(() => {
		const toggle = () => {
			isAlternateKey = !isAlternateKey;
		};

		const id = setInterval(toggle, 1500);

		return () => {
			clearInterval(id);
		};
	});
</script>

<div class="flex flex-col items-center justify-center gap-1">
	<div class="flex justify-center lg:flex">
		<kbd class:opacity-30={variant === 'horizontal'} class="kbd kbd-lg">
			{#if isAlternateKey}
				<span class="absolute" transition:fade>▲</span>
			{:else}
				<span class="absolute" transition:fade>w</span>
			{/if}
		</kbd>
	</div>
	<div class="flex justify-center gap-1">
		<kbd class:opacity-30={variant === 'vertical' || variant === 'up'} class="kbd kbd-lg">
			{#if isAlternateKey}
				<span class="absolute" transition:fade>◀︎</span>
			{:else}
				<span class="absolute" transition:fade>a</span>
			{/if}
		</kbd>
		<kbd class:opacity-30={variant === 'horizontal' || variant === 'up'} class="kbd kbd-lg">
			{#if isAlternateKey}
				<span class="absolute" transition:fade>▼</span>
			{:else}
				<span class="absolute" transition:fade>s</span>
			{/if}
		</kbd>
		<kbd class:opacity-30={variant === 'vertical' || variant === 'up'} class="kbd kbd-lg">
			{#if isAlternateKey}
				<span class="absolute" transition:fade>▶︎</span>
			{:else}
				<span class="absolute" transition:fade>d</span>
			{/if}
		</kbd>
	</div>
</div>
