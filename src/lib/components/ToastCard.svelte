<script lang="ts">
	// TODO: Remove dispatch when the library is updated to Svelte 5
	import { createEventDispatcher } from 'svelte';

	const dispatch = createEventDispatcher();

	interface Props {
		title?: string;
		description?: string;
		cancel?: {
			label: string;
			onClick?: () => void;
		};
		action?: {
			label: string;
			onClick?: () => void;
		};
	}

	let { title, description, cancel, action }: Props = $props();
</script>

<div class="w-full grow flex flex-col mx-auto">
	<div>
		{#if title}
			<h3 class="text-lg font-bold mt-0 mb-2">{title}</h3>
		{/if}

		{#if description}
			<div class="text-base mb-4">{description}</div>
		{/if}
	</div>

	<div class="flex flex-col gap-2 items-stretch mt-4">
		{#if cancel}
			<button
				class="btn btn-md btn-outline"
				onclick={() => {
					dispatch('closeToast');
					cancel?.onClick?.();
				}}
			>
				{cancel?.label}
			</button>
		{/if}

		{#if action}
			<button
				class="btn btn-md btn-primary"
				onclick={() => {
					dispatch('closeToast');
					action?.onClick?.();
				}}
			>
				{action?.label}
			</button>
		{/if}
	</div>
</div>
