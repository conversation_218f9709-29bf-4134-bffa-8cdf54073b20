<script lang="ts">
	import { animate, motionValue } from 'motion';
	import { onDestroy } from 'svelte';

	interface Props {
		angle: number;
		strokeWidth?: number;
		radius?: number;
		class?: string;
	}

	let { angle, radius = 50, strokeWidth = 2, class: className }: Props = $props();
	const effectiveAngle = motionValue(0);
	effectiveAngle.on('change', (latest) => {
		d = getPath(latest);
	});

	$effect(() => {
		animate([
			[
				effectiveAngle,
				angle,
				{
					duration: 0.3,
					ease: 'easeInOut',
				},
			],
		]);
	});

	let d = $state('');

	function getPath(angle: number) {
		const centerX = radius + strokeWidth / 2;
		const centerY = radius + strokeWidth / 2;
		const startAngle = -90; // Starts from top (12 o'clock position)
		const endAngle = startAngle + angle;

		const startX = centerX + radius * Math.cos((startAngle * Math.PI) / 180);
		const startY = centerY + radius * Math.sin((startAngle * Math.PI) / 180);
		const endX = centerX + radius * Math.cos((endAngle * Math.PI) / 180);
		const endY = centerY + radius * Math.sin((endAngle * Math.PI) / 180);

		const largeArcFlag = angle > 180 ? 1 : 0;

		return `M ${startX} ${startY} A ${radius} ${radius} 0 ${largeArcFlag} 1 ${endX} ${endY}`;
	}

	onDestroy(() => {
		effectiveAngle.destroy();
	});
</script>

<svg viewBox="0 0 {radius * 2 + strokeWidth} {radius * 2 + strokeWidth}" class={className}>
	<path {d} fill="none" stroke="currentColor" stroke-width={strokeWidth} />
</svg>
