<script lang="ts">
	import { Playlight } from '$lib/stores/playlightSdk.svelte';
	import { cn } from '$lib/util/cn';
	import { SettingsManager } from '$lib/util/SettingsManager.svelte';
	import { onDestroy, onMount } from 'svelte';
	import JoystickIcon from '../Icons/JoystickIcon.svelte';
	import { fade } from 'svelte/transition';

	interface Props {
		class?: string;
	}

	let { class: className }: Props = $props();

	let moreGamesSettings = new SettingsManager({
		key: 'more-games-button',
		defaultSettings: {
			clickCount: 0,
		},
	});

	let hasIndicator = $derived(moreGamesSettings.settings.clickCount === 0);

	onMount(() => {
		moreGamesSettings.load();
	});

	onDestroy(() => {
		moreGamesSettings.dispose();
	});
</script>

<button
	class={cn('btn relative', className)}
	onclick={() => {
		Playlight.sdk?.setDiscovery(true);
		moreGamesSettings.settings.clickCount += 1;
	}}
	aria-label="More Games"
>
	{#if hasIndicator}
		<div
			class="absolute top-0 right-0 translate-x-1/2 -translate-y-1/2"
			transition:fade={{ duration: 300 }}
		>
			<span class="badge badge-primary badge-xs hidden md:block">New</span>

			<div class="inline-grid *:[grid-area:1/1] md:hidden">
				<div class="status status-primary animate-ping"></div>
				<div class="status status-primary"></div>
			</div>
		</div>
	{/if}
	<JoystickIcon class="size-6" />
</button>
