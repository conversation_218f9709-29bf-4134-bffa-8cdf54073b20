<script lang="ts">
	interface Props {
		variant?: 'both' | 'vertical' | 'horizontal';
	}

	let { variant = 'both' }: Props = $props();
</script>

<div class="flex flex-col items-center justify-center gap-1">
	<div class="flex justify-center lg:flex">
		<kbd class:opacity-30={variant === 'horizontal'} class="kbd">▲</kbd>
	</div>
	<div class="flex justify-center gap-1">
		<kbd class:opacity-30={variant === 'vertical'} class="kbd">◀︎</kbd>
		<kbd class:opacity-30={variant === 'horizontal'} class="kbd">▼</kbd>
		<kbd class:opacity-30={variant === 'vertical'} class="kbd">▶︎</kbd>
	</div>
</div>
