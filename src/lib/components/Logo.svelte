<script lang="ts">
	let { ...props } = $props();
</script>

<svg class="fill-current {props.class}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 70 38">
	<path
		fill="inherit"
		fill-rule="evenodd"
		d="M13 25a3 3 0 0 0 3 3h6l.864-4.387A2 2 0 0 1 24.826 22h20.348a2 2 0 0 1 1.962 1.613L48 28h6a3 3 0 0 0 3-3V3a3 3 0 0 0-3-3H16a3 3 0 0 0-3 3v22Zm5-23a3 3 0 0 0-3 3v12a3 3 0 0 0 3 3h34a3 3 0 0 0 3-3V5a3 3 0 0 0-3-3H18Z"
		clip-rule="evenodd"
	/>
	<path
		fill="inherit"
		d="M46 28H24l.621-2.485A2 2 0 0 1 26.561 24h16.877a2 2 0 0 1 1.94 1.515L46 28Z"
	/>
	<path
		fill="inherit"
		fill-rule="evenodd"
		d="M22 11a4 4 0 0 1 4-4h18a4 4 0 0 1 0 8H26a4 4 0 0 1-4-4Zm7 0a2 2 0 1 1-4 0 2 2 0 0 1 4 0Zm14 2a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"
		clip-rule="evenodd"
	/>
	<path fill="inherit" d="M64.5 29a2.5 2.5 0 1 1 0-5 2.5 2.5 0 0 1 0 5Z" />
	<path
		fill="inherit"
		fill-rule="evenodd"
		d="M59 2.012c.166-.008.332-.012.5-.012C65.299 2 70 6.701 70 12.5c0 .168-.004.334-.012.5H70v19h-.022a5.5 5.5 0 1 1-10.955 0H59V2.012ZM64.5 30a3.5 3.5 0 1 1 0-7 3.5 3.5 0 0 1 0 7ZM63 11.5a1.5 1.5 0 1 0 3 0 1.5 1.5 0 0 0-3 0Zm0 6a1.5 1.5 0 1 0 3 0 1.5 1.5 0 0 0-3 0ZM61.5 13a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3Zm6 0a1.5 1.5 0 1 0 0 3 1.5 1.5 0 0 0 0-3Z"
		clip-rule="evenodd"
	/>
	<path fill="inherit" d="M8 26.5a2.5 2.5 0 1 1-5 0 2.5 2.5 0 0 1 5 0Z" />
	<path
		fill="inherit"
		fill-rule="evenodd"
		d="M11 2.012A10.674 10.674 0 0 0 10.5 2C4.701 2 0 6.701 0 12.5c0 .168.004.334.012.5H0v19h.022a5.5 5.5 0 1 0 10.955 0H11V2.012ZM5.5 18a1 1 0 0 0 1-1v-1.5H8a1 1 0 1 0 0-2H6.5V12a1 1 0 1 0-2 0v1.5H3a1 1 0 1 0 0 2h1.5V17a1 1 0 0 0 1 1ZM9 26.5a3.5 3.5 0 1 1-7 0 3.5 3.5 0 0 1 7 0Z"
		clip-rule="evenodd"
	/>
</svg>
