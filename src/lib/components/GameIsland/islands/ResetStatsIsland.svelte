<script lang="ts">
	interface Props {
		onReset: () => void;
		onCancel: () => void;
	}

	let { onReset, onCancel }: Props = $props();
</script>

<!-- svelte-ignore a11y_no_static_element_interactions -->
<!-- svelte-ignore a11y_click_events_have_key_events -->
<div
	class="flex flex-col w-full p-8"
	onclick={(event) => {
		event.stopPropagation();
	}}
>
	<h3 class="text-2xl font-medium mb-1">Reset stats?</h3>

	<p>All stats for this game will be lost</p>

	<div class="flex gap-2 items-center justify-center w-full mt-6">
		<button class="btn btn-soft rounded-full grow" onclick={onCancel}>Cancel</button>
		<button onclick={onReset} class="btn btn-primary rounded-full grow">Reset</button>
	</div>
</div>
