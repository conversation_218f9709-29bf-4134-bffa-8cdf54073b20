<script lang="ts">
	import type { Snippet } from 'svelte';

	interface Control {
		name?: string;
		bindings: [Snippet] | [Snippet, Snippet];
	}

	interface Props {
		goal: string;
		desktopControls: Control[];
		mobileControls: Control[];
	}

	let { goal, desktopControls, mobileControls }: Props = $props();
</script>

<div class="w-full p-8">
	<div class="text-xl font-bold mb-2">Goal</div>
	<div class="text-md">{goal}</div>

	<div class="text-xl font-bold mt-4 mb-2">Controls</div>

	{#snippet SingleBinding(children: Snippet)}
		<div class="flex-center">
			{@render children()}
		</div>
	{/snippet}

	{#snippet DoubleBinding(left: Snippet, right: Snippet)}
		<div class="grid grid-cols-[1fr_auto_1fr] gap-4">
			<div class="flex items-center justify-end">
				{@render left()}
			</div>

			<div class="divider divider-horizontal">OR</div>

			<div class="flex items-center justify-start">
				{@render right()}
			</div>
		</div>
	{/snippet}

	{#snippet ControlBinding(control: Control)}
		<div class="grow">
			{#if control.name}
				<div class="text-sm mb-2">{control.name}</div>
			{/if}

			{#if control.bindings.length === 1}
				{@render SingleBinding(control.bindings[0])}
			{:else}
				{@render DoubleBinding(control.bindings[0], control.bindings[1])}
			{/if}
		</div>
	{/snippet}

	<!-- Desktop -->
	<div class="lg:flex gap-6 flex-col hidden">
		{#each desktopControls as control}
			{@render ControlBinding(control)}
		{/each}

		<div class="text-center">Click to start</div>
	</div>

	<!-- Mobile -->
	<div class="flex gap-6 flex-col lg:hidden">
		{#each mobileControls as control}
			{@render ControlBinding(control)}
		{/each}

		<div class="text-center">Touch to start</div>
	</div>
</div>
