<script lang="ts">
	import PinIcon from '$lib/components/Icons/PinIcon.svelte';
	import { cn } from '$lib/util/cn';
	import type { CustomDataPoint, FixedDataPoint, LiveDataPoint } from '$lib/util/Stats.svelte';
	import { onMount, type Snippet } from 'svelte';
	import { fade } from 'svelte/transition';
	import Time from '$lib/components/Time.svelte';
	import NumberFlow from '@number-flow/svelte';
	import { wait } from '$lib/functions/wait';
	import ShimmerIcon from '$lib/components/Icons/ShimmerIcon.svelte';
	import { numberOrZero } from '$lib/util/numberOrZero';

	interface Props {
		stat: FixedDataPoint | LiveDataPoint<any, any> | CustomDataPoint<any, any>;
		isPinned?: boolean;
		onPinChange?: (isPinned: boolean) => void;
		newBest?: boolean;
		class?: string;
		withPin?: boolean;
		withTooltip?: boolean;
		animated?: boolean;
		firstAnimationDelay?: number;
		children?: Snippet;
	}

	let {
		stat,
		isPinned,
		onPinChange,
		newBest,
		withPin,
		withTooltip,
		animated,
		firstAnimationDelay = 0,
		class: classFromProps,
		children,
	}: Props = $props();
	let canRenderValue = $state(!animated);

	onMount(() => {
		wait(firstAnimationDelay).then(() => {
			canRenderValue = true;
		});
	});
</script>

<div class={cn('stat p-0 text-center flex flex-col relative border-0!', classFromProps)}>
	{#if withPin}
		{@const label = `${isPinned ? 'Unpin' : 'Pin'} ${stat.name}`}

		<button
			transition:fade={{ duration: 150 }}
			class={cn('btn btn-circle btn-soft btn-sm absolute -right-4 -top-4 z-10', {
				'btn-active btn-secondary': isPinned,
			})}
			aria-label={label}
			onclick={() => onPinChange?.(!isPinned)}
		>
			<PinIcon class="size-6" />
		</button>
	{/if}

	<div
		class={withTooltip && stat.description
			? 'tooltip tooltip-primary before:max-w-40! before:z-20! after:z-20!'
			: undefined}
		data-tip={withTooltip ? stat.description : undefined}
	>
		<div
			class={cn('stat-title text-gray-300 opacity-60', {
				'underline underline-offset-4 decoration-dashed cursor-help':
					!!stat.description && withTooltip,
			})}
		>
			{stat.name}
		</div>
	</div>
	<div class="stat-value [font-variant-numeric:tabular-nums]">
		{#if stat.unit === 'time'}
			<Time time={canRenderValue ? numberOrZero(stat.value) : 0} {animated} />
		{:else}
			<NumberFlow
				value={canRenderValue ? numberOrZero(stat.value) : 0}
				format={{
					roundingMode: 'floor',
					notation: stat.value >= 1_000_000 ? 'compact' : 'standard',
					maximumFractionDigits: 1,
				}}
				{animated}
			/>
		{/if}
	</div>
	{#if newBest}
		<div class="stat-desc text-secondary">
			<span class="relative">
				New Best
				<ShimmerIcon class="size-6 absolute left-full ml-1 bottom-0" />
			</span>
		</div>
	{/if}
	{@render children?.()}
</div>
