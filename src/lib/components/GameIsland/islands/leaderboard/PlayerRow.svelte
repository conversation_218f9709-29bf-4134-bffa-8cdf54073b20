<script lang="ts">
	import Time from '$lib/components/Time.svelte';
	import { cn } from '$lib/util/cn';

	interface Props {
		rank: number;
		score: number;
		moves: number;
		hasMoves?: boolean;
		time: number;
		isPlayer?: boolean;
		username: string;
		class?: string;
	}

	let {
		rank,
		score,
		time,
		moves,
		isPlayer,
		hasMoves = false,
		username,
		class: className,
	}: Props = $props();

	const numberFormat = Intl.NumberFormat();
</script>

<tr
	class={cn(className, {
		'bg-secondary! text-base-300! font-bold': isPlayer && rank > 2,
		'bg-yellow-400! text-base-300! font-bold': isPlayer && rank === 0,
		'bg-gray-500! font-bold': isPlayer && rank === 1,
		'bg-orange-500! text-base-300! font-bold': isPlayer && rank === 2,
	})}
>
	<td>{numberFormat.format(rank + 1)}</td>
	<td>
		<span class="line-clamp-1 wrap-anywhere">
			{username}
			{#if isPlayer}
				<span class="">(you)</span>
			{/if}
		</span>
	</td>
	<td>{numberFormat.format(score)}</td>
	{#if hasMoves}
		<td>{numberFormat.format(moves)}</td>
	{/if}
	<td>
		<Time variant="static" animated={false} {time} class="justify-start" />
	</td>
</tr>
