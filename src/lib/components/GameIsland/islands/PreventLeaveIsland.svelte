<script lang="ts">
	interface Props {
		onLeaveGame: () => void;
		onCancel: () => void;
	}

	let { onLeaveGame, onCancel }: Props = $props();
</script>

<!-- svelte-ignore a11y_no_static_element_interactions -->
<!-- svelte-ignore a11y_click_events_have_key_events -->
<div
	class="flex flex-col w-full p-8"
	onclick={(event) => {
		event.stopPropagation();
	}}
>
	<h3 class="text-2xl font-medium mb-1">Leave game?</h3>

	<p>The current game progress will be lost</p>

	<div class="flex gap-2 items-center justify-center w-full mt-6">
		<button class="btn btn-soft rounded-full grow" onclick={onCancel}>Cancel</button>
		<button onclick={onLeaveGame} class="btn btn-primary rounded-full grow">Leave game</button>
	</div>
</div>
