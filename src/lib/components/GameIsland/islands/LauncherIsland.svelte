<script lang="ts">
	import CalendarIcon from '$lib/components/Icons/CalendarIcon.svelte';
	import ShareIcon from '$lib/components/Icons/ShareIcon.svelte';
	import StatsIcon from '$lib/components/Icons/StatsIcon.svelte';

	type Island = 'stats' | 'share' | 'daily-game' | 'leaderboard';
	interface Props {
		onChange: (island: Island) => void;
	}

	let { onChange }: Props = $props();
</script>

<div class="flex items-center gap-2 justify-around p-8">
	<button onclick={() => onChange('stats')} class="btn btn-circle btn-lg flex">
		<StatsIcon class="size-8" />
	</button>
	<button onclick={() => onChange('share')} class="btn btn-circle btn-lg flex">
		<ShareIcon class="size-8" />
	</button>
	<button onclick={() => onChange('daily-game')} class="btn btn-circle btn-lg flex">
		<CalendarIcon class="size-8" />
	</button>
	<!-- <button onclick={() => onChange('leaderboard')} class="btn btn-circle btn-lg flex">
		<PeopleIcon variant="multiple" class="size-8" />
	</button> -->
</div>
