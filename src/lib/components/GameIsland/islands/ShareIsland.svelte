<script lang="ts">
	import { copy } from 'svelte-copy';
	import { page } from '$app/state';
	import { wait } from '$lib/functions/wait';
	import { cn } from '$lib/util/cn';
	import CloseIcon from '$lib/components/Icons/CloseIcon.svelte';
	import CheckIcon from '$lib/components/Icons/CheckIcon.svelte';
	import CopyIcon from '$lib/components/Icons/CopyIcon.svelte';

	let shareText = $derived(
		page.url.toString().replace('https://', '').replace('http://', '').replace('www.', ''),
	);
	let copyState = $state<'none' | 'success' | 'error'>('none');

	$effect(() => {
		if (copyState !== 'none') {
			wait(3000).then(() => (copyState = 'none'));
		}
	});
</script>

<div class="flex flex-col gap-4 w-full p-8 pt-12">
	<div class="mb-2">
		<div class="text-center text-2xl font-semibold">Share</div>
	</div>

	<input readonly value={shareText} class="input input-bordered w-full max-w-xs select-text" />

	<button
		use:copy={{
			text: shareText,
			onCopy() {
				copyState = 'success';
			},
			onError() {
				copyState = 'error';
			},
		}}
		class={cn('btn rounded-full grow', {
			'btn-primary': copyState === 'none',
			'btn-success': copyState === 'success',
			'btn-error': copyState === 'error',
		})}
	>
		{#if copyState === 'none'}
			<CopyIcon class="size-6" /> Copy link
		{:else if copyState === 'success'}
			<CheckIcon class="size-6" /> Copied!
		{:else}
			<CloseIcon class="size-6" /> Failed
		{/if}
	</button>
</div>
