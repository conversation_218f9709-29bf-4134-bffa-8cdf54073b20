<script lang="ts">
	import ShareIcon from '$lib/components/Icons/ShareIcon.svelte';
	import type { Stats } from '$lib/util/Stats.svelte';
	import Stat from './Stat.svelte';
	import LeaderboardTable from './leaderboard/LeaderboardTable.svelte';
	import { Leaderboard as LeaderboardModel } from '$lib/util/Leaderboard.svelte';
	import { authClient } from '$lib/auth/client';
	import { islandSettings } from '$lib/stores/islandSettings.svelte';
	import DotsHorizontalIcon from '$lib/components/Icons/DotsHorizontalIcon.svelte';
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import EyeIcon from '$lib/components/Icons/EyeIcon.svelte';
	import { SettingsManager } from '$lib/util/SettingsManager.svelte';

	interface Props {
		stats: Stats<any, any>;
		leaderboard?: LeaderboardModel;
		noButton?: boolean;
		shareable?: boolean;
		replayable?: boolean;
		onShare?: () => void;
		onPlayNewGame: () => void;
		// TODO: Implement it
		onReplay?: () => void;
	}

	let {
		stats,
		leaderboard,
		noButton,
		replayable,
		shareable,
		onShare,
		onPlayNewGame,
		onReplay,
	}: Props = $props();

	let visibleStats = $derived.by(() => {
		const statsToShow = [...stats.pinnedStats].filter(Boolean);

		if (statsToShow.length >= 2) {
			return statsToShow;
		}

		const otherStats = [...Object.values(stats.liveStats), ...Object.values(stats.fixedStats)];

		let index = 0;

		while (statsToShow.length !== 2 && index < otherStats.length) {
			const stat = otherStats[index];

			if (!statsToShow.find((pinned) => pinned?.name === stat.name)) {
				statsToShow.push(stat);
			}

			index += 1;
		}

		return statsToShow;
	});

	const session = authClient.useSession();
	let newGameIslandSettings = new SettingsManager({
		key: 'new-game-island',
		defaultSettings: {
			canShowJoinLeaderboardCTA: true,
		},
	});

	$effect.pre(() => {
		newGameIslandSettings.load();
	});

	function hideJoinLeaderboardCTA(e: MouseEvent) {
		newGameIslandSettings.settings.canShowJoinLeaderboardCTA = false;
		e.stopPropagation();
	}
</script>

<div class="grid grid-cols-1 py-8">
	{#if islandSettings.settings.stats}
		<div class="grid grid-cols-2 gap-2 px-8 mb-4">
			{#each visibleStats as stat}
				{#if stat}
					<Stat
						{stat}
						firstAnimationDelay={550}
						newBest={stat.isNewBest}
						animated={islandSettings.settings.animatedStats}
					/>
				{/if}
			{/each}
		</div>
	{/if}

	{#if leaderboard && islandSettings.settings.leaderboards && islandSettings.settings.showLeaderboardsOnGameOver}
		{#if $session.data?.user}
			<LeaderboardTable {leaderboard} noFloatingPlayer noTopPlayers noScoreAlert class="mb-4" />
		{:else if newGameIslandSettings.settings.canShowJoinLeaderboardCTA}
			<div class="justify-center z-10 flex flex-col items-center text-center px-8 gap-4">
				<div class="relative w-full">
					<Dropdown class="absolute -top-7 -right-4 dropdown-left">
						<DropdownButton
							class="btn btn-sm btn-ghost btn-circle"
							aria-label="open leaderboard menu"
						>
							<DotsHorizontalIcon class="size-5" />
						</DropdownButton>

						<DropdownContent menu class="w-52">
							<DropdownItem>
								<button onclick={hideJoinLeaderboardCTA}>
									<EyeIcon variant="invisible" class="size-5" />
									Hide this message
								</button>
							</DropdownItem>
						</DropdownContent>
					</Dropdown>

					<p class="mb-1 text-2xl font-medium">Sign in to join the leaderboard</p>
					<p class="text-sm font-normal">Compete for a top 3 spot!</p>
				</div>

				<a href="/signin" class="btn btn-secondary btn-outline w-full rounded-full">
					Join the leaderboard
				</a>
			</div>
		{/if}
	{/if}

	{#if !noButton}
		<div class="flex flex-col gap-4 grow px-8 mt-2">
			{#if leaderboard && $session.data?.user && replayable}
				<button
					class="btn btn-secondary btn-outline rounded-full"
					onclick={(event) => {
						event.stopPropagation();
						onReplay?.();
					}}
				>
					Replay Current Game
				</button>
			{/if}

			<div class="flex gap-4">
				{#if shareable}
					<button
						class="btn btn-circle"
						onclick={(event) => {
							event.stopPropagation();
							onShare?.();
						}}
						aria-label="Share"
					>
						<ShareIcon class="size-6" />
					</button>
				{/if}
				<button
					class="btn btn-primary rounded-full grow"
					onclick={(event) => {
						event.stopPropagation();
						onPlayNewGame();
					}}
				>
					Play a new game
				</button>
			</div>
		</div>
	{/if}
</div>
