<script lang="ts">
	interface Props {
		title: string;
		description: string;
		onCancel: () => void;
	}

	let { title, description, onCancel }: Props = $props();
</script>

<!-- svelte-ignore a11y_no_static_element_interactions -->
<!-- svelte-ignore a11y_click_events_have_key_events -->
<div
	class="flex flex-col w-full p-8"
	onclick={(event) => {
		event.stopPropagation();
	}}
>
	<h3 class="text-2xl font-medium mb-1">{title}</h3>

	<p>{description}</p>

	<div class="flex gap-2 items-center justify-center w-full mt-6">
		<button class="btn btn-soft rounded-full grow" onclick={onCancel}>Not now</button>
		<button
			onclick={() => {
				location.reload();
			}}
			class="btn btn-primary rounded-full grow">Refresh page</button
		>
	</div>
</div>
