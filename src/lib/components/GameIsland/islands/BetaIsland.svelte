<script lang="ts">
	import FeedbackIcon from '$lib/components/Icons/FeedbackIcon.svelte';

	interface Props {
		onClose: () => void;
	}

	let { onClose }: Props = $props();
</script>

<!-- svelte-ignore a11y_no_static_element_interactions -->
<!-- svelte-ignore a11y_click_events_have_key_events -->
<div
	class="flex flex-col w-full p-8"
	onclick={(event) => {
		event.stopPropagation();
	}}
>
	<h3 class="text-2xl font-medium mb-1">Beta release</h3>

	<p>
		The game is in beta, so bugs and poor performance on some devices may occur. Feedback <FeedbackIcon
			class="size-6 inline"
		/> is greatly appreciated!
	</p>

	<button onclick={onClose} class="btn btn-primary rounded-full grow mt-6">Got it</button>
</div>
