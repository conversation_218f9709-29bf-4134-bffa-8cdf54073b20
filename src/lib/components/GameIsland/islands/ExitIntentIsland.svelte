<script lang="ts">
	import JoystickIcon from '$lib/components/Icons/JoystickIcon.svelte';
	import { getRandomItemAt } from '$lib/functions/getRandomItemAt';
	import { Playlight } from '$lib/stores/playlightSdk.svelte';

	const messages = [
		// 'The cure for boredom',
		// 'Ready for what’s next?',
		'Why stop the fun now?',
		"Games don't play themselves",
		'A new adventure awaits',
		"There's always time for one more",
	];

	let randomMessage = getRandomItemAt(messages);
</script>

<div class="flex flex-col w-full p-8">
	<h3 class="text-3xl text-center font-medium mb-1">Leaving already?</h3>

	<p class="text-center">{randomMessage}</p>

	<button
		class="btn btn-primary rounded-full w-full mt-6"
		onclick={(event) => {
			event.stopPropagation();
			Playlight.sdk?.setDiscovery(true);
		}}
	>
		<JoystickIcon class="size-7" />

		Play partner games
	</button>
</div>
