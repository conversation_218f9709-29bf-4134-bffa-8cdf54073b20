<script lang="ts">
	import { cn } from '$lib/util/cn';
	import type { Snippet } from 'svelte';

	interface Props {
		children: Snippet;
		class?: string;
		asSettings?: boolean;
	}

	let { children, class: className, asSettings = false }: Props = $props();
</script>

<div
	class={cn(
		'collapse-content [visibility:unset]! relative',
		{
			'pr-0 pb-0! flex flex-col before:absolute before:top-2 before:left-0 before:w-[1px] before:bottom-2 before:bg-base-content/60 before:z-10':
				asSettings,
		},
		className,
	)}
>
	{@render children()}
</div>
