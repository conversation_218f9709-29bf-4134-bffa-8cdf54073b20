<script lang="ts">
	import { cn } from '$lib/util/cn';
	import type { Snippet } from 'svelte';

	interface Props {
		children: Snippet;
		class?: string;
		open?: boolean;
	}

	let { children, open = false, class: className }: Props = $props();
</script>

<div
	class={cn(
		'collapse shrink-0 rounded-none [visibility:unset]!',
		{ 'collapse-open': open },
		className,
	)}
>
	{@render children()}
</div>
