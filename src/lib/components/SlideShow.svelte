<script lang="ts">
	import { browser } from '$app/environment';
	import { onDestroy } from 'svelte';
	import { fade } from 'svelte/transition';

	let index = $state(0);
	let timeoutId = -1;
	let image: string = $state('');
	let oldImage: string = $state('');

	interface Props {
		class?: string;
		style?: string;
		images: string[];
		slideDuration?: number;
		fadeDuration?: number;
		active?: boolean;
		onChange?: (image: string) => void;
	}

	let {
		class: classFromProps = '',
		style: styleFromProps = '',
		images,
		slideDuration = 5000,
		fadeDuration = 2000,
		active = true,
		onChange = () => {
			/** Ignore */
		},
	}: Props = $props();

	function stop() {
		clearTimeout(timeoutId);
		timeoutId = -1;
	}

	function start() {
		stop();
		const currentImages = images;

		if (active) {
			timeoutId = setTimeout(() => {
				if (active) {
					if (images === currentImages) {
						let nextIndex = index + 1;

						if (nextIndex >= images.length) {
							nextIndex = 0;
						}

						index = nextIndex;
					}

					start();
				}
			}, slideDuration) as unknown as number;
		}
	}

	onDestroy(() => {
		stop();
	});

	$effect(() => {
		// Reset index when images change and the current index is out of bounds
		if (index >= images.length) {
			index = 0;
			start();
		}
	});

	let imagesToPreload = $derived([images[index], images[index + 1] ?? images[0]].filter(Boolean));

	$effect(() => {
		if (active) {
			image = images[index];

			if (image !== oldImage) {
				onChange(image);
				oldImage = image;
			}
		}
	});

	$effect(() => {
		// Start or stop timeout according to active prop
		if (active) {
			start();
		} else {
			stop();
		}
	});
</script>

{#if image}
	{#key image}
		<div
			transition:fade={{ duration: fadeDuration }}
			class="bg-cover bg-no-repeat {classFromProps}"
			style={image ? `background-image: url(${image}); ${styleFromProps}` : styleFromProps}
		></div>
	{/key}
{/if}

<!-- Preload current and next images -->
{#if browser}
	{#each imagesToPreload as image}
		<img width="0" height="0" src={image} alt="" />
	{/each}
{/if}
