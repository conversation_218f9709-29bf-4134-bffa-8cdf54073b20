<script lang="ts">
	import { getContext, type Snippet } from 'svelte';
	import { tabsContext, type TabsContext } from './Tabs.svelte';
	import { cn } from '$lib/util/cn';
	import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'svelte/elements';

	interface Props {
		key: string;
		class?: string;
		children: Snippet;
		onclick?: <PERSON><PERSON>vent<PERSON>andler<HTMLButtonElement>;
	}

	let context = getContext<TabsContext>(tabsContext);

	let { key, class: className, children, onclick, ...props }: Props = $props();
</script>

<button
	class={cn(
		'flex flex-row gap-2 justify-center items-center rounded-full px-4 py-3 z-10 cursor-pointer',
		{
			'text-neutral-content': context?.active === key,
			'text-base-content': context?.active !== key,
		},
		className,
	)}
	data-key={key}
	onclick={(e) => {
		if (context) {
			context.active = key;
		}

		onclick?.(e);
	}}
	{...props}
>
	{@render children()}
</button>
