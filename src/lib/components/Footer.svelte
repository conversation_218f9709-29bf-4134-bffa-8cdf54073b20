<script lang="ts">
	import { cn } from '$lib/util/cn';
	import FeedbackModal from './FeedbackModal.svelte';
	import InstagramIcon from './Icons/logos/InstagramIcon.svelte';
	import ThreadsIcon from './Icons/logos/ThreadsIcon.svelte';
	import TiktokIcon from './Icons/logos/TiktokIcon.svelte';

	interface Props {
		class?: string;
	}

	let props: Props = $props();

	let isOpen = $state(false);
</script>

<FeedbackModal title="Contact" requiredEmail bind:isOpen />

<footer
	class={cn(
		'footer footer-center footer-horizontal mt-8 rounded-sm bg-base-200 p-10 text-lg text-base-content',
		props.class,
	)}
>
	<nav class="grid grid-flow-row gap-6 sm:grid-flow-col">
		<a href="/about" class="link-hover link">About us</a>
		<a href="/terms-and-conditions" class="link-hover link">Terms of use</a>
		<a href="/privacy-policy" class="link-hover link">Privacy policy</a>
		<a href="/disclaimer" class="link-hover link">Disclaimer</a>
		<button class="link-hover link" onclick={() => (isOpen = true)}>Contact</button>
	</nav>

	<nav>
		<div class="grid grid-flow-col gap-6">
			<a href="https://www.instagram.com/lofiandgames_official/" target="_blank">
				<InstagramIcon class="size-8" />
			</a>
			<a href="https://www.threads.com/@lofiandgames_official" target="_blank">
				<ThreadsIcon class="size-8" />
			</a>
			<a href="https://www.tiktok.com/@lofiandgames" target="_blank">
				<TiktokIcon class="size-8" />
			</a>
		</div>
	</nav>

	<aside>
		<p>Copyright © 2022-present - All rights reserved</p>
	</aside>
</footer>
