<script lang="ts">
	import { cn } from '$lib/util/cn';
	import type { Snippet } from 'svelte';
	import ChevronLeftIcon from '../Icons/ChevronLeftIcon.svelte';

	interface Props {
		onBack: () => void;
		class?: string;
		title: string;
		trailing?: Snippet;
	}

	let { onBack, class: className, title, trailing }: Props = $props();
</script>

<div class={cn('flex items-center gap-4 pt-2 pb-4', className)}>
	<button class="btn btn-ghost btn-circle btn-sm" aria-label="Back" onclick={onBack}>
		<ChevronLeftIcon class="size-6" />
	</button>

	<span class="text-lg font-medium">
		{title}
	</span>

	{#if trailing}
		<div class="flex grow justify-end items-center">
			{@render trailing()}
		</div>
	{/if}
</div>
