<script lang="ts">
	import InfoSolidIcon from './Icons/InfoSolidIcon.svelte';
	import { cn } from '$lib/util/cn';
	import WarningSolidIcon from './Icons/WarningSolidIcon.svelte';
	import CheckIcon from './Icons/CheckIcon.svelte';

	interface Props {
		title?: string;
		description?: string;
		sentiment?: 'info' | 'success' | 'warning' | 'error';
		class?: string;
		cancel?: {
			label: string;
			onClick?: () => void;
		};
		action?: {
			label: string;
			onClick?: () => void;
		};
	}

	let { title, description, cancel, action, sentiment, class: className }: Props = $props();
</script>

<div
	role="alert"
	class={cn(
		'alert',
		{
			'alert-info': sentiment === 'info',
			'alert-success': sentiment === 'success',
			'alert-warning': sentiment === 'warning',
			'alert-error': sentiment === 'error',
		},
		className,
	)}
>
	{#if sentiment === 'warning'}
		<WarningSolidIcon class="size-5" />
	{:else if sentiment === 'info' || !sentiment}
		<InfoSolidIcon class="size-5" />
	{:else if sentiment === 'success'}
		<CheckIcon class="size-5" />
	{:else if sentiment === 'error'}
		<WarningSolidIcon class="size-5" />
	{/if}

	<div>
		{#if title}
			<h3 class="text-base font-medium">{title}</h3>
		{/if}

		{#if description}
			<div class="text-sm">{description}</div>
		{/if}
	</div>

	<div>
		{#if cancel}
			<button
				class="btn btn-sm"
				onclick={() => {
					cancel?.onClick?.();
				}}
			>
				{cancel?.label}
			</button>
		{/if}

		{#if action}
			<button
				class="btn btn-sm btn-primary"
				onclick={() => {
					action?.onClick?.();
				}}
			>
				{action?.label}
			</button>
		{/if}
	</div>
</div>
