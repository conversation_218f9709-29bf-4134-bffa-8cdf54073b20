<script lang="ts">
	import { fade } from 'svelte/transition';
	import { twMerge } from 'tailwind-merge';

	interface Props {
		class?: string;
		onclose?: (event: MouseEvent) => void;
	}

	const { class: classFromProps, onclose, ...props }: Props = $props();
</script>

<div
	transition:fade|global={{ duration: 300 }}
	class={twMerge(
		'fixed inset-0 bg-black opacity-40 cursor-default touch-pinch-zoom z-30 outline-hidden',
		classFromProps,
	)}
	role="button"
	tabindex="0"
	aria-label="close"
	onclick={(event) => {
		event.preventDefault();
		event.stopPropagation();
		onclose?.(event);
	}}
	oncontextmenu={(event) => {
		event.preventDefault();
		event.stopPropagation();
		onclose?.(event);
	}}
	{...props}
></div>
