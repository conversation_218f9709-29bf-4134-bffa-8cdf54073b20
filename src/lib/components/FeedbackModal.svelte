<script lang="ts">
	import { supabase } from '$lib/api/supabase';
	import { captureException } from '@sentry/sveltekit';
	import Dialog from './Dialog.svelte';
	import emailjs from '@emailjs/browser';
	import Alert from './Alert.svelte';
	import { fade } from 'svelte/transition';
	import { quadInOut } from 'svelte/easing';
	import { authClient } from '$lib/auth/client';

	interface Props {
		isOpen?: boolean;
		context?: string;
		extra?: string;
		title?: string;
		description?: string;
		requiredEmail?: boolean;
		feedbackLabel?: string;
		feedbackPlaceholder?: string;
		feedbackErrorLabel?: string;
	}

	let {
		isOpen = $bindable(false),
		context = '',
		extra = '',
		title = 'We Value Your Feedback!',
		description = 'Help us improve your experience by sharing your thoughts and suggestions. Let us know what you love and what we can do better!',
		requiredEmail = false,
		feedbackLabel = 'Feedback',
		feedbackPlaceholder = 'Feedback',
		feedbackErrorLabel = 'Enter your feedback',
	}: Props = $props();

	let email = $state('');
	let feedback = $state('');
	let sendState: 'sending' | 'error' | 'success' | 'none' = $state('none');
	let feedbackError: string | null = $state(null);
	let emailError: string | null = $state(null);
	let session = authClient.useSession();

	function reset() {
		email = $session.data?.user?.email ?? '';
		feedback = '';
		sendState = 'none';
		feedbackError = null;
		emailError = null;
	}

	async function sendFeedback() {
		if (!$session.data?.user.email) {
			if (requiredEmail && email.trim().length === 0) {
				emailError = 'Email is required';
				return;
			}

			if (email.trim().length !== 0 && !email.includes('@')) {
				emailError = 'Email is incorrect';
				return;
			}
		}

		emailError = null;

		if (feedback.trim() === '') {
			feedbackError = 'Feedback is required';
			return;
		}

		sendState = 'sending';

		try {
			function serialize(obj: Record<string, any>) {
				const result: Record<string, any> = {};
				let _tmp: Record<string, any> = {};

				for (var i in obj) {
					// enabledPlugin is too nested, also skip functions
					if (i === 'enabledPlugin' || typeof obj[i] === 'function') {
						continue;
					} else if (typeof obj[i] === 'object') {
						// get props recursively
						_tmp = serialize(obj[i]);
						// if object is not {}
						if (Object.keys(_tmp).length) {
							result[i] = _tmp;
						}
					} else {
						// string, number or boolean
						result[i] = obj[i];
					}
				}
				return result;
			}

			let device: Record<string, any> = {};

			try {
				device = serialize(navigator);
			} catch (e) {
				captureException(e);
			}

			let feedbackWithExtra = `${feedback} \n ${extra}`;

			await supabase
				.from('feedback')
				.insert({ context, feedback: feedbackWithExtra, email, device })
				.throwOnError();
			sendState = 'success';

			void emailjs.send(
				'service_5txlodq',
				'template_i1rhaz4',
				{
					context,
					feedback: feedbackWithExtra,
					email,
				},
				{
					publicKey: '7os0r2MhYMaPByC_M',
				},
			);
		} catch (error) {
			sendState = 'error';
		}
	}

	$effect(function initEmail() {
		if ($session.data?.user?.email) {
			email = $session.data.user.email;
		}
	});

	$effect(() => {
		if (feedback.trim() !== '') {
			feedbackError = null;
		}
	});

	$effect(() => {
		if (email.trim() !== '') {
			emailError = null;
		}
	});

	$effect(() => {
		if (!isOpen) {
			setTimeout(() => {
				if (sendState === 'success') {
					reset();
				}

				sendState = 'none';
				feedbackError = null;
			}, 300);
		}
	});
</script>

<Dialog bind:isOpen>
	{#if sendState !== 'success'}
		<article class="p-0">
			<div>
				{#if sendState === 'error'}
					<div transition:fade={{ duration: 300, easing: quadInOut }}>
						<Alert
							class="mt-8"
							sentiment="error"
							title="There was an error. Please, try again later."
						/>
					</div>
				{/if}

				<h2 class="mt-4">{title}</h2>

				<p class="mb-4">
					{description}
				</p>

				<div class="flex flex-col">
					{#if !$session.data?.user?.email}
						<fieldset class="fieldset">
							<legend class="fieldset-legend">
								Email
								{#if !requiredEmail}
									<span class="label font-normal">(optional)</span>
								{/if}
							</legend>
							<input
								type="email"
								placeholder="<EMAIL>"
								class="input input-bordered validator w-full"
								bind:value={email}
								onkeydown={(e) => e.stopPropagation()}
								name="Email"
								autocomplete="email"
								required={requiredEmail}
							/>

							<div class="validator-hint hidden">Enter a valid email address</div>
						</fieldset>
					{/if}

					<fieldset class="fieldset">
						<legend class="fieldset-legend">{feedbackLabel}</legend>
						<textarea
							placeholder={feedbackPlaceholder}
							class="textarea validator textarea-bordered textarea-lg w-full"
							bind:value={feedback}
							onkeydown={(e) => e.stopPropagation()}
							name="Feedback"
							required
						></textarea>

						<div class="validator-hint hidden">{feedbackErrorLabel}</div>
					</fieldset>
				</div>
			</div>

			<div class="modal-action">
				<form action="dialog">
					<button class="btn btn-ghost">Cancel</button>
				</form>

				<button
					class="btn btn-primary"
					disabled={sendState === 'sending'}
					onclick={() => sendFeedback()}
				>
					{#if sendState === 'sending'}
						<span class="loading loading-spinner"></span>
					{/if}
					Send
				</button>
			</div>
		</article>
	{/if}

	{#if sendState === 'success'}
		<article class="text-center p-0">
			<h2 class="mt-4 mb-2">Thank you!</h2>
			<p>Your opinion is very important</p>
			<p class="my-8 text-5xl">🥳</p>

			<div class="card-actions justify-center">
				<form method="dialog">
					<button class="btn btn-primary">Awesome</button>
				</form>
			</div>
		</article>
	{/if}
</Dialog>
