<script lang="ts">
	interface Props {
		class?: string;
	}

	let props: Props = $props();
</script>

<svg
	class="size-full"
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<path
		d="M10.4093 21.7578L9.43948 18.8483C9.04134 17.6539 8.10407 16.7167 6.90965 16.3185L4.00018 15.3487L6.90965 14.3789C8.10407 13.9807 9.04134 13.0435 9.43948 11.849L10.4093 8.93957L11.3791 11.849C11.7773 13.0435 12.7145 13.9807 13.9089 14.3789L16.8184 15.3487L13.9089 16.3185C12.7145 16.7167 11.7773 17.6539 11.3791 18.8483L10.4093 21.7578Z"
		fill="currentColor"
	/>
	<path
		d="M17.4698 11.9998L17.3523 11.6472C16.9542 10.4528 16.0169 9.51553 14.8225 9.11739L14.47 8.99988L14.8225 8.88236C16.0169 8.48422 16.9542 7.54696 17.3523 6.35254L17.4698 6L17.5874 6.35254C17.9855 7.54696 18.9228 8.48422 20.1172 8.88236L20.4697 8.99988L20.1172 9.11739C18.9228 9.51553 17.9855 10.4528 17.5874 11.6472L17.4698 11.9998Z"
		fill="currentColor"
	/>
	<path
		d="M13.3905 5.60962C13.1851 4.99354 12.7017 4.51011 12.0856 4.30475C12.7017 4.09939 13.1851 3.61595 13.3905 2.99988C13.5959 3.61595 14.0793 4.09939 14.6954 4.30475C14.0793 4.51011 13.5959 4.99354 13.3905 5.60962Z"
		fill="currentColor"
	/>
</svg>
