<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', className)}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<path
		d="M17 6 7 12l10 6"
		stroke="currentColor"
		stroke-width="2"
		stroke-linecap="round"
		stroke-linejoin="round"
	/>
	<circle cx="17" cy="18" r="3" fill="currentColor" />
	<circle cx="7" cy="12" r="3" fill="currentColor" />
	<circle cx="17" cy="6" r="3" fill="currentColor" />
</svg>
