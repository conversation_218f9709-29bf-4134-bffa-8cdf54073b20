<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', className)}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<path
		d="M8.889 5H6a1 1 0 0 0-1 1v2.889M15.111 5H18a1 1 0 0 1 1 1v2.889m0 6.222V18a1 1 0 0 1-1 1h-2.889M8.89 19H6a1 1 0 0 1-1-1v-2.889"
		stroke="currentColor"
		stroke-width="2"
		stroke-linecap="round"
		stroke-linejoin="round"
	/>
</svg>
