<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', className)}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<path
		fill-rule="evenodd"
		clip-rule="evenodd"
		d="M12 5H5v14h7v2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7v2Z"
		fill="currentColor"
	/>
	<path
		d="M10 12h10m0 0-4 4m4-4-4-4"
		stroke="currentColor"
		stroke-width="2"
		stroke-linecap="round"
		stroke-linejoin="round"
	/>
</svg>
