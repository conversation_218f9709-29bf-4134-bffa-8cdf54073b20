<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
		width?: string | number;
		height?: string | number;
		x?: string | number;
		y?: string | number;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', className)}
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	viewBox="0 0 24 24"
	{...props}
>
	<rect x="4" y="6" width="16" height="14" rx="3" stroke="currentColor" stroke-width="2" />
	<path
		d="M7 6h10a3 3 0 0 1 3 3H4a3 3 0 0 1 3-3Z"
		fill="currentColor"
		stroke="currentColor"
		stroke-width="2"
	/>
	<rect x="7" y="3" width="2" height="3" rx="1" fill="currentColor" />
	<rect x="15" y="3" width="2" height="3" rx="1" fill="currentColor" />
	<rect x="14" y="14" width="4" height="4" rx="1" fill="currentColor" />
</svg>
