<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', className)}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<circle cx="6" cy="11" r="3" fill="currentColor" />
	<circle cx="11" cy="9" r="5" fill="currentColor" />
	<circle cx="17" cy="10" r="4" fill="currentColor" />
	<path d="M6 11h11v3H6v-3Z" fill="currentColor" />
	<path d="M7 16v2m5-2v3m5-3v2" stroke="currentColor" stroke-linecap="round" />
</svg>
