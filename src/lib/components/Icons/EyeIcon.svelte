<script lang="ts">
	import { cn } from '$lib/util/cn';

	type Variant = 'visible' | 'invisible';

	interface Props {
		variant?: Variant;
		class?: string;
	}

	const { variant = 'visible', class: classFromProps, ...props }: Props = $props();
</script>

<svg
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	class={cn('size-full', classFromProps, {
		'eye-visible': variant === 'visible',
	})}
	{...props}
>
	<path
		class="fill-current"
		fill-rule="evenodd"
		clip-rule="evenodd"
		d="M12 17C14.2091 17 16 15.2091 16 13C16 10.7909 14.2091 9 12 9C9.79086 9 8 10.7909 8 13C8 15.2091 9.79086 17 12 17ZM11 13C11.5523 13 12 12.5523 12 12C12 11.4477 11.5523 11 11 11C10.4477 11 10 11.4477 10 12C10 12.5523 10.4477 13 11 13Z"
	/>
	<path
		class="stroke-current"
		d="M19 14C19 10.134 15.866 7 12 7C8.13401 7 5 10.134 5 14"
		stroke-linecap="round"
	/>

	<path
		class="stroke-base-100 invisible-line"
		d="M17.6569 6.34326L6.34315 17.657"
		stroke-width="3"
		stroke-linecap="round"
	/>
	<path
		class="stroke-current invisible-line"
		d="M17.6569 6.34326L6.34315 17.657"
		stroke-linecap="round"
	/>
</svg>

<style>
	.eye-visible .invisible-line {
		stroke-dasharray: 20;
		stroke-dashoffset: 20;
	}

	.invisible-line {
		stroke-dasharray: 20;
		stroke-dashoffset: 0;
		transition: stroke-dashoffset 300ms ease-in-out;
	}
</style>
