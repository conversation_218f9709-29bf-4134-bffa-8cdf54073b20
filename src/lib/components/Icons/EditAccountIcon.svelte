<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', className)}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<circle cx="12" cy="8.143" r="5.143" fill="currentColor" />
	<path
		d="m17.96 13.347 1.796 1.796-5.655 5.656-1.79.012-.006-1.808 5.656-5.656Zm1.987-1.986a1 1 0 0 1 1.414 0l.382.382a1 1 0 0 1 0 1.414l-1.089 1.088-1.795-1.795 1.088-1.089Z"
		fill="currentColor"
	/>
	<path
		fill-rule="evenodd"
		clip-rule="evenodd"
		d="M12 14.571c.86 0 1.695.12 2.49.342L8.403 21h-3.95c-.701 0-1.18-.703-.843-1.317 1.682-3.057 4.81-5.112 8.39-5.112Z"
		fill="currentColor"
	/>
</svg>
