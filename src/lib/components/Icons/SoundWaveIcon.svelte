<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	const { class: classFromProps, ...props }: Props = $props();
</script>

<svg
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	class={cn('size-full', classFromProps)}
	{...props}
>
	<path
		d="M4.25 11.5v1m3-4.5v8m3-6v4m3-8v12m3-9v6m3-4v2"
		stroke="currentColor"
		stroke-width="2"
		stroke-linecap="round"
	/>
</svg>
