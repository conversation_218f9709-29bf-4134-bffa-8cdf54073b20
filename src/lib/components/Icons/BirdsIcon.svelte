<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', className)}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<path
		d="M20.159 10.274c-.095.353-.577.268-.713-.071-.448-1.114-1.445-2.032-2.76-2.384-1.26-.338-2.529-.079-3.463.596-.472-1.052-1.442-1.91-2.702-2.248-1.314-.352-2.637-.056-3.581.685-.288.225-.749.058-.654-.295.526-1.964 2.678-3.095 4.807-2.524 1.26.337 2.23 1.196 2.701 2.248.935-.675 2.204-.934 3.465-.596 2.128.57 3.426 2.625 2.9 4.59Z"
		fill="currentColor"
	/>
	<path
		d="M20.159 10.274c-.095.353-.577.268-.713-.071-.448-1.114-1.445-2.032-2.76-2.384-1.26-.338-2.529-.079-3.463.596-.472-1.052-1.442-1.91-2.702-2.248-1.314-.352-2.637-.056-3.581.685-.288.225-.749.058-.654-.295.526-1.964 2.678-3.095 4.807-2.524 1.26.337 2.23 1.196 2.701 2.248.935-.675 2.204-.934 3.465-.596 2.128.57 3.426 2.625 2.9 4.59Zm.151 9.57c-.115.43-.702.325-.868-.087-.543-1.354-1.756-2.47-3.354-2.898-1.533-.41-3.076-.096-4.212.725-.573-1.28-1.752-2.323-3.284-2.734-1.598-.428-3.207-.068-4.355.833-.35.274-.91.071-.794-.358.64-2.389 3.256-3.763 5.844-3.07 1.532.411 2.711 1.455 3.285 2.734 1.136-.82 2.679-1.136 4.211-.725 2.588.693 4.167 3.192 3.527 5.58Z"
		fill="currentColor"
	/>
	<path
		d="M20.31 19.844c-.115.43-.702.325-.868-.087-.543-1.354-1.756-2.47-3.354-2.898-1.533-.41-3.076-.096-4.212.725-.573-1.28-1.752-2.323-3.284-2.734-1.598-.428-3.207-.068-4.355.833-.35.274-.91.071-.794-.358.64-2.389 3.256-3.763 5.844-3.07 1.532.411 2.711 1.455 3.285 2.734 1.136-.82 2.679-1.136 4.211-.725 2.588.693 4.167 3.192 3.527 5.58Z"
		fill="currentColor"
	/>
</svg>
