<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	const { class: classFromProps, ...props }: Props = $props();
</script>

<svg
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	class={cn('size-full', classFromProps)}
	{...props}
>
	<rect x="4" y="4" width="6" height="6" rx="2" stroke="currentColor" stroke-width="2" />
	<rect x="14" y="4" width="6" height="6" rx="2" stroke="currentColor" stroke-width="2" />
	<rect x="4" y="14" width="6" height="6" rx="2" stroke="currentColor" stroke-width="2" />
	<rect x="14" y="14" width="6" height="6" rx="2" stroke="currentColor" stroke-width="2" />
</svg>
