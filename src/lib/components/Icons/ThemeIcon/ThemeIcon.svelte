<script lang="ts">
	import { theme } from '$lib/stores/theme.svelte';
	import Moon from './Moon.svelte';
	import Sun from './Sun.svelte';

	interface Props {
		class?: string;
	}

	let { class: className = '' }: Props = $props();
</script>

{#if theme.loaded}
	{#if theme.value === 'light' || theme.value === 'light-classic'}
		<Sun class={className} />
	{/if}

	{#if theme.value === 'dark' || theme.value === 'dark-classic'}
		<Moon class={className} />
	{/if}
{/if}
