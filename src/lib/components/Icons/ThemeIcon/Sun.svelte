<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		animated?: boolean;
		class?: string;
	}

	let { animated, class: className, ...props }: Props = $props();
</script>

<svg
	class={cn(
		'size-full',
		{
			animated,
		},
		className,
	)}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<circle cx="12" cy="12" r="4.5" fill="currentColor" />
	<line
		x1="12"
		y1="5"
		x2="12"
		y2="3"
		stroke="currentColor"
		stroke-width="2"
		stroke-linecap="round"
	/>
	<line
		x1="16.9497"
		y1="7.05014"
		x2="18.3639"
		y2="5.63593"
		stroke="currentColor"
		stroke-width="2"
		stroke-linecap="round"
	/>
	<line
		x1="19"
		y1="12"
		x2="21"
		y2="12"
		stroke="currentColor"
		stroke-width="2"
		stroke-linecap="round"
	/>
	<line
		x1="16.9499"
		y1="16.9498"
		x2="18.3641"
		y2="18.364"
		stroke="currentColor"
		stroke-width="2"
		stroke-linecap="round"
	/>
	<line
		x1="12"
		y1="19"
		x2="12"
		y2="21"
		stroke="currentColor"
		stroke-width="2"
		stroke-linecap="round"
	/>
	<line
		x1="7.05029"
		y1="16.9499"
		x2="5.63608"
		y2="18.3641"
		stroke="currentColor"
		stroke-width="2"
		stroke-linecap="round"
	/>
	<line
		x1="5"
		y1="12"
		x2="3"
		y2="12"
		stroke="currentColor"
		stroke-width="2"
		stroke-linecap="round"
	/>
	<line
		x1="7.05014"
		y1="7.05023"
		x2="5.63593"
		y2="5.63602"
		stroke="currentColor"
		stroke-width="2"
		stroke-linecap="round"
	/>
</svg>

<style>
	@keyframes rise {
		from {
			opacity: 0;
			transform: translateY(100%);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.animated circle {
		animation: rise 300ms ease-out forwards;
	}

	@keyframes dash {
		from {
			stroke-dashoffset: 4;
		}
		to {
			stroke-dashoffset: 0;
		}
	}

	.animated line {
		animation: dash 200ms ease-in forwards;
		stroke-dashoffset: 4;
	}

	line {
		stroke-dasharray: 4;
		stroke-dashoffset: 0;
	}
	line:nth-of-type(1) {
		animation-delay: 200ms;
	}
	line:nth-of-type(2) {
		animation-delay: 250ms;
	}
	line:nth-of-type(3) {
		animation-delay: 300ms;
	}
	line:nth-of-type(4) {
		animation-delay: 350ms;
	}
	line:nth-of-type(5) {
		animation-delay: 400ms;
	}
	line:nth-of-type(6) {
		animation-delay: 450ms;
	}
	line:nth-of-type(7) {
		animation-delay: 500ms;
	}
	line:nth-of-type(8) {
		animation-delay: 550ms;
	}
</style>
