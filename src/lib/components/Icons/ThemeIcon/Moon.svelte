<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		animated?: boolean;
		class?: string;
	}

	let { animated = false, class: className, ...props }: Props = $props();
</script>

<svg
	class={cn(
		'size-full',
		{
			animated,
		},
		className,
	)}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<path
		d="M13.341 5.08C11.386 6.817 10.866 9.688 12.25 12c1.383 2.313 4.217 3.313 6.751 2.546a7.043 7.043 0 0 1-3.128 3.515c-3.466 1.933-7.898.786-9.9-2.562-2.001-3.347-.813-7.627 2.653-9.56a7.435 7.435 0 0 1 4.716-.858Z"
		fill="currentColor"
	/>
</svg>

<style>
	@keyframes rise {
		from {
			opacity: 0;
			transform: translateY(100%);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.animated path {
		animation: rise 300ms ease-out forwards;
	}
</style>
