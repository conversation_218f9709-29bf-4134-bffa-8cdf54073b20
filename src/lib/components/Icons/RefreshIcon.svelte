<script lang="ts">
	import { cn } from '$lib/util/cn';

	let { class: classFromProps = '' } = $props();
</script>

<svg class={cn('size-full', classFromProps)} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
	<path
		fill="currentColor"
		d="M5 12a7 7 0 0 1 13.93-1h-.928a1 1 0 0 0-.866 1.5l1.933 3.348a1 1 0 0 0 1.732 0l1.933-3.348a1 1 0 0 0-.866-1.5h-.923A9.002 9.002 0 0 0 3 12a9 9 0 0 0 15.12 6.599 1 1 0 1 0-1.36-1.466A7 7 0 0 1 5 12Z"
	/>
</svg>
