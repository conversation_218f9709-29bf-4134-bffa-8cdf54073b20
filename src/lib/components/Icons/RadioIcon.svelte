<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', className)}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<path
		fill-rule="evenodd"
		clip-rule="evenodd"
		d="M3 9a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Zm16 8.5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0ZM6 9a1 1 0 0 0-1 1v3a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-3a1 1 0 0 0-1-1H6Zm-1 7.5a.5.5 0 0 1 .5-.5h6a.5.5 0 0 1 0 1h-6a.5.5 0 0 1-.5-.5Zm.5 1.5a.5.5 0 0 0 0 1h6a.5.5 0 0 0 0-1h-6Z"
		fill="currentColor"
	/>
	<rect
		x="4.881"
		y="7.454"
		width="9"
		height="2"
		rx="1"
		transform="rotate(-30 4.88 7.454)"
		fill="currentColor"
	/>
	<rect x="14" y="12" width="2" height="3" rx="1" fill="currentColor" />
</svg>
