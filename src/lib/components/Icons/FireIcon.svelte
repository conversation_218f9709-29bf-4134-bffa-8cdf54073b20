<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', className)}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<path
		fill-rule="evenodd"
		clip-rule="evenodd"
		d="M8.777 20.406A3.624 3.624 0 0 1 8 18.168c0-2.087 3-8.5 4-6.223.425.97 1.213 1.862 1.977 2.73C15.01 15.845 16 16.968 16 18.168a3.63 3.63 0 0 1-.777 2.238C17.467 19.273 19 16.994 19 14.368c0-3.756-5-16.2-7-11.2-.72 1.801-1.83 3.27-2.956 4.59A8.39 8.39 0 0 1 8.5 6.623C7.625 4.315 5 11.398 5 13.807c0 .***********.3a4.213 4.213 0 0 0-.008.26c0 2.627 1.533 4.906 3.777 6.039Z"
		fill="currentColor"
	/>
	<path
		d="M14 19.279c0 1.043-.895 1.889-2 1.889s-2-.846-2-1.89c0-1.042 1.5-2.11 2-3.11s2 2.068 2 3.11Z"
		fill="currentColor"
	/>
</svg>
