<script lang="ts">
	interface Props {
		class?: string;
	}

	let { class: classFromProps = 'w-6 h-6' }: Props = $props();
</script>

<svg class={classFromProps} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
	<path
		fill="currentColor"
		d="m21 10.667-.039 4.654c-.004.517-.525.845-.938.591l-3.716-2.29c-.413-.254-.408-.9.01-1.163l1.199-.756c-1.097-1.884-3.379-3.18-6.016-3.18-3.706 0-6.71 2.56-6.71 5.715 0 .42-.401.762-.895.762S3 14.659 3 14.238C3 10.241 6.806 7 11.5 7c3.226 0 6.033 1.53 7.473 3.786l1.098-.692c.417-.263.933.056.929.573Z"
	/>
</svg>
