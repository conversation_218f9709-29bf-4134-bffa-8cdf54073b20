<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', className)}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<path
		d="M13.487 9.18a2 2 0 0 0 1.548 1.209l.139.016 3.86.31-2.941 2.518a2 2 0 0 0-.672 1.847l.027.137.899 3.766-3.305-2.018a2 2 0 0 0-1.963-.069l-.122.069-3.305 2.018.899-3.766a2 2 0 0 0-.644-1.984l-2.941-2.518 3.86-.31a2 2 0 0 0 1.628-1.098l.059-.127L12 5.604l1.487 3.576Z"
		stroke="currentColor"
		stroke-width="2"
	/>
</svg>
