<script lang="ts">
	let { ...props } = $props();
</script>

<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" {...props}>
	<path
		stroke="currentColor"
		stroke-linejoin="round"
		d="M12 21.5A7.5 7.5 0 0 1 4.5 14v-1.5h15V14a7.5 7.5 0 0 1-7.5 7.5Z"
	/>
	<path
		class="left-button"
		fill="currentColor"
		fill-rule="evenodd"
		d="M11 2.062V10H4a8.001 8.001 0 0 1 7-7.938Z"
		clip-rule="evenodd"
	/>
	<path
		stroke="currentColor"
		stroke-linejoin="round"
		d="M19.484 9.5A7.504 7.504 0 0 0 13.5 2.65V9.5h5.984Z"
	/>
</svg>

<style>
	@keyframes scale {
		80% {
			transform: scale(1);
		}
		90% {
			transform: scale(0.9);
		}
		100% {
			transform: scale(1);
		}
	}

	.left-button {
		transform-origin: center center;
		animation: scale 2000ms ease-in-out forwards;
		animation-iteration-count: infinite;
	}
</style>
