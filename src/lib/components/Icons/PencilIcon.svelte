<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', className)}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<path
		d="m14.121 7.05 2.829 2.828-9.9 9.9H4.222V16.95l9.9-9.9Zm3.536-3.536a1 1 0 0 1 1.414 0l1.414 1.415a1 1 0 0 1 0 1.414l-2.12 2.121-2.83-2.828 2.122-2.122Z"
		fill="currentColor"
	/>
</svg>
