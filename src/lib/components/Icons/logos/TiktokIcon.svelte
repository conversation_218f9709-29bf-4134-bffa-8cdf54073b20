<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', className)}
	width="17"
	height="18"
	viewBox="0 0 17 18"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<path
		fill-rule="evenodd"
		clip-rule="evenodd"
		d="M9.522.806h2.29a4.259 4.259 0 0 0 1.908 2.789l.001.001a4.397 4.397 0 0 0 2.369.688v3.057a7.556 7.556 0 0 1-4.352-1.368v6.212c0 3.103-2.582 5.627-5.757 5.627a5.815 5.815 0 0 1-3.3-1.02l-.001-.002C1.196 15.773.224 14.089.224 12.186c0-3.103 2.582-5.627 5.757-5.627.263 0 .522.021.776.055v3.12a2.646 2.646 0 0 0-.776-.119c-1.45 0-2.63 1.153-2.63 2.57 0 .987.573 1.844 1.41 2.275.365.187.78.295 1.22.295 1.416 0 2.572-1.101 2.624-2.473L8.61.03h3.128c0 .265.026.524.074.775h-2.29Z"
		fill="currentColor"
	/>
</svg>
