<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	const { class: classFromProps, ...props }: Props = $props();
</script>

<svg
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	class={cn('size-full', classFromProps)}
	{...props}
>
	<rect x="6" y="11" width="2" height="8" rx="1" class="fill-current" />
	<rect x="11" y="4" width="2" height="15" rx="1" class="fill-current" />
	<rect x="16" y="8" width="2" height="11" rx="1" class="fill-current" />
</svg>
