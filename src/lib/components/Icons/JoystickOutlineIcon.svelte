<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', className)}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<circle cx="17" cy="11" r="1" fill="currentColor" />
	<circle cx="15" cy="13" r="1" fill="currentColor" />
	<rect x="7.333" y="10" width="1.333" height="4" rx=".667" fill="currentColor" />
	<rect
		x="6"
		y="12.667"
		width="1.333"
		height="4"
		rx=".667"
		transform="rotate(-90 6 12.667)"
		fill="currentColor"
	/>
	<rect x="4" y="7" width="16" height="10" rx="2" stroke="currentColor" stroke-width="2" />
</svg>
