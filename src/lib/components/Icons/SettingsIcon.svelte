<script lang="ts">
	let { class: classFromProps = 'size-full' } = $props();
</script>

<svg class={classFromProps} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
	<path
		fill="currentColor"
		fill-rule="evenodd"
		d="M14 4.312C14 3.587 13.413 3 12.688 3h-1.376C10.587 3 10 3.587 10 4.312v.257c0 .428-.287.8-.682.963-.396.164-.856.102-1.158-.2l-.182-.183a1.312 1.312 0 0 0-1.855 0l-.974.974a1.312 1.312 0 0 0 0 1.855l.182.182c.303.302.365.763.201 1.158-.164.395-.535.682-.963.682h-.257C3.587 10 3 10.587 3 11.312v1.376C3 13.413 3.587 14 4.312 14h.257c.428 0 .8.287.963.682.164.396.102.856-.2 1.158l-.183.182a1.312 1.312 0 0 0 0 1.855l.974.974a1.312 1.312 0 0 0 1.855 0l.182-.182c.302-.303.762-.365 1.158-.201.395.164.682.535.682.963v.257c0 .725.587 1.312 1.312 1.312h1.376c.725 0 1.312-.587 1.312-1.312v-.257c0-.428.287-.8.682-.963.396-.164.856-.102 1.158.2l.182.183a1.312 1.312 0 0 0 1.855 0l.974-.974a1.312 1.312 0 0 0 0-1.855l-.182-.182c-.303-.302-.365-.762-.201-1.158.164-.395.535-.682.963-.682h.257c.725 0 1.312-.587 1.312-1.312v-1.376c0-.725-.587-1.312-1.312-1.312h-.257c-.428 0-.8-.287-.963-.682-.164-.396-.102-.856.2-1.158l.183-.182a1.312 1.312 0 0 0 0-1.855l-.974-.974a1.312 1.312 0 0 0-1.855 0l-.182.182c-.302.303-.762.365-1.158.201-.395-.164-.682-.535-.682-.963v-.257ZM12 16a4 4 0 1 0 0-8 4 4 0 0 0 0 8Z"
		clip-rule="evenodd"
	/>
</svg>
