<script lang="ts">
	import { cn } from '$lib/util/cn';

	let { muted = false, class: classFromProps = '' } = $props();
</script>

<svg
	class={cn('size-full', classFromProps)}
	class:muted
	xmlns="http://www.w3.org/2000/svg"
	viewBox="0 0 24 24"
	fill="transparent"
>
	<g class="speaker-with-sound">
		<path
			class="outer-sound"
			stroke="currentColor"
			stroke-linecap="round"
			d="M6 16a4 4 0 0 1 0-8"
		/>
		<path
			class="inner-sound"
			stroke="currentColor"
			stroke-linecap="round"
			d="M6 14a2 2 0 1 1 0-4"
		/>
		<path
			fill="currentColor"
			d="M16 13.374v-2.748c0-.632-.543-1.145-1.212-1.145H12.8L9.988 7.268C9.198 6.646 8 7.176 8 8.148v7.705c0 .97 1.199 1.5 1.988.88l2.812-2.214h1.988c.67 0 1.212-.513 1.212-1.145Z"
		/>
	</g>
	<path
		class="muted-line text-base-100"
		stroke="currentColor"
		stroke-linecap="round"
		stroke-width="3"
		d="M17 6 5.686 17.314"
	/>
	<path class="muted-line" stroke="currentColor" stroke-linecap="round" d="M17 6 5.686 17.314" />
</svg>

<style>
	.outer-sound {
		transition: stroke-dashoffset 300ms ease-in-out;
		stroke-dashoffset: 0;
		stroke-dasharray: 18;
	}

	.muted .outer-sound {
		stroke-dashoffset: 18;
	}

	.inner-sound {
		transition: stroke-dashoffset 300ms ease-in-out 100ms;
		stroke-dashoffset: 0;
		stroke-dasharray: 15;
	}

	.muted .inner-sound {
		stroke-dashoffset: 15;
	}

	.muted-line {
		transition: stroke-dashoffset 300ms ease-in-out;
		stroke-dasharray: 20;
		stroke-dashoffset: 20;
	}

	.muted .muted-line {
		stroke-dashoffset: 0;
		transition-delay: 300ms;
	}

	/* Speaker with sound */

	.speaker-with-sound {
		transform: translateX(3px);
		transition: transform 300ms ease-out 300ms;
	}

	.muted .speaker-with-sound {
		transform: translateX(0px);
	}
</style>
