<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', className)}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<path
		d="M18 6a3 3 0 0 1 3 3v6a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3V9a3 3 0 0 1 3-3h12ZM8 10a.667.667 0 0 0-.667.667v.666h-.666a.667.667 0 0 0 0 1.334h.666v.666a.667.667 0 0 0 1.334 0v-.666h.666a.667.667 0 0 0 0-1.334h-.666v-.666A.667.667 0 0 0 8 10Zm7 2a1 1 0 1 0 0 2 1 1 0 0 0 0-2Zm2-2a1 1 0 1 0 0 2 1 1 0 0 0 0-2Z"
		fill="currentColor"
	/>
</svg>
