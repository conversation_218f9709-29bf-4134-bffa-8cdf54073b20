<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	const { class: classFromProps, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', classFromProps)}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<path
		d="M7.28605 13.1785C6.63518 13.1785 6.10754 12.6509 6.10754 12C6.10754 11.3492 6.63518 10.8215 7.28605 10.8215H16.7141C17.3649 10.8215 17.8926 11.3492 17.8926 12C17.8926 12.6509 17.3649 13.1785 16.7141 13.1785H7.28605Z"
		fill="currentColor"
	/>
</svg>
