<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', className)}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<path
		d="M13.786 5.023a1.8 1.8 0 1 0-3.573 0A7.203 7.203 0 0 0 4.8 12v4.5h-.9a.9.9 0 1 0 0 1.8h5.4a2.7 2.7 0 1 0 5.4 0h5.4a.9.9 0 1 0 0-1.8h-.9V12a7.203 7.203 0 0 0-5.414-6.977Z"
		fill="currentColor"
	/>
</svg>
