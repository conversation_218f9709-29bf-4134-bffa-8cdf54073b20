<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', className)}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<path d="M7 8h10v10a2 2 0 0 1-2 2H9a2 2 0 0 1-2-2V8Z" fill="currentColor" />
	<path d="M7 8h10v10a2 2 0 0 1-2 2H9a2 2 0 0 1-2-2V8Z" fill="currentColor" />
	<rect x="6" y="5" width="12" height="2" rx="1" fill="currentColor" />
	<rect x="6" y="5" width="12" height="2" rx="1" fill="currentColor" />
	<rect x="9" y="4" width="6" height="2" rx="1" fill="currentColor" />
	<rect x="9" y="4" width="6" height="2" rx="1" fill="currentColor" />
</svg>
