<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	const { class: classFromProps, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', classFromProps)}
	xmlns="http://www.w3.org/2000/svg"
	viewBox="0 0 24 24"
	fill="none"
	{...props}
>
	<path
		d="M6.727 15C5.221 15 4 13.76 4 12.23V5.77C4 4.24 5.221 3 6.727 3h4.546C12.779 3 14 4.24 14 5.77"
		stroke="currentColor"
		stroke-width="2"
	/>
	<rect x="9" y="8" width="10" height="12" rx="2" stroke="currentColor" stroke-width="2" />
</svg>
