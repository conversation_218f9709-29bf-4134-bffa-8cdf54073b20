<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', className)}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<path
		fill-rule="evenodd"
		clip-rule="evenodd"
		d="M17 6H7a2 2 0 0 0-2 2v8a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2ZM7 4a4 4 0 0 0-4 4v8a4 4 0 0 0 4 4h10a4 4 0 0 0 4-4V8a4 4 0 0 0-4-4H7Z"
		fill="currentColor"
	/>
	<path
		d="m6.086 12.914-1.5 1.5A2 2 0 0 0 4 15.828V16a2 2 0 0 0 2 2h12.44a1.06 1.06 0 0 0 .75-1.81l-1.776-1.776a2 2 0 0 0-2.828 0l-.672.672a2 2 0 0 1-2.828 0l-2.172-2.172a2 2 0 0 0-2.828 0Z"
		fill="currentColor"
		stroke="currentColor"
	/>
	<circle cx="16.5" cy="8.5" r="1.5" fill="currentColor" />
</svg>
