<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', className)}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<path d="M4 5h16M4 12h16" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
	<path
		d="M20.5 16.134a1 1 0 0 1 0 1.732l-3 1.732a1 1 0 0 1-1.5-.866v-3.464a1 1 0 0 1 1.5-.866l3 1.732Z"
		fill="currentColor"
	/>
	<path d="M4 19h9" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
</svg>
