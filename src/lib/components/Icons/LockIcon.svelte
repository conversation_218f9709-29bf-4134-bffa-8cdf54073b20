<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', className)}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<path
		fill-rule="evenodd"
		clip-rule="evenodd"
		d="M15 8v2H9V8a3 3 0 1 1 6 0Zm-9 2h1V8a5 5 0 0 1 10 0v2h1a1 1 0 0 1 1 1v9a1 1 0 0 1-1 1H6a1 1 0 0 1-1-1v-9a1 1 0 0 1 1-1Zm6 8a2 2 0 1 0 0-4 2 2 0 0 0 0 4Z"
		fill="currentColor"
	/>
</svg>
