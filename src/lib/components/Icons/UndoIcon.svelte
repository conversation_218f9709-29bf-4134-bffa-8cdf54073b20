<script lang="ts">
	interface Props {
		class?: string;
	}

	let { class: classFromProps = 'w-6 h-6' }: Props = $props();
</script>

<svg class={classFromProps} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
	<path
		fill="currentColor"
		d="m3 10.667.039 4.654c.004.517.526.845.938.591l3.717-2.29c.412-.254.407-.9-.01-1.163l-1.2-.756c1.097-1.884 3.379-3.18 6.016-3.18 3.706 0 6.71 2.56 6.71 5.715 0 .42.401.762.895.762s.895-.341.895-.762C21 10.241 17.194 7 12.5 7c-3.226 0-6.033 1.53-7.473 3.786l-1.098-.692c-.417-.263-.933.056-.929.573Z"
	/>
</svg>
