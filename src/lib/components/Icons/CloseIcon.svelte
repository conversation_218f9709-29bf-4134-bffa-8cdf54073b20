<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	const { class: classFromProps, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', classFromProps)}
	xmlns="http://www.w3.org/2000/svg"
	viewBox="0 0 24 24"
	{...props}
>
	<path
		fill="currentColor"
		d="M7.833 9.5A1.178 1.178 0 1 1 9.5 7.833l6.667 6.667a1.179 1.179 0 0 1-1.667 1.667L7.833 9.5Z"
	/>
	<path
		fill="currentColor"
		d="M14.5 7.833A1.179 1.179 0 1 1 16.167 9.5L9.5 16.167A1.179 1.179 0 1 1 7.833 14.5L14.5 7.833Z"
	/>
</svg>
