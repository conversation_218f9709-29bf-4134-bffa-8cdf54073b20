<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	const { class: classFromProps, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', classFromProps)}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<rect
		x="17.314"
		y="7"
		width="2"
		height="12"
		rx="1"
		transform="rotate(45 17.314 7)"
		fill="currentColor"
	/>
	<rect
		x="6"
		y="12.657"
		width="2"
		height="6"
		rx="1"
		transform="rotate(-45 6 12.657)"
		fill="currentColor"
	/>
</svg>
