<script lang="ts">
	import { cn } from '$lib/util/cn';

	interface Props {
		class?: string;
	}

	let { class: className, ...props }: Props = $props();
</script>

<svg
	class={cn('size-full', className)}
	viewBox="0 0 24 24"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	{...props}
>
	<path
		fill-rule="evenodd"
		clip-rule="evenodd"
		d="M11.895 14.572c-3.536.04-6.619 2.084-8.285 5.111-.338.614.142 1.317.842 1.317h7.222A6.973 6.973 0 0 1 11 18a6.97 6.97 0 0 1 .895-3.428Z"
		fill="currentColor"
	/>
	<circle cx="12" cy="8.143" r="5.143" fill="currentColor" />
	<path
		fill-rule="evenodd"
		clip-rule="evenodd"
		d="M19.111 13.729a.729.729 0 0 0-.729-.729h-.764a.729.729 0 0 0-.73.729v.143a.593.593 0 0 1-.378.535.587.587 0 0 1-.644-.112l-.1-.1a.729.729 0 0 0-1.031 0l-.54.54a.729.729 0 0 0 0 1.03l.1.101a.587.587 0 0 1 .112.644.593.593 0 0 1-.536.379h-.142a.729.729 0 0 0-.729.729v.764c0 .403.326.73.729.73h.143c.237 0 .444.159.535.378a.587.587 0 0 1-.112.644l-.1.1a.729.729 0 0 0 0 1.031l.54.54a.729.729 0 0 0 1.03 0l.101-.1a.587.587 0 0 1 .644-.112c.22.091.379.298.379.535v.143c0 .403.326.729.729.729h.764a.729.729 0 0 0 .73-.729v-.143c0-.237.159-.444.378-.535a.587.587 0 0 1 .644.112l.1.1a.729.729 0 0 0 1.031 0l.54-.54a.729.729 0 0 0 0-1.03l-.1-.101a.587.587 0 0 1-.112-.644.593.593 0 0 1 .535-.379h.143a.729.729 0 0 0 .729-.729v-.764a.729.729 0 0 0-.729-.73h-.143a.593.593 0 0 1-.535-.378.587.587 0 0 1 .112-.644l.1-.1a.729.729 0 0 0 0-1.031l-.54-.54a.729.729 0 0 0-1.03 0l-.101.1a.587.587 0 0 1-.644.112.593.593 0 0 1-.379-.536v-.142ZM18 20.222a2.222 2.222 0 1 0 0-4.444 2.222 2.222 0 0 0 0 4.444Z"
		fill="currentColor"
	/>
</svg>
