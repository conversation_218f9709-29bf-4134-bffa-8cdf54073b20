<script lang="ts">
	let { ...props } = $props();
</script>

<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" {...props}>
	<rect
		width="13"
		height="8"
		x="4"
		y="17"
		stroke="currentColor"
		stroke-linejoin="round"
		stroke-width="2"
		rx="2"
		transform="rotate(-90 4 17)"
	/>
	<path
		fill="currentColor"
		fill-rule="evenodd"
		d="M5 18.953V19a3 3 0 0 0 3 3h10a3 3 0 0 0 3-3v-4a3 3 0 0 0-3-3h-4v2h4a1 1 0 0 1 1 1v4a1 1 0 0 1-1 1H8a1 1 0 0 1-1-1H5.6c-.204 0-.405-.016-.6-.047Z"
		clip-rule="evenodd"
	/>
	<path
		fill="currentColor"
		d="m20.96 9.396-.94 1.476c-.105.164-.35.172-.442.015l-.827-1.413c-.091-.157.04-.362.236-.369l.564-.02c-.003-.802-.532-1.634-1.443-2.12-1.279-.68-2.826-.418-3.455.587-.084.134-.29.17-.46.078-.171-.09-.242-.273-.158-.407.797-1.274 2.756-1.606 4.377-.743 1.114.594 1.777 1.598 1.825 2.58l.517-.017c.196-.007.311.189.206.353Z"
	/>
</svg>
