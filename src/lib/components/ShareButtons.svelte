<script lang="ts">
	// @ts-nocheck
	import { page } from '$app/state';
	import { Telegram, WhatsApp, Facebook, X, Email, Reddit } from 'svelte-share-buttons-component';

	type Size = 'sm' | 'md';

	const sizeToTextClass: Record<Size, string> = {
		sm: 'text-xl',
		md: 'text-2xl',
	};

	interface Props {
		url?: string;
		title: string;
		size?: Size;
	}

	let { url = page.url.toString(), title, size = 'md' }: Props = $props();

	let textClass = $derived(sizeToTextClass[size]);
</script>

<div class="flex-center w-full gap-4 flex-wrap">
	<Facebook class="flex-center rounded-full {textClass}" quote={title} {url} />
	<X
		class="flex-center rounded-full {textClass}"
		text={title}
		url={encodeURIComponent(url)}
		related="game,website"
		via="lofiandgames"
		hashtags="lofiandgames"
	/>
	<WhatsApp class="flex-center rounded-full {textClass}" text="{title} {url}" />
	<Telegram class="flex-center rounded-full {textClass}" text={title} {url} />
	<Email class="flex-center rounded-full {textClass}" subject={title} body="{title} {url}" />
	<Reddit class="flex-center rounded-full {textClass}" {title} {url} />
</div>
