<script lang="ts">
	import { fly } from 'svelte/transition';
	import type { Snippet } from 'svelte';

	interface Props {
		children: Snippet;
	}

	let { children }: Props = $props();

	const pageTransitionDuration = 300;
</script>

<div
	in:fly={{ y: 5, duration: pageTransitionDuration, delay: pageTransitionDuration }}
	out:fly={{ y: -5, duration: pageTransitionDuration }}
>
	{@render children()}
</div>
