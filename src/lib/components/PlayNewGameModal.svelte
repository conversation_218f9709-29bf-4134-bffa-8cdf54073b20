<script lang="ts">
	import type { Snippet } from 'svelte';

	interface Props {
		id?: string;
		onPlayNewGame?: any;
		onClose?: any;
		children?: Snippet;
	}

	let {
		id = 'new-game-modal',
		onPlayNewGame = () => {},
		onClose = () => {},
		children,
	}: Props = $props();
</script>

<input
	{id}
	type="checkbox"
	class="modal-toggle"
	onchange={(e) => {
		if (e.currentTarget?.checked === false) {
			onClose();
		}
	}}
/>
<label for={id} class="modal cursor-pointer">
	<label class="modal-box relative" for="">
		<h3 class="text-lg font-bold">Play a new game?</h3>
		<p class="py-4">The current game progress will be lost</p>
		{@render children?.()}
		<div class="modal-action">
			<label for={id} class="btn btn-ghost">Cancel</label>
			<!-- svelte-ignore a11y_click_events_have_key_events -->
			<!-- svelte-ignore a11y_no_noninteractive_element_interactions -->
			<label onclick={onPlayNewGame} for={id} class="btn btn-primary">Play a new game</label>
		</div>
	</label>
</label>
