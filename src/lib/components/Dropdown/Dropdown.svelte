<script lang="ts">
	import type { Snippet } from 'svelte';
	import { twMerge } from 'tailwind-merge';
	import Backdrop from '../Backdrop.svelte';
	import { clickOutside } from '$lib/actions/clickOutside.svelte';

	interface Props {
		open?: boolean;
		class?: string;
		noBackdrop?: boolean;
		closeStrategy?: 'backdrop-click' | 'click-outside';
		children: Snippet;
	}

	let {
		open = $bindable(false),
		class: classFromProps = '',
		noBackdrop = false,
		closeStrategy = 'click-outside',
		children,
	}: Props = $props();
</script>

{#if open}
	{#if !noBackdrop}
		<Backdrop
			onclose={(event) => {
				if (closeStrategy === 'backdrop-click') {
					event.stopPropagation();
					open = false;
				}
			}}
		/>
	{/if}
{/if}

<details
	use:clickOutside
	onclickoutside={(event) => {
		if (open && closeStrategy === 'click-outside') {
			event.stopPropagation();
			open = false;
		}
	}}
	class={twMerge('dropdown', classFromProps)}
	bind:open
>
	{@render children()}
</details>
