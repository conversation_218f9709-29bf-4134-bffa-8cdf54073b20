<script lang="ts">
	import { cn } from '$lib/util/cn';
	import type { Snippet } from 'svelte';

	interface Props {
		class?: string;
		menu?: boolean;
		mobileVariant?: 'default' | 'screen-centered' | 'screen-fill';
		mobileVariantBreakpoints?: {
			position: 'xs' | 'sm';
			size: 'xs' | 'sm';
		};
		children: Snippet;
		ontouchmove?: (event: TouchEvent) => void;
	}

	let {
		class: classFromProps = '',
		mobileVariant,
		menu = false,
		mobileVariantBreakpoints = {
			position: 'xs',
			size: 'xs',
		},
		children,
		...props
	}: Props = $props();

	const positionClasses = {
		xs: 'fixed! left-1/2 -translate-x-1/2 xs:absolute! xs:left-auto xs:translate-x-0',
		sm: 'fixed! left-1/2 -translate-x-1/2 sm:absolute! sm:left-auto sm:translate-x-0',
	};
	const sizeClasses = {
		xs: 'w-[calc(100vw-32px)] xs:w-96 max-h-[calc(100vh-4rem)] overflow-auto',
		sm: 'w-[calc(100vw-32px)] sm:w-96 max-h-[calc(100vh-4rem)] overflow-auto',
	};
</script>

<ul
	class={cn(
		'dropdown-content bg-base-100! rounded-box z-40 w-52 p-4 shadow-sm my-1',
		{
			'menu p-2 w-32': menu,
			[positionClasses[mobileVariantBreakpoints.position]]:
				mobileVariant === 'screen-centered' || mobileVariant === 'screen-fill',
			[sizeClasses[mobileVariantBreakpoints.size]]: mobileVariant === 'screen-fill',
		},
		classFromProps,
	)}
	{...props}
>
	{@render children()}
</ul>
