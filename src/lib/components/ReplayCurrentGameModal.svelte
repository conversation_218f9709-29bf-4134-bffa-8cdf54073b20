<script lang="ts">
	interface Props {
		id?: string;
		onReplay?: any;
	}

	let { id = 'replay-game-modal', onReplay = () => {} }: Props = $props();
</script>

<input type="checkbox" {id} class="modal-toggle" />
<label for={id} class="modal cursor-pointer">
	<label class="modal-box relative" for="">
		<h3 class="text-lg font-bold">Replay current game?</h3>
		<p class="py-4">The current game progress will be lost</p>
		<div class="modal-action">
			<label for={id} class="btn btn-ghost">Cancel</label>
			<!-- svelte-ignore a11y_click_events_have_key_events -->
			<!-- svelte-ignore a11y_no_noninteractive_element_interactions -->
			<label onclick={onReplay} for={id} class="btn btn-primary">Replay current game</label>
		</div>
	</label>
</label>
