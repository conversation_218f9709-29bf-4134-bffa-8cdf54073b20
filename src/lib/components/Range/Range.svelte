<script lang="ts">
	import { twMerge } from 'tailwind-merge';

	interface Props {
		class?: string;
		id?: string;
		disabled?: boolean;
		onChange?: any;
		min?: number;
		max?: number;
		step?: number;
		value?: number;
		onpointerup?: () => void;
	}

	let {
		class: classFromProps = '',
		id = '',
		disabled = false,
		onChange = (event: Event) => {},
		min = 0,
		max = 100,
		step = 1,
		value = $bindable(50),
		onpointerup = () => {},
	}: Props = $props();
</script>

<input
	type="range"
	{id}
	{min}
	{max}
	{step}
	{disabled}
	bind:value
	onchange={onChange}
	{onpointerup}
	class={twMerge('range', classFromProps)}
/>
