<script lang="ts">
	import InfoIcon from './Icons/InfoIcon.svelte';
	import type { MouseEventHandler } from 'svelte/elements';
	import { cn } from '$lib/util/cn';
	import InfoSolidIcon from './Icons/InfoSolidIcon.svelte';

	interface Props {
		class?: string;
		iconClass?: string;
		onclick: MouseEventHandler<HTMLButtonElement>;
	}

	let { class: classFromProps = '', iconClass, onclick }: Props = $props();
</script>

<button {onclick} class={cn('btn btn-sm', classFromProps)} aria-label="show info">
	<InfoSolidIcon class={cn('size-5', iconClass)} />
</button>
