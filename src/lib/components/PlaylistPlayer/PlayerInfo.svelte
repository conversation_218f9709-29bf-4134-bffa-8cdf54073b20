<script lang="ts">
	import { musicPlayer } from '$lib/stores/musicPlayer.svelte';
	import { siteSounds } from '$lib/stores/siteSounds.svelte';
	import { cn } from '$lib/util/cn';
	import PlayerAttribution from './PlayerAttribution.svelte';

	interface Props {
		albumWrapperClass?: string;
	}

	let { albumWrapperClass = '' }: Props = $props();
</script>

<div class="flex items-center gap-4 py-2 px-1 transition-opacity">
	<a
		target="_blank"
		rel="noreferrer noopener"
		href={musicPlayer.currentSong?.attribution.album.url}
		class={cn('size-14 shrink-0', albumWrapperClass)}
		aria-label={musicPlayer.currentSong?.attribution.album.name ??
			musicPlayer.currentSong?.attribution.work.name ??
			''}
	>
		{#if siteSounds.changeRadioStation.playing()}
			<video
				autoplay
				loop
				muted
				playsinline
				class="size-full rounded-sm"
				src="https://static.lofiandgames.com/videos/static.mp4"
			></video>
		{:else if musicPlayer.currentSong?.attribution.album.picture}
			<div
				style={`background-image: url("${musicPlayer.currentSong?.attribution.album.picture}")`}
				class="size-full bg-base-200 rounded-sm bg-cover bg-center dark:bg-base-100"
			></div>
		{/if}
	</a>

	<PlayerAttribution attribution={musicPlayer.currentSong?.attribution} />
</div>
