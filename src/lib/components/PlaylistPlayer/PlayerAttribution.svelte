<script lang="ts">
	import type { Attribution } from '$lib/models/Attribution';
	import { musicPlayer } from '$lib/stores/musicPlayer.svelte';
	import { siteSounds } from '$lib/stores/siteSounds.svelte';
	import PlaylistButton from './MusicOptionsButton.svelte';

	interface Props {
		class?: string;
		attribution: Attribution | null | undefined;
	}

	let { class: classFromProps = '', attribution }: Props = $props();
</script>

<div class="flex w-full flex-col {classFromProps}">
	<a
		class="text-lg font-semibold leading-5 line-clamp-3"
		target="_blank"
		rel="noreferrer noopener"
		href={attribution?.work.url}
	>
		{siteSounds.changeRadioStation.playing()
			? 'Tuning to lofi radio station...'
			: (attribution?.work.name ?? 'No music')}
	</a>
	<a
		class="text-sm font-normal line-clamp-2"
		target="_blank"
		rel="noreferrer noopener"
		href={attribution?.creator.url}
	>
		{siteSounds.changeRadioStation.playing() ? '...' : (attribution?.creator.name ?? 'No artist')}
	</a>

	{#if musicPlayer.player === 'playlist'}
		<div class="flex w-full gap-1 justify-between items-center">
			<a
				class="text-xs font-light line-clamp-1"
				target="_blank"
				rel="noreferrer noopener"
				href={attribution?.license?.url}
			>
				{attribution?.license?.name ?? ''}
			</a>

			<PlaylistButton />
		</div>
	{/if}
</div>
