<script lang="ts">
	import { dmca } from '$lib/components/DMCA/DmcaBodyOpacity/dmca.svelte';
	import { TS } from '$lib/components/DMCA/DmcaBodyOpacity/TextShuffler';

	let opacity = 1;
	let interval = 1000;
	let si = setInterval;
	let ci = clearInterval;

	$effect(() => {
		if (dmca?.f) {
			const id = si(() => {
				if (opacity > 0) {
					opacity -= 0.01;
				}
				// document.body.style.opacity = ...
				(document as any)[TS.u('foet')][TS.u('0ntmd')][TS.u('o3zq5nt')] = `${opacity}`;
			}, interval);

			return () => ci(id);
		}
	});
</script>
