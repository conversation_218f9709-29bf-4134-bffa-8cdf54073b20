/**
 * Get all combinations of the arrays
 * @example
 * getAllCombinations([1, 2], ['a', 'b', 'c'])
 * // returns [[1, 'a'], [1, 'b'], [1, 'c'], [2, 'a'], [2, 'b'], [2, 'c']]
 */
export function getAllCombinations<T extends unknown[][]>(
	...arrays: T
): Array<Array<T[number][number]>> {
	// Handle edge cases
	if (arrays.length === 0) return [];
	if (arrays.length === 1) return arrays[0].map((item) => [item]);

	// Start with the first array's elements as initial combinations
	let result = arrays[0].map((item) => [item]);

	// For each subsequent array, create new combinations with existing ones
	for (let i = 1; i < arrays.length; i++) {
		const currentArray = arrays[i];
		const tempResult = [];

		// Combine each existing combination with each element in the current array
		for (const combination of result) {
			for (const item of currentArray) {
				tempResult.push([...combination, item]);
			}
		}

		result = tempResult;
	}

	return result;
}

/**
 * Get all combinations based on the possibilities object
 * @example
 * getAllCombinationsFrom({ num: [1, 2], char : ['a', 'b', 'c'] })
 * returns [{ num: 1, char: 'a' }, { num: 1, char: 'b' }, { num: 1, char: 'c' }, { num: 2, char: 'a' }, { num: 2, char: 'b' }, { num: 2, char: 'c' }]
 */
export function getAllCombinationsFrom<T extends Record<string, unknown[]>>(
	possibilities: T,
): Array<{
	[K in keyof T]: T[K][number];
}> {
	// Get all keys from the possibilities object
	const keys = Object.keys(possibilities) as Array<keyof T>;

	// Handle edge case
	if (keys.length === 0) return [];

	// Extract arrays from the possibilities object
	const arrays = keys.map((key) => possibilities[key]);

	// Get all combinations of the arrays
	const combinations = getAllCombinations(...arrays);

	// Map the combinations to objects with the original keys
	return combinations.map((combination) => {
		const result = {} as { [K in keyof T]: T[K][number] };
		keys.forEach((key, index) => {
			result[key] = combination[index];
		});
		return result;
	});
}
