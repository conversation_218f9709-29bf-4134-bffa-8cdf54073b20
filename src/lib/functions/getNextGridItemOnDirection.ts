import type { GridItem } from '$lib/models/GridItem';
import type { Direction } from '$lib/util/DirectionListener';

export type DiagonalDirection = 'up-right' | 'up-left' | 'down-right' | 'down-left';

export type AnyDirection = Direction | DiagonalDirection;

export const getNextGridItemOnDirection = (
	{ row, column }: GridItem,
	direction: AnyDirection,
	gridSize: GridItem,
): GridItem | null => {
	let item: GridItem | null = null;

	if (direction === 'up') {
		item = { row: row - 1, column };
	}

	if (direction === 'down') {
		item = { row: row + 1, column };
	}

	if (direction === 'left') {
		item = { row, column: column - 1 };
	}

	if (direction === 'right') {
		item = { row, column: column + 1 };
	}

	if (direction === 'up-right') {
		item = { row: row - 1, column: column + 1 };
	}

	if (direction === 'up-left') {
		item = { row: row - 1, column: column - 1 };
	}

	if (direction === 'down-right') {
		item = { row: row + 1, column: column + 1 };
	}

	if (direction === 'down-left') {
		item = { row: row + 1, column: column - 1 };
	}

	if (item === null) {
		return null;
	}

	if (
		item.row < 0 ||
		item.column < 0 ||
		item.row >= gridSize.row ||
		item.column >= gridSize.column
	) {
		return null;
	}

	return item;
};
