/** @see https://stackoverflow.com/questions/43566019/how-to-choose-a-weighted-random-array-element-in-javascript */
export function getRandomItemWithWeightsAt<T>(
	items: T[],
	weights: number[],
	randomFunc = Math.random,
) {
	let i;
	weights = [...weights];

	for (i = 1; i < weights.length; i++) weights[i] += weights[i - 1];

	let random = randomFunc() * weights[weights.length - 1];

	for (i = 0; i < weights.length; i++) if (weights[i] > random) break;

	return items[i];
}
