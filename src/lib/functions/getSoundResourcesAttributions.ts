import type { GameSoundResource } from '$lib/data/gameSounds';
import type { Attribution } from '$lib/models/Attribution';
import { getUniqueAttributions } from './getUniqueAttributions';

export const getSoundResourcesAttributions = (
	soundResources: Record<string, GameSoundResource>,
): Attribution[] => {
	const attributions = Object.values(soundResources)
		.map((resource) => resource.attribution)
		.filter(Boolean) as Attribution[];

	return getUniqueAttributions(attributions);
};
