export function get2DGrid<T>(size: number): Array<Array<T>>;
export function get2DGrid<T>(
	rows: number,
	columns: number,
	fill?: (row: number, column: number) => T | null,
): Array<Array<T>>;

export function get2DGrid<T>(
	size: number,
	columns?: number,
	fill?: (row: number, column: number) => T | null,
): Array<Array<T | null>> {
	return Array(size)
		.fill(null)
		.map((_, row) =>
			Array(columns ?? size)
				.fill(null)
				.map((_, column) => fill?.(row, column) ?? null),
		);
}
