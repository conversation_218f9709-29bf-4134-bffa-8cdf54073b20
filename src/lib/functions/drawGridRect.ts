import type { Point2D } from '$lib/models/Point2D';

export type DrawGridRectOptions = {
	row: number;
	column: number;
	color: string;
	context: CanvasRenderingContext2D;
	gridItemSize: number;
	gridItemMargin?: number;
	gridStart?: Point2D;
	scale?: Point2D;
	translate?: Point2D;
	rotateAngle?: number;
	opacity?: number;
};

export const drawGridRect = ({
	color,
	column,
	context,
	gridItemSize,
	row,
	gridStart = { x: 0, y: 0 },
	gridItemMargin = 0,
	scale = { x: 1, y: 1 },
	translate = { x: 0, y: 0 },
	rotateAngle = 0,
	opacity = 1,
}: DrawGridRectOptions) => {
	const size = gridItemSize - 2 * gridItemMargin;
	const halfSize = size / 2;

	context.fillStyle = color;
	context.save();
	context.translate(
		gridStart.x + column * gridItemSize + gridItemMargin + halfSize,
		gridStart.y + row * gridItemSize + gridItemMargin + halfSize,
	);
	context.translate(translate.x, translate.y);
	context.scale(scale.x, scale.y);
	context.rotate(rotateAngle);
	context.globalAlpha = opacity;

	context.fillRect(-halfSize, -halfSize, size, size);

	context.restore();
};
