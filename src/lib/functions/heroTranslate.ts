/**
 * Same as the original crossfade function, but without the opacity
 * @see https://github.com/sveltejs/svelte/blob/master/src/runtime/transition/index.ts
 */

import type { EasingFunction, TransitionConfig } from 'svelte/transition';
import { cubicOut } from 'svelte/easing';

export interface heroTranslateParams {
	delay?: number;
	duration?: number | ((len: number) => number);
	easing?: EasingFunction;
}

type ClientRectMap = Map<any, { rect: ClientRect }>;

export function heroTranslate({
	fallback,
	...defaults
}: heroTranslateParams & {
	fallback?: (node: Element, params: heroTranslateParams, intro: boolean) => TransitionConfig;
}): [
	(
		node: Element,
		params: heroTranslateParams & {
			key: any;
		},
	) => () => TransitionConfig,
	(
		node: Element,
		params: heroTranslateParams & {
			key: any;
		},
	) => () => TransitionConfig,
] {
	const to_receive: ClientRectMap = new Map();
	const to_send: ClientRectMap = new Map();

	function heroTranslate(
		from: DOMRect,
		node: Element,
		params: heroTranslateParams,
	): TransitionConfig {
		const {
			delay = 0,
			duration = (d: number) => Math.sqrt(d) * 30,
			easing = cubicOut,
		} = { ...defaults, ...params };

		const to = node.getBoundingClientRect();
		const dx = from.left - to.left;
		const dy = from.top - to.top;
		const dw = to.width === 0 || from.width === 0 ? 1 : from.width / to.width;
		const dh = to.height === 0 || from.height === 0 ? 1 : from.height / to.height;
		const d = Math.sqrt(dx * dx + dy * dy);

		const style = getComputedStyle(node);
		const transform = style.transform === 'none' ? '' : style.transform;

		return {
			delay,
			duration: typeof duration === 'function' ? duration(d) : duration,
			easing,
			css: (t, u) => `
				transform-origin: top left;
				transform: ${transform} translate(${u * dx}px,${u * dy}px) scale(${t + (1 - t) * dw}, ${
					t + (1 - t) * dh
				});
			`,
		};
	}

	function transition(items: ClientRectMap, counterparts: ClientRectMap, intro: boolean) {
		return (node: Element, params: heroTranslateParams & { key: any }) => {
			items.set(params.key, {
				rect: node.getBoundingClientRect(),
			});

			return () => {
				if (counterparts.has(params.key)) {
					const { rect } = counterparts.get(params.key) as any;
					counterparts.delete(params.key);

					return heroTranslate(rect, node, params);
				}

				// if the node is disappearing altogether
				// (i.e. wasn't claimed by the other list)
				// then we need to supply an outro
				items.delete(params.key);
				return fallback && fallback(node, params, intro);
			};
		};
	}

	return [transition(to_send, to_receive, false), transition(to_receive, to_send, true)] as any;
}
