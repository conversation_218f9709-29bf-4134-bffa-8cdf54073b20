import { getTimeUnits } from './getTimeUnits';

export const getTimeString = (timeInMs: number) => {
	const { hours, minutes, seconds } = getTimeUnits(timeInMs);

	const hoursString = hours.toString().padStart(2, '0');
	const minutesString = minutes.toString().padStart(2, '0');
	const secondsString = seconds.toString().padStart(2, '0');
	const days = Math.floor(hours / 24);
	const years = Math.floor(days / 365);

	if (years > 0) {
		const remainingDays = days % 365;

		return `${years}y ${remainingDays}d`;
	}

	if (days > 0) {
		const remainingHours = hours % 24;

		return `${days}d ${remainingHours}h`;
	}

	if (hours) {
		return `${hoursString}:${minutesString}:${secondsString}`;
	}

	return `${minutesString}:${secondsString}`;
};
