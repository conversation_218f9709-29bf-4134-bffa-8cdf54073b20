export type CardSuit = 'heart' | 'diamond' | 'club' | 'spade';

export type CardValue = 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 'A' | 'J' | 'Q' | 'K';

export type CardFace = 'up' | 'down';

export type Card = {
	suit: CardSuit;
	value: CardValue;
	face: CardFace;
};

export const suits: CardSuit[] = ['club', 'diamond', 'heart', 'spade'];

export const values: CardValue[] = [2, 3, 4, 5, 6, 7, 8, 9, 10, 'J', 'Q', 'K', 'A'];

export type SuitColor = 'red' | 'black';

export const suitToColor: Record<CardSuit, SuitColor> = {
	club: 'black',
	diamond: 'red',
	heart: 'red',
	spade: 'black',
};
