<script lang="ts">
	import Navbar from '$lib/components/Navbar/Navbar.svelte';
	import Footer from '$lib/components/Footer.svelte';
	import PageTransition from '$lib/components/PageTransition.svelte';
	import type { Snippet } from 'svelte';
	interface Props {
		children: Snippet;
	}

	let { children }: Props = $props();
</script>

<PageTransition>
	<Navbar />

	{@render children()}

	<Footer />
</PageTransition>
