<script lang="ts">
	import { cn } from '$lib/util/cn';
	import type { Snippet } from 'svelte';
	import AuthBackground from './AuthBackground.svelte';

	interface Props {
		children: Snippet;
		image?: string;
		cardClass?: string;
		contentClass?: string;
	}

	let { children, image, cardClass = '', contentClass = '' }: Props = $props();
</script>

<AuthBackground />

<div class="relative flex items-center justify-center min-h-screen-no-navbar p-4 pb-10">
	<div
		class={cn(
			'card w-full bg-base-100 shadow-2xl max-w-md rounded-2xl size-full grow',
			{
				'lg:grid lg:grid-cols-2 lg:max-w-4xl': image,
			},
			cardClass,
		)}
	>
		<div
			class={cn(
				'relative flex items-start justify-center max-w-[inherit] size-full p-8 grow',
				{
					'lg:p-12 lg:min-h-[596px]': image,
				},
				contentClass,
			)}
		>
			{@render children()}
		</div>

		{#if image}
			<div
				class="bg-base-300 bg-cover size-full rounded-tr-2xl rounded-br-2xl hidden lg:block bg-center"
				style="background-image: url({image})"
			></div>
		{/if}
	</div>
</div>
