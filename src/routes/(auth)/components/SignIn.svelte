<script lang="ts">
	import { page } from '$app/state';
	import { authClient } from '$lib/auth/client';
	import { cn } from '$lib/util/cn';
	import capitalize from 'lodash/capitalize';
	import SocialLoginButton from './SocialLoginButton.svelte';
	import Turnstile, { type TurnstileHandler } from './Turnstile.svelte';
	import { toast } from 'svelte-sonner';
	import { fly } from 'svelte/transition';
	import { quadInOut } from 'svelte/easing';
	import { goto } from '$app/navigation';

	interface Props {
		class?: string;
		email: string;
	}

	let { email = $bindable(), class: className }: Props = $props();

	const session = authClient.useSession();

	let password = $state('');
	let turnstileToken = $state('');
	let turnstileHandler = $state() as TurnstileHandler;
	let isLoading = $state(false);
	let error = $state(null) as { code?: string; message?: string } | null;

	function handleKeyDown(event: KeyboardEvent) {
		if (event.key === 'Enter') {
			signIn();
		}
	}

	async function signInWithGoogle() {
		if (!turnstileToken || isLoading) {
			return;
		}

		error = null;
		isLoading = true;

		const { error: apiError } = await authClient.signIn.social({
			provider: 'google',
			callbackURL: `${page.url.origin}`,
			fetchOptions: {
				credentials: 'include',
				headers: {
					'x-captcha-response': turnstileToken,
				},
			},
		});

		error = apiError;
		isLoading = false;

		if (error) {
			turnstileHandler.reset();

			toast.error(
				capitalize(error?.message ?? 'Oops, something went wrong. Please try again later'),
				{
					duration: 5_000,
				},
			);
		}
	}

	async function signIn() {
		if (!turnstileToken || isLoading) {
			return;
		}

		error = null;
		isLoading = true;

		if (email.includes('@')) {
			const { error: apiError } = await authClient.signIn.email({
				email,
				password,
				fetchOptions: {
					credentials: 'include',
					headers: {
						'x-captcha-response': turnstileToken,
					},
				},
			});

			error = apiError;
		} else {
			const { error: apiError } = await authClient.signIn.username({
				username: email,
				password,
				fetchOptions: {
					credentials: 'include',
					headers: {
						'x-captcha-response': turnstileToken,
					},
				},
			});

			error = apiError;
		}

		isLoading = false;

		if (error) {
			turnstileHandler.reset();

			toast.error(
				capitalize(error?.message ?? 'Oops, something went wrong. Please try again later'),
				{
					duration: 5_000,
				},
			);
		} else {
			goto('/');
		}
	}
</script>

<div transition:fly={{ y: 25, duration: 300, easing: quadInOut }} class={cn('w-full', className)}>
	<div class="w-full flex flex-col items-center justify-center gap-2">
		<h2 class="text-3xl mb-4">Sign In</h2>

		<label class="label flex flex-col w-full">
			<span class="text-sm self-start">Email or username</span>

			<input
				class={cn('input input-bordered w-full', {
					'input-error': [
						'INVALID_EMAIL_OR_PASSWORD',
						'INVALID_USERNAME_OR_PASSWORD',
						'USERNAME_IS_TOO_SHORT',
						'USERNAME_IS_TOO_LONG',
						'USERNAME_IS_INVALID',
						'ACCOUNT_NOT_FOUND',
						'CREDENTIAL_ACCOUNT_NOT_FOUND',
						'USER_EMAIL_NOT_FOUND',
						'USER_NOT_FOUND',
					].includes($state.snapshot(error?.code) as any),
				})}
				disabled={isLoading || $session.isPending}
				type="email"
				placeholder="Email or username"
				tabindex={1}
				bind:value={email}
				onkeydown={handleKeyDown}
				autocomplete="email"
			/>
		</label>

		<label class="label flex flex-col w-full">
			<div class="flex items-center justify-between gap-2 w-full">
				<span class="text-sm">Password</span>
				<a class="text-sm link" tabindex={3} href="/forgot-password"> Forgot password? </a>
			</div>

			<input
				class={cn('input input-bordered w-full', {
					'input-error': [
						'INVALID_USERNAME_OR_PASSWORD',
						'INVALID_EMAIL_OR_PASSWORD',
						'INVALID_PASSWORD',
						'PASSWORD_TOO_LONG',
						'PASSWORD_TOO_SHORT',
					].includes($state.snapshot(error?.code) as any),
				})}
				disabled={isLoading || $session.isPending}
				type="password"
				placeholder="Password"
				tabindex={2}
				bind:value={password}
				onkeydown={handleKeyDown}
				autocomplete="current-password"
			/>
		</label>

		<Turnstile bind:token={turnstileToken} bind:this={turnstileHandler} />

		<button
			class="btn btn-primary w-full"
			disabled={!turnstileToken || isLoading || $session.isPending}
			onclick={signIn}
			tabindex={4}
		>
			{#if !turnstileToken || isLoading}
				<span class="loading loading-spinner"></span>
			{/if}

			Sign in
		</button>

		<div class="divider">or</div>

		<SocialLoginButton
			provider="google"
			variant="signIn"
			loading={!turnstileToken || isLoading || $session.isPending}
			onclick={signInWithGoogle}
		/>

		<div class="pt-4">
			Not a member? <a href="/signup" class="link">Create account</a>
		</div>
	</div>
</div>
