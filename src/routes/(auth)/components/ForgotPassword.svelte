<script lang="ts">
	import { cn } from '$lib/util/cn';
	import capitalize from 'lodash/capitalize';
	import Turnstile, { type TurnstileHandler } from './Turnstile.svelte';
	import EmailIcon from '$lib/components/Icons/EmailIcon.svelte';
	import { fly } from 'svelte/transition';
	import { quadInOut } from 'svelte/easing';
	import { authClient } from '$lib/auth/client';
	import { page } from '$app/state';
	import { toast } from 'svelte-sonner';

	interface Props {
		email: string;
	}

	let { email = $bindable() }: Props = $props();

	let turnstileToken = $state('');
	let turnstileHandler = $state() as TurnstileHandler;
	let isLoading = $state(false);
	let error = $state(null) as { code?: string; message?: string } | null;
	let success = $state(false);

	function handleKeyDown(event: KeyboardEvent) {
		if (event.key === 'Enter') {
			sendResetEmail();
		}
	}

	async function sendResetEmail() {
		if (!turnstileToken || isLoading) {
			return;
		}

		isLoading = true;
		error = null;

		const { error: apiError } = await authClient.forgetPassword({
			email,
			redirectTo: `${page.url.origin}/reset-password`,
			fetchOptions: {
				headers: {
					'x-captcha-response': turnstileToken,
				},
			},
		});

		isLoading = false;
		error = apiError;

		if (error) {
			turnstileHandler.reset();

			toast.error(
				error?.code === 'VALIDATION_ERROR'
					? 'Invalid email'
					: capitalize(error?.message ?? 'Oops, something went wrong. Please try again later'),
				{
					duration: 5_000,
				},
			);
		} else {
			success = true;
		}
	}
</script>

<div class="size-full min-h-[280px] flex items-center justify-center self-center">
	{#if success}
		<div
			transition:fly|global={{ y: 25, duration: 300, delay: 400, easing: quadInOut }}
			class="w-full flex flex-col items-center justify-center gap-1 absolute inset-0 text-center"
		>
			<EmailIcon class="size-14 mb-2" />

			<h2 class="text-3xl">All Done!</h2>

			<p class="text-lg">Check your email for a reset link</p>
		</div>
	{:else}
		<div
			transition:fly|global={{ y: -25, duration: 300, easing: quadInOut }}
			class="w-full flex flex-col items-center justify-center gap-2"
		>
			<h2 class="text-3xl mb-4">Forgot Password</h2>

			<label class="label flex flex-col items-start w-full">
				<span class="text-sm">Email</span>

				<input
					class={cn('input input-bordered w-full validator', {
						'input-error': [
							'VALIDATION_ERROR',
							'INVALID_EMAIL_OR_PASSWORD',
							'INVALID_USERNAME_OR_PASSWORD',
							'USERNAME_IS_TOO_SHORT',
							'USERNAME_IS_TOO_LONG',
							'USERNAME_IS_INVALID',
							'ACCOUNT_NOT_FOUND',
							'CREDENTIAL_ACCOUNT_NOT_FOUND',
							'USER_EMAIL_NOT_FOUND',
							'USER_NOT_FOUND',
						].includes($state.snapshot(error?.code) as any),
					})}
					disabled={isLoading}
					type="email"
					placeholder="Email"
					tabindex={1}
					bind:value={email}
					onkeydown={handleKeyDown}
					autocomplete="email"
				/>

				<div class="validator-hint self-start hidden mt-0">Enter a valid email address</div>
			</label>

			<Turnstile bind:token={turnstileToken} bind:this={turnstileHandler} />

			<button
				class="btn btn-primary w-full"
				disabled={!turnstileToken || isLoading}
				onclick={sendResetEmail}
			>
				{#if !turnstileToken || isLoading}
					<span class="loading loading-spinner"></span>
				{/if}

				Send reset instructions
			</button>

			<a class="link pt-4" href="/signin">Back to sign in</a>
		</div>
	{/if}
</div>
