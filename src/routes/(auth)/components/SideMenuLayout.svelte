<script lang="ts">
	import { fade } from 'svelte/transition';
	import { quadInOut } from 'svelte/easing';
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import { page } from '$app/state';
	import type { Component, Snippet } from 'svelte';

	type MenuItem = {
		text: string;
		href: string;
		icon: Component;
	};

	interface Props {
		menuItems: MenuItem[];
		isLoading?: boolean;
		children: Snippet;
	}

	let { menuItems, isLoading = false, children }: Props = $props();
	let open = $state(false);

	let activeMenuItem = $derived.by(() => {
		const url = page.url.pathname;

		return menuItems.find((item) => item.href === url);
	});
</script>

<div class="grow flex flex-col gap-4 justify-center items-start relative w-full self-stretch">
	<Dropdown bind:open>
		<DropdownButton class="flex sm:hidden">
			{#if activeMenuItem}
				<activeMenuItem.icon class="size-5" />
				{activeMenuItem.text}
			{/if}
		</DropdownButton>

		<DropdownContent menu class="w-48">
			{#each menuItems as item}
				<DropdownItem>
					<a
						href={item.href}
						onclick={() => (open = false)}
						class:menu-active={activeMenuItem?.href === item.href}
					>
						<item.icon class="size-5" />
						{item.text}
					</a>
				</DropdownItem>
			{/each}
		</DropdownContent>
	</Dropdown>

	<div class="flex size-full items-stretch justify-start grow">
		<ul class="menu menu-lg w-52 flex-nowrap sm:flex hidden">
			{#each menuItems as item}
				<li>
					<a href={item.href} class:menu-active={activeMenuItem?.href === item.href}>
						<item.icon class="size-5" />
						{item.text}
					</a>
				</li>
			{/each}
		</ul>

		<div class="w-full grow sm:pl-12">
			<div class="size-full relative">
				{#if isLoading}
					<div
						transition:fade|global={{ duration: 200, easing: quadInOut }}
						class="absolute inset-0 flex items-center justify-center h-full"
					>
						<div class="loading loading-spinner size-10"></div>
					</div>
				{:else}
					{@render children()}
				{/if}
			</div>
		</div>
	</div>
</div>
