<script lang="ts">
	import { theme } from '$lib/stores/theme.svelte';
	import { turnstileState } from '$lib/stores/turnstileState.svelte';
	import { untrack } from 'svelte';

	interface Props {
		token: string;
	}

	let { token = $bindable('') }: Props = $props();
	let element = $state() as HTMLElement;

	export interface TurnstileHandler {
		reset: () => void;
	}

	export function reset() {
		if (turnstileState.loaded) {
			token = '';
			turnstile.reset(element);
		}
	}

	$effect(() => {
		if (turnstileState.loaded) {
			untrack(() => {
				turnstile.render(element, {
					sitekey: import.meta.env.VITE_TURNSTILE_KEY,
					theme: theme.brightness,
					appearance: 'interaction-only',

					callback: function (newToken: string) {
						token = newToken;
					},
					'expired-callback': () => {
						turnstile.reset(element);
					},
					'error-callback': (error) => {
						if (error.includes('30030')) {
							turnstile.reset(element);
						}
					},
				});
			});
		}
	});
</script>

<div class="min-h-6" bind:this={element}></div>
