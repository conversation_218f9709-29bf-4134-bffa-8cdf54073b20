<div
	class="fixed inset-0 bg-linear-to-tr from-primary/15 to-secondary/15 dark:bg-linear-to-bl dark:from-primary/20 dark:to-secondary/20 size-full -z-10 animate-bg bg-[length:200%_200%]"
></div>

<style>
	@keyframes bg-animation {
		from {
			background-position: 0% 0%;
		}
		to {
			background-position: 100% 0%;
		}
	}

	.animate-bg {
		transform-origin: center;
		animation: bg-animation 5s ease-in-out infinite alternate;
	}
</style>
