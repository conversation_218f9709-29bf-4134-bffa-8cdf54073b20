<script lang="ts">
	import Navbar from '$lib/components/Navbar/Navbar.svelte';
	import PageTransition from '$lib/components/PageTransition.svelte';
	import { pickRandom } from '@thi.ng/random';
	import { MetaTags } from 'svelte-meta-tags';

	const images = [
		'https://media3.giphy.com/media/v1.Y2lkPTc5MGI3NjExMTJka2R5enhlanVkZ3ZydmxldWZtMms2cDhmdWgwNGttenIzaGdvdyZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/9MFsKQ8A6HCN2/giphy.gif',
		'https://media2.giphy.com/media/v1.Y2lkPTc5MGI3NjExM280OG5yd2FueXRvN21nd2x4NndyOWhrMnIxenhocnVybjY1cGVjNCZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/ZqlvCTNHpqrio/giphy.gif',
		'https://media4.giphy.com/media/v1.Y2lkPTc5MGI3NjExbHk5MHMzc3BydHMwbjU2djV5bGJxcnhpYzdnbWl2emM2dmgxNDN1NSZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/Mjl0BsAgMGYTe/giphy.gif',
		'https://media3.giphy.com/media/v1.Y2lkPTc5MGI3NjExcHFwMmp6NjZ1aGhyMm5xMmg1amlnbjFlMXVoeHlqdHgxb2dhM3V6eSZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/3oEjHAUOqG3lSS0f1C/giphy.gif',
		'https://media2.giphy.com/media/v1.Y2lkPTc5MGI3NjExbnJ5dXN5NTloa2w3Y2oxZ3cwamlvZngwNndncW16NTRjMW16am55MCZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/fUYhyT9IjftxrxJXcE/giphy.gif',
		'https://media2.giphy.com/media/v1.Y2lkPTc5MGI3NjExczN1dHR6endzbWkybmt0dzBueXJqY21ueHhweWIwczZ1dXFrYzI4MyZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/3orieLHXgpfkKO9Iju/giphy.gif',
		'https://media4.giphy.com/media/v1.Y2lkPTc5MGI3NjExdTY1ZTRxdjJoMzZjZWw1M2Y2OTFmZWhsNzNnaWp6N3I5d2tpZjk2aSZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/ff0dv4KMGxjna/giphy.gif',
		'https://media2.giphy.com/media/v1.Y2lkPTc5MGI3NjExenM4bzUxZjUxMGk0ZjBpMTNudjBsbnhtOHZkY3djc3BpeGY5cGJqOCZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/lEc7pLgBpipbi/giphy.gif',
		'https://media3.giphy.com/media/v1.Y2lkPTc5MGI3NjExaDA0Z2hwdjZ1eWxhYnpzdThkcmNmY3c3d3Z5ejVkcTdndnExbXQ5dSZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/FWAcpJsFT9mvrv0e7a/giphy.gif',
		'https://media3.giphy.com/media/v1.Y2lkPTc5MGI3NjExZmZ3NW16djBwemVkdHIxZHZnMjY3d3hnc2xmMXdrdHB5ZnRqN3RraCZlcD12MV9pbnRlcm5hbF9naWZfYnlfaWQmY3Q9Zw/GDp7LycxkT3LG/giphy.gif',
	];
	let image = $state('');

	$effect.pre(() => {
		image = pickRandom(images);
	});
</script>

<MetaTags
	title="WP Admin Dashboard"
	titleTemplate="%s | Lofi and Games"
	description="WP Administrative dashboard"
	canonical="https://www.lofiandgames.com/wp-admin"
	openGraph={{
		url: 'https://www.lofiandgames.com/wp-admin',
		images: [
			{
				url: 'https://www.lofiandgames.com/share.png',
				width: 1200,
				height: 630,
				alt: 'Lofi and Games',
			},
		],
		siteName: 'Lofi and Games',
		type: 'website',
	}}
	twitter={{
		cardType: 'summary_large_image',
		title: 'WP Admin Dashboard - Lofi and Games',
		image: 'https://www.lofiandgames.com/share.png',
		site: 'https://www.lofiandgames.com/wp-admin',
	}}
/>

<PageTransition>
	<Navbar />

	<div class="min-h-screen-no-navbar flex items-center justify-center">
		{#if image}
			<img class="max-w-md w-full" alt="admin" src={image} />
		{/if}
	</div>
</PageTransition>
