<script lang="ts">
	import { cn } from '$lib/util/cn';
	import type { Snippet } from 'svelte';
	import { quadInOut } from 'svelte/easing';
	import { fly } from 'svelte/transition';

	interface Props {
		title: string;
		class?: string;
		trailing?: Snippet;
		children: Snippet;
	}

	let { title, class: className, children, trailing }: Props = $props();
</script>

<div
	in:fly={{
		y: 15,
		duration: 300,
		delay: 300,
		easing: quadInOut,
	}}
	out:fly={{ y: -15, duration: 300, easing: quadInOut }}
	onoutrostart={(e) => {
		if (e.target) {
			e.currentTarget.style.position = 'absolute';
			e.currentTarget.style.inset = '0';
		}
	}}
	class={cn('flex flex-col gap-4 size-full grow max-w-full', className)}
>
	<div class="flex items-center justify-between gap-4">
		<h1 class="text-3xl font-bold shrink-0">{title}</h1>
		{#if trailing}
			{@render trailing()}
		{/if}
	</div>

	{@render children()}
</div>
