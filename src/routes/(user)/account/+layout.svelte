<script lang="ts">
	import Navbar from '$lib/components/Navbar/Navbar.svelte';
	import PageTransition from '$lib/components/PageTransition.svelte';
	import type { Snippet } from 'svelte';
	import { authClient } from '$lib/auth/client';
	import { goto } from '$app/navigation';
	import AuthContainer from '../../(auth)/components/AuthContainer.svelte';
	import AccountSettingsIcon from '$lib/components/Icons/AccountSettingsIcon.svelte';
	import LockIcon from '$lib/components/Icons/LockIcon.svelte';
	import SideMenuLayout from '../../(auth)/components/SideMenuLayout.svelte';
	import { MetaTags } from 'svelte-meta-tags';
	import { theme } from '$lib/stores/theme.svelte';
	import AuthMetaColor from '../../(auth)/components/AuthMetaColor.svelte';

	interface Props {
		children: Snippet;
	}

	let { children }: Props = $props();
	const session = authClient.useSession();
	const menuItems = [
		{
			text: 'Account',
			href: '/account',
			icon: AccountSettingsIcon,
		},
		{
			text: 'Password',
			href: '/account/password',
			icon: LockIcon,
		},
	];

	$effect(() => {
		if (!$session.isPending && !$session.data?.user) {
			goto('/signin');
		}
	});
</script>

<AuthMetaColor />

<PageTransition>
	<Navbar variant="transparent" />

	<AuthContainer
		cardClass="max-w-4xl min-h-[80vh]"
		contentClass="grow flex flex-col gap-4 justify-start items-start"
	>
		<SideMenuLayout {menuItems} isLoading={$session.isPending}>
			{@render children()}
		</SideMenuLayout>
	</AuthContainer>
</PageTransition>
