<script lang="ts">
	import { supabase } from '$lib/api/supabase';
	import Footer from '$lib/components/Footer.svelte';
	import Navbar from '$lib/components/Navbar/Navbar.svelte';
	import PageTransition from '$lib/components/PageTransition.svelte';
	import { onMount } from 'svelte';

	let data: Array<{
		feedback: string;
		context: string;
	}> = $state([]);

	onMount(async () => {
		try {
			const select = await supabase.from('feedback').select('feedback,context');
			data = select.data as [];
			data.reverse();
		} catch (error) {
			console.error(error);
		}
	});
</script>

<PageTransition>
	<Navbar />

	<article class="px-4 py-8 mx-auto">
		<h1>Feedbacks</h1>

		<h2>Total: {data?.length}</h2>

		{#each data as entry}
			<p>[{entry.context}] {entry.feedback}</p>
		{/each}
	</article>

	<Footer />
</PageTransition>
