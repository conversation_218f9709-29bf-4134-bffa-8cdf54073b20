<script lang="ts">
	import type { Attribution } from '$lib/models/Attribution';
	import { cn } from '$lib/util/cn';

	interface Props {
		attribution: Attribution;
		class?: string;
	}

	let { attribution, class: className }: Props = $props();
</script>

<div
	class={cn(
		'flex flex-row gap-2 glass px-4 py-3 rounded-full items-center justify-center',
		className,
	)}
>
	<a
		class="text-base font-semibold leading-5"
		target="_blank"
		rel="noreferrer noopener"
		href={attribution.work.url}
	>
		{attribution.work.name ?? ''}
	</a>

	<span class="text-sm font-normal">by</span>

	<a
		class="text-base font-semibold"
		target="_blank"
		rel="noreferrer noopener"
		href={attribution.creator.url}
	>
		{attribution.creator.name ?? ''}
	</a>

	{#if attribution.license}
		<span class="text-sm font-normal">•</span>

		<a
			class="text-sm font-light"
			target="_blank"
			rel="noreferrer noopener"
			href={attribution.license.url}
		>
			{attribution.license.name ?? ''}
		</a>
	{/if}
</div>
