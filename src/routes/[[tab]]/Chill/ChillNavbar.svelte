<script>
	import Logo from '$lib/components/Logo.svelte';
	import ThemeButton from '$lib/components/Navbar/ThemeButton.svelte';
	import { siteSounds } from '$lib/stores/siteSounds.svelte';
	import { fade } from 'svelte/transition';
</script>

<nav class="navbar absolute" transition:fade={{ duration: 500 }}>
	<div class="navbar-start">
		<a aria-label="Home" href="/" class="relative btn-ghost btn btn-lg gap-2 py-1 text-xl px-2">
			<div class="glass absolute inset-0 -z-10 rounded-lg"></div>
			<Logo class="h-8 md:h-full" />
		</a>
	</div>

	<div class="navbar-center">
		<div role="tablist" class="tabs tabs-box glass">
			<a
				href="/"
				role="tab"
				class="tab"
				onclick={() => {
					siteSounds.homePlay.play();
				}}
			>
				Play
			</a>
			<a
				href="/chill"
				role="tab"
				class="tab tab-active glass"
				onclick={() => {
					siteSounds.homeChill.play();
				}}
			>
				Chill
			</a>
		</div>
	</div>

	<div class="navbar-end relative">
		<ThemeButton>
			<div class="glass absolute inset-0 -z-10 rounded-full"></div>
		</ThemeButton>
	</div>
</nav>
