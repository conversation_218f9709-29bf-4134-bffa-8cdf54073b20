import {
	Engine,
	Keys,
	Scene,
	type SceneEvents,
	vec,
	type SceneActivationContext,
	type Subscription,
} from 'excalibur';
import { Bird } from '../actors/Bird';
import { Floor } from '../actors/Floor';
import { PipeSpawner } from '../actors/PipeSpawner';
import { settings } from '../settings.svelte';
import { FloorSpawner } from '../actors/FloorSpawner';
import { Forest } from '../actors/Forest';
import { Buildings } from '../actors/Buildings';
import { Clouds } from '../actors/Clouds';
import { Sky } from '../actors/Sky';
import { Roof } from '../actors/Roof';
import { Score } from '../actors/Score';
import type { flappyBirdieSoundResources } from '../assets/flappyBirdieSoundResources';
import type { GameSound } from '$lib/util/GameSound.svelte';
import { Impact } from '../actors/Impact';

interface GameScreenEvents {
	started: {};
	gameOver: {};
	hitFloor: {};
}

interface Props {
	sounds: Record<keyof typeof flappyBirdieSoundResources, GameSound>;
}

export class GameScreen extends Scene {
	bird!: Bird;
	floorSpawner!: FloorSpawner;
	pipeSpawner!: PipeSpawner;
	clouds!: Clouds;
	forest!: Forest;
	buildings!: Buildings;
	sky!: Sky;
	roof!: Roof;
	scoreActor!: Score;
	impact!: Impact;
	score = $state(0);
	sounds: Props['sounds'];
	started = false;

	constructor({ sounds }: Props) {
		super();
		this.sounds = sounds;
	}

	initBird() {
		this.bird = new Bird({ sounds: this.sounds });

		this.bird.on('dead', ({ collidedWith }) => {
			if (!(collidedWith instanceof Floor)) {
				this.impact.blink();
			}

			this.onGameOver();
		});

		this.bird.on('incrementScore', ({ amount }) => {
			this.score += amount;
			this.scoreActor.setScore(this.score);
		});

		this.bird.on('hitFloor', () => {
			this.emit('hitFloor', {});
		});

		this.add(this.bird);
	}

	initFloor() {
		this.floorSpawner = new FloorSpawner();

		this.add(this.floorSpawner);
		this.floorSpawner.start();
	}

	initPipes() {
		this.pipeSpawner = new PipeSpawner();

		this.add(this.pipeSpawner);
	}

	initForest() {
		this.forest = new Forest({
			x: this.engine.drawWidth / 2,
			y: this.engine.drawHeight - settings.floor.height + 1 - settings.forest.height / 2,
			width: this.engine.drawWidth,
			height: settings.forest.height,
		});

		this.add(this.forest);
	}

	initBuildings() {
		this.buildings = new Buildings({
			x: this.engine.drawWidth / 2,
			y: this.engine.drawHeight - settings.floor.height + 1 - settings.forest.height,
			height: settings.buildings.height,
			width: this.engine.drawWidth,
		});

		this.add(this.buildings);
	}

	initClouds() {
		this.clouds = new Clouds({
			x: this.engine.drawWidth / 2,
			y:
				this.engine.drawHeight -
				settings.floor.height -
				settings.forest.height -
				settings.buildings.height / 2,
			height: settings.clouds.height,
			width: this.engine.drawWidth,
		});

		this.add(this.clouds);
	}

	initSky() {
		this.sky = new Sky({
			x: this.engine.drawWidth / 2,
			y: this.engine.drawHeight / 2,
			width: this.engine.drawWidth,
			height: this.engine.drawHeight,
		});
		this.add(this.sky);
	}

	initRoof() {
		this.roof = new Roof({
			height: 100,
			y: -settings.bird.height * 1.5,
			width: this.engine.drawWidth,
			anchor: vec(0, 1),
		});

		this.add(this.roof);
	}

	initScore() {
		this.scoreActor = new Score({
			x: this.engine.drawWidth / 2,
			y: settings.score.y,
		});

		this.add(this.scoreActor);
	}

	initImpact() {
		this.impact = new Impact({
			x: 0,
			y: 0,
			width: this.engine.drawWidth,
			height: this.engine.drawHeight,
			anchor: vec(0, 0),
		});

		this.add(this.impact);
	}

	onInitialize(engine: Engine): void {
		this.physics.config.gravity = vec(settings.gravity.x, settings.gravity.y);
		this.initBird();
		this.initFloor();
		this.initPipes();
		this.initForest();
		this.initBuildings();
		this.initClouds();
		this.initSky();
		this.initRoof();
		this.initScore();
		this.initImpact();

		this.reset();
	}

	onActivate(context: SceneActivationContext<unknown>): void {
		this.reset();
	}

	onGameOver() {
		this.pipeSpawner.stop();
		this.floorSpawner.stop();
		this.emit('gameOver', {});
	}

	reset = () => {
		this.started = false;
		this.pipeSpawner.reset();
		this.floorSpawner.reset();
		this.floorSpawner.start();
		this.bird.reset();
		this.impact.reset();
		this.score = 0;
		this.scoreActor.setScore(this.score);

		this.engine.input.pointers.primary.once('down', this.start);
		this.engine.input.keyboard.once('press', (event) => {
			if (event.key === Keys.Space) {
				this.start();
			}
		});
		this.engine.input.keyboard.once('press', (event) => {
			if (event.key === Keys.Space || event.key === Keys.W || event.key === Keys.ArrowUp) {
				this.start();
			}
		});
	};

	start = () => {
		if (this.started) {
			return;
		}

		this.bird.start();
		this.pipeSpawner.start();
		this.floorSpawner.start();
		this.started = true;

		this.emit('started', {});
	};

	reloadGraphics() {
		this.bird?.reloadGraphics();
		this.buildings?.reloadGraphics();
		this.clouds?.reloadGraphics();
		this.floorSpawner?.reloadGraphics();
		this.forest?.reloadGraphics();
		this.sky?.reloadGraphics();
		this.pipeSpawner?.reloadGraphics();
		this.scoreActor?.reloadGraphics();
	}
}

export interface GameScreen extends Scene {
	// Add the event map for type safety
	on<K extends keyof (GameScreenEvents & SceneEvents)>(
		eventName: K,
		handler: (event: (GameScreenEvents & SceneEvents)[K]) => void,
	): Subscription;
	emit<K extends keyof (GameScreenEvents & SceneEvents)>(
		eventName: K,
		payload: (GameScreenEvents & SceneEvents)[K],
	): Subscription;
}
