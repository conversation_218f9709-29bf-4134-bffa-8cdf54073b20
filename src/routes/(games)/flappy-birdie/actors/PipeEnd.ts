import { Actor, CollisionType, Engine, Sprite, type ActorArgs } from 'excalibur';
import { settings } from '../settings.svelte';
import { flappyBirdieSpriteSheet } from '../assets/flappyBirdieSpriteSheet';
import { NotBirdCollisionGroup } from './collisionGroups';

export class PipeEnd extends Actor {
	private sprite!: Sprite;

	constructor(config?: ActorArgs) {
		super({
			z: settings.z.pipeEnd,
			collisionGroup: NotBirdCollisionGroup,
			collisionType: CollisionType.Passive,
			...config,
		});

		this.body.useGravity = false;
	}

	reloadGraphics() {
		this.sprite = flappyBirdieSpriteSheet.pipeEnd();

		this.graphics.use(this.sprite);
	}

	onInitialize(_engine: Engine): void {
		this.reloadGraphics();
	}
}
