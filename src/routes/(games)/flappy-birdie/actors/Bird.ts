import {
	Actor,
	type ActorEvents,
	type Animation,
	CircleCollider,
	type Collider,
	type CollisionContact,
	CollisionType,
	type Engine,
	type Entity,
	type KeyEvent,
	Keys,
	type Side,
	vec,
	type Subscription,
} from 'excalibur';
import { settings } from '../settings.svelte';
import { PipeGap } from './PipeGap';
import { flappyBirdieSpriteSheet } from '../assets/flappyBirdieSpriteSheet';
import { Floor } from './Floor';
import { Roof } from './Roof';
import type { flappyBirdieSoundResources } from '../assets/flappyBirdieSoundResources';
import type { GameSound } from '$lib/util/GameSound.svelte';
import { PipeBody } from './PipeBody';
import { PipeEnd } from './PipeEnd';
import { BirdCollisionGroup } from './collisionGroups';
import { Pipe } from './Pipe';

const degree90 = Math.PI / 2;
const degrees165 = Math.PI * 2 - Math.PI / 12;

interface BirdEvents {
	incrementScore: { amount: number };
	dead: { collidedWith: Entity };
	hitFloor: {};
	hitRoof: {};
	hitPipe: {};
}

type Props = {
	sounds: Record<keyof typeof flappyBirdieSoundResources, GameSound>;
};

export class Bird extends Actor {
	isDead = false;
	flyAnimation?: Animation;
	isOnFloor = false;
	sounds: Props['sounds'];
	private abortController: AbortController | null = null;

	constructor({ sounds }: Props) {
		super({
			name: 'Bird',
			z: settings.z.bird,
			collisionGroup: BirdCollisionGroup,
			collider: new CircleCollider({
				offset: vec(2, 0),
				radius: Math.min(settings.bird.width / 2, settings.bird.height / 2) - 4,
			}),
		});

		this.sounds = sounds;
	}

	reloadGraphics() {
		const oldAnimation = this.flyAnimation;

		this.flyAnimation = flappyBirdieSpriteSheet.birdFly();
		this.flyAnimation.speed = oldAnimation?.speed ?? 1;
		this.flyAnimation.goToFrame(oldAnimation?.currentFrameIndex ?? 0);

		if (this.isDead) {
			this.graphics.use(flappyBirdieSpriteSheet.birdDead());
		} else {
			this.graphics.use(this.flyAnimation);
		}
	}

	onInitialize(engine: Engine): void {
		this.reloadGraphics();

		this.reset();
	}

	onCollisionStart(self: Collider, other: Collider, side: Side, contact: CollisionContact): void {
		const owner = other.owner;

		if (owner instanceof Roof) {
			this.emit('hitRoof', {});
			return;
		}

		if (owner instanceof Floor && !this.isOnFloor) {
			this.isOnFloor = true;
			this.angularVelocity = 0;
			this.sounds.impactWithFloor.play();
			this.emit('hitFloor', {});
		}

		if (owner instanceof PipeBody || owner instanceof PipeEnd || owner instanceof Pipe) {
			this.emit('hitPipe', {});
		}

		if (owner instanceof PipeGap) {
			this.emit('incrementScore', { amount: 1 });
			this.sounds.score.play();
		} else {
			this.die(owner);
		}
	}

	onPreUpdate(engine: Engine, delta: number): void {
		// Lock X position to initial spawn position
		this.pos.x = this.scene?.engine.halfDrawWidth ?? 0;
		this.vel.x = 0;

		if (this.vel.y < 0) {
			this.vel.clampMagnitude(settings.bird.maxUpSpeed);
		} else {
			this.vel.clampMagnitude(settings.bird.maxDownSpeed);
		}

		if (this.body.useGravity && !this.isOnFloor) {
			if (this.rotation > degree90 && this.rotation < degrees165) {
				if (this.angularVelocity > 0) {
					this.rotation = degree90;
				} else {
					this.rotation = degrees165;
				}
			}

			this.angularVelocity +=
				((this.angularVelocity > 0
					? settings.bird.flyAngularSpeedPositiveDecay
					: settings.bird.flyAngularSpeedNegativeDecay) *
					delta) /
				10;

			if (this.angularVelocity > settings.bird.flyMaxFallAngularSpeed) {
				this.angularVelocity = settings.bird.flyMaxFallAngularSpeed;
			}

			if (this.rotation === degree90) {
				this.flyAnimation?.pause();
				this.flyAnimation?.goToFrame(1);
			}
		}
	}

	fly = () => {
		if (this.isOnFloor || this.isDead || !this.scene?.engine.isRunning()) {
			return;
		}

		this.vel.y = -settings.bird.flySpeed;
		this.angularVelocity = settings.bird.flyAngularSpeed;

		if (!this.flyAnimation?.isPlaying) {
			this.flyAnimation?.play();
		}

		this.sounds.fly.play();
	};

	reset() {
		if (this.flyAnimation) {
			this.graphics.use(this.flyAnimation);
			this.flyAnimation.play();
			this.flyAnimation.speed = 0.5;
		}
		this.isOnFloor = false;
		this.angularVelocity = 0;
		this.rotation = 0;
		this.isDead = false;
		this.pos.x = this.scene?.engine.halfDrawWidth ?? 0;
		this.pos.y = this.scene?.engine.halfDrawHeight ?? 0;
		this.body.collisionType = CollisionType.Active;
		this.body.useGravity = false;
	}

	start() {
		if (this.flyAnimation) {
			this.flyAnimation.play();
			this.flyAnimation.speed = 1;
		}
		this.isDead = false;
		this.body.useGravity = true;
		this.fly();

		this.addListeners();
	}

	removeListeners() {
		this.scene!.engine.input.pointers.primary.off('down', this.fly);
		this.scene!.engine.input.keyboard.off('press', this.handleKeyPress);
	}

	addListeners() {
		this.scene!.engine.input.pointers.primary.on('down', this.fly);
		this.scene!.engine.input.keyboard.on('press', this.handleKeyPress);
	}

	handleKeyPress = (event: KeyEvent) => {
		if (event.key === Keys.Space || event.key === Keys.W || event.key === Keys.ArrowUp) {
			this.fly();
		}
	};

	die(collidedWith: Entity) {
		if (this.isDead) {
			return;
		}

		this.isDead = true;

		if (this.flyAnimation) {
			this.flyAnimation.pause();
			this.flyAnimation.goToFrame(0);
		}
		if (this.vel.x > 0) {
			this.vel.x = 0;
		}
		if (this.vel.y < 0) {
			this.vel.y = 0;
		}
		this.removeListeners();

		this.graphics.use(flappyBirdieSpriteSheet.birdDead());

		if (!this.isOnFloor) {
			this.sounds.fly.stop();
			this.sounds.impact.play();

			setTimeout(() => {
				this.sounds.fall.play();
			}, 300);
		}

		this.emit('dead', { collidedWith });
	}
}

export interface Bird extends Actor {
	// Add the event map for type safety
	on<K extends keyof (BirdEvents & ActorEvents)>(
		eventName: K,
		handler: (event: (BirdEvents & ActorEvents)[K]) => void,
	): Subscription;
	emit<K extends keyof (BirdEvents & ActorEvents)>(
		eventName: K,
		payload: (BirdEvents & ActorEvents)[K],
	): Subscription;
}
