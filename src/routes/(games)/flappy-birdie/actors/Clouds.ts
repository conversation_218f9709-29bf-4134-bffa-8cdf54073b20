import { Actor, CollisionType, Engine, type ActorArgs } from 'excalibur';
import { settings } from '../settings.svelte';
import { flappyBirdieSpriteSheet } from '../assets/flappyBirdieSpriteSheet';
import type { TiledSprite } from '../lib/TilesSprite';

export class Clouds extends Actor {
	sprite!: TiledSprite;

	constructor(config?: ActorArgs) {
		super({
			z: settings.z.clouds,
			collisionType: CollisionType.PreventCollision,
			...config,
		});

		this.reloadGraphics();
	}

	reloadGraphics() {
		this.sprite = flappyBirdieSpriteSheet.clouds({
			width: this.width,
			height: this.height,
		});

		this.graphics.use(this.sprite);
	}

	onInitialize(_engine: Engine): void {
		this.reloadGraphics();
	}
}
