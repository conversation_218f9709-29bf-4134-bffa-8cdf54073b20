<script lang="ts">
	import InfoModal from '$lib/components/InfoModal.svelte';
	import { sudokuSoundResources as sounds } from './sudokuSoundResources';

	interface Props {
		isOpen?: boolean;
	}

	let { isOpen = $bindable(false) }: Props = $props();
</script>

<InfoModal {sounds} bind:isOpen>
	<h1>Sudoku</h1>

	<p>
		Sudoku is a popular puzzle game that has captured the attention of players around the world. The
		objective of the game is to fill in a 9x9 grid with digits so that each column, each row, and
		each of the nine 3x3 sub-grids that compose the grid contains all of the digits from 1 to 9
		without repeating any number in the same row, column or 3x3 subgrid.
	</p>

	<h2>How to Play Sudoku: The Game Rules</h2>

	<ul>
		<li>
			Each row, column, and 3x3 subgrid in a Sudoku puzzle must contain all of the digits from 1 to
			9 without repeating any number.
		</li>

		<li>A valid Sudoku puzzle has only one solution.</li>

		<li>
			The basic rule of the game is to fill in the missing numbers in the grid following the above
			rules.
		</li>
	</ul>

	<h2>Useful Sudoku Game Tips</h2>

	<li>
		Start with the rows, columns, and 3x3 sub-grids that have the most numbers filled in. These are
		the easiest to solve.
	</li>

	<li>
		Always check the possibilities of a number in the empty spot before filling it in. This will
		save you from making mistakes.
	</li>

	<li>
		If you get stuck, try guessing a number and then work through the logic to see if your guess is
		correct.
	</li>

	<h2>Conclusion</h2>

	<p>
		Sudoku is a fun and challenging game that can be enjoyed by players of all ages. With practice
		and these tips, you'll be solving puzzles in no time.
	</p>
</InfoModal>
