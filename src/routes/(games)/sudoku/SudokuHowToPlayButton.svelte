<script lang="ts">
	import Dialog from '$lib/components/Dialog.svelte';
	import ThemeImage from '$lib/components/ThemeImage.svelte';
	import { isMacLike } from '$lib/functions/isMacLike';
	import { onMount } from 'svelte';

	let isOpen = $state(false);
	let isMac = $state(false);

	onMount(() => {
		isMac = isMacLike();
	});
</script>

<button class="btn btn-sm" onclick={() => (isOpen = true)}>How to play</button>

<Dialog bind:isOpen modalBoxClass="sm:max-w-2xl">
	<article>
		<h2>How to Play Sudoku</h2>

		<p>
			Sudoku is a logic-based number puzzle played on a 9x9 grid, divided into nine 3x3 boxes. The
			goal is to fill in the empty spaces while following these rules:
		</p>

		<ul>
			<li>Each row must contain the numbers 1-9, without repeats.</li>
			<li>Each column must contain the numbers 1-9, without repeats.</li>
			<li>Each 3x3 box must contain the numbers 1-9, without repeats.</li>
		</ul>

		<ThemeImage
			lightAvif="https://static.lofiandgames.com/images/sudoku/tutorial/sudoku-1.avif"
			lightPng="https://static.lofiandgames.com/images/sudoku/tutorial/sudoku-1.png"
			darkAvif="https://static.lofiandgames.com/images/sudoku/tutorial/sudoku-1-dark.avif"
			darkPng="https://static.lofiandgames.com/images/sudoku/tutorial/sudoku-1-dark.png"
			alt="Sudoku setup"
			caption="Sudoku Game with row 1, column 2, and the first 3x3 box highlighted"
		/>

		<h3>Gameplay</h3>

		<h4>Find Easy Numbers</h4>

		<p>
			Look for rows, columns, or boxes that are almost complete. If only one number is missing,
			place it!
		</p>

		<ThemeImage
			lightAvif="https://static.lofiandgames.com/images/sudoku/tutorial/sudoku-2.avif"
			lightPng="https://static.lofiandgames.com/images/sudoku/tutorial/sudoku-2.png"
			darkAvif="https://static.lofiandgames.com/images/sudoku/tutorial/sudoku-2-dark.avif"
			darkPng="https://static.lofiandgames.com/images/sudoku/tutorial/sudoku-2-dark.png"
			alt="Placing a number"
			caption="Placing 8 completes the row 6, column 1, and the 3x3 box"
		/>

		<h4>Watch for Hidden Singles</h4>
		<p>
			If a number can only fit in one spot within a row, column, or box, that must be the correct
			placement.
		</p>

		<ThemeImage
			lightAvif="https://static.lofiandgames.com/images/sudoku/tutorial/sudoku-3.avif"
			lightPng="https://static.lofiandgames.com/images/sudoku/tutorial/sudoku-3.png"
			darkAvif="https://static.lofiandgames.com/images/sudoku/tutorial/sudoku-3-dark.avif"
			darkPng="https://static.lofiandgames.com/images/sudoku/tutorial/sudoku-3-dark.png"
			alt="Placing a hidden single number"
			caption="2 is the only possible number on row 9, column 3, because the columns 1 and 2, and rows 7 and 8 already contain a 2"
		/>

		<h4>Use the Notes Feature</h4>

		<p>
			If you're unsure about a number, toggle "Notes" mode to add possible candidates in a cell.
		</p>

		<ThemeImage
			lightAvif="https://static.lofiandgames.com/images/sudoku/tutorial/sudoku-4.avif"
			lightPng="https://static.lofiandgames.com/images/sudoku/tutorial/sudoku-4.png"
			darkAvif="https://static.lofiandgames.com/images/sudoku/tutorial/sudoku-4-dark.avif"
			darkPng="https://static.lofiandgames.com/images/sudoku/tutorial/sudoku-4-dark.png"
			alt="Adding notes to a cell"
			caption="Adding 7 and 9 to the cell on row 4, column 5"
		/>

		<h4>Complete the Puzzle</h4>

		<p>Keep using logic (no guessing!) until the grid is fully filled.</p>

		<ThemeImage
			lightAvif="https://static.lofiandgames.com/images/sudoku/tutorial/sudoku-5.avif"
			lightPng="https://static.lofiandgames.com/images/sudoku/tutorial/sudoku-5.png"
			darkAvif="https://static.lofiandgames.com/images/sudoku/tutorial/sudoku-5-dark.avif"
			darkPng="https://static.lofiandgames.com/images/sudoku/tutorial/sudoku-5-dark.png"
			alt="Filled sudoku game"
			caption="Filled sudoku game"
		/>

		<h4>Need Help?</h4>

		<p>Use the Help button if you get stuck!</p>

		<section class="hidden lg:block">
			<h3>Keyboard Shortcuts</h3>

			<div class="overflow-x-auto">
				<table class="table">
					<thead>
						<tr>
							<th>Action</th>
							<th>Keybinding</th>
						</tr>
					</thead>

					<tbody>
						<tr>
							<td>Place Number (notes mode off)</td>
							<td>
								<kbd class="kbd dark:text-base-content">1</kbd>
								-
								<kbd class="kbd dark:text-base-content">9</kbd>
								or
								<kbd class="kbd dark:text-base-content">{isMac ? '⌘' : 'Ctrl'}</kbd>
								+ Click on cell number
							</td>
						</tr>
						<tr>
							<td>Add/Remove Note (notes mode on)</td>
							<td>
								<kbd class="kbd dark:text-base-content">1</kbd>
								-
								<kbd class="kbd dark:text-base-content">9</kbd>
								or Click on cell number
							</td>
						</tr>
						<tr>
							<td>Add/Remove Note</td>
							<td>
								<kbd class="kbd dark:text-base-content">Shift</kbd>
								+
								<kbd class="kbd dark:text-base-content">1</kbd>
								-
								<kbd class="kbd dark:text-base-content">9</kbd>
								or Click on cell number
							</td>
						</tr>
						<tr>
							<td>Toggle Notes Mode</td>
							<td>
								<kbd class="kbd dark:text-base-content">N</kbd>
								or
								<kbd class="kbd dark:text-base-content">Shift</kbd>
							</td>
						</tr>
						<tr>
							<td>Move Selection</td>
							<td>
								<kbd class="kbd dark:text-base-content">W</kbd>
								<kbd class="kbd dark:text-base-content">A</kbd>
								<kbd class="kbd dark:text-base-content">S</kbd>
								<kbd class="kbd dark:text-base-content">D</kbd>
								or
								<kbd class="kbd dark:text-base-content">▲</kbd>
								<kbd class="kbd dark:text-base-content">◀︎</kbd>
								<kbd class="kbd dark:text-base-content">▼</kbd>
								<kbd class="kbd dark:text-base-content">▶︎</kbd>
							</td>
						</tr>
						<tr>
							<td>Erase Cell</td>
							<td>
								<kbd class="kbd dark:text-base-content">{isMac ? 'delete' : 'backspace'}</kbd>
							</td>
						</tr>
						<tr>
							<td>Reveal Cell</td>
							<td>
								<kbd class="kbd dark:text-base-content">R</kbd>
							</td>
						</tr>
						<tr>
							<td>Reveal Cell Notes</td>
							<td>
								<kbd class="kbd dark:text-base-content">O</kbd>
							</td>
						</tr>
						<tr>
							<td>Show Hint</td>
							<td>
								<kbd class="kbd dark:text-base-content">H</kbd>
							</td>
						</tr>
						<tr>
							<td>Undo</td>
							<td>
								<div class="kbd dark:text-base-content">{isMac ? '⌘' : 'Ctrl'}</div>
								+
								<div class="kbd dark:text-base-content">Z</div>
							</td>
						</tr>
						<tr>
							<td>Redo</td>
							<td>
								<kbd class="kbd dark:text-base-content">{isMac ? '⌘' : 'Ctrl'}</kbd>
								+
								<kbd class="kbd dark:text-base-content">Shift</kbd>
								+
								<kbd class="kbd dark:text-base-content">Z</kbd>
							</td>
						</tr>
						<tr>
							<td>Toggle Pause</td>
							<td>
								<kbd class="kbd dark:text-base-content">P</kbd>
							</td>
						</tr>
						<tr>
							<td>Replay Current Game</td>
							<td>
								<kbd class="kbd dark:text-base-content">Shift</kbd>
								+
								<kbd class="kbd dark:text-base-content">C</kbd>
							</td>
						</tr>
						<tr>
							<td>Play a New Game</td>
							<td>
								<kbd class="kbd dark:text-base-content">Shift</kbd>
								+
								<kbd class="kbd dark:text-base-content">N</kbd>
							</td>
						</tr>
						<tr>
							<td>Play Daily Game</td>
							<td>
								<kbd class="kbd dark:text-base-content">Shift</kbd>
								+
								<kbd class="kbd dark:text-base-content">D</kbd>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</section>
	</article>
</Dialog>
