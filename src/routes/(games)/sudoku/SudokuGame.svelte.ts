/* eslint-disable @typescript-eslint/no-non-null-assertion */
import { supabase } from '$lib/api/supabase';
import { clone2DGrid } from '$lib/functions/clone2DGrid';
import { get2DGrid } from '$lib/functions/get2DGrid';
import { getNextGridItemOnDirection } from '$lib/functions/getNextGridItemOnDirection';
import { getRandomItemAt } from '$lib/functions/getRandomItemAt';
import { shuffle } from '$lib/functions/shuffle';
import { wait } from '$lib/functions/wait';
import type { GridItem } from '$lib/models/GridItem';
import { DirectionListener } from '$lib/util/DirectionListener';
import type { SettingsManager } from '$lib/util/SettingsManager.svelte';
import { Timer } from '$lib/util/Timer.svelte';
import { Undoable } from '$lib/util/Undoable/Undoable.svelte';
import { UndoableKeyboardListener } from '$lib/util/Undoable/UndoableKeyboardListener';
import { untrack } from 'svelte';
import { handleError } from '../../../hooks.client';

export type SudokuItemState =
	| 'normal'
	| 'selected'
	| 'highlighted'
	| 'error'
	| 'immutable'
	| 'hint';
export type SudokuDifficulty = 'easy' | 'medium' | 'hard';
export type SudokuHint = {
	position: GridItem;
	value: number;
};
export type SudokuBoard = (number | null)[][];
export type SolvedSudokuBoard = number[][];
export type SudokuNotes = (number[] | null)[][];
export type SudokuErrors = (boolean | null)[][];
export type SudokuClusterPosition = {
	position: GridItem;
	clusterIndex: number;
};
export type SudokuState = {
	board: SudokuBoard;
	notes: SudokuNotes;
	errors: SudokuErrors;
	selectedItem: GridItem | null;
};
export type SudokuSettingsManager = SettingsManager<{
	difficulty: SudokuDifficulty;
	autoNotes: boolean;
	autoCheck: boolean;
}>;

const sudokuMinInitialBoardCompletion: Record<SudokuDifficulty, number> = {
	easy: 0.67,
	medium: 0.55,
	hard: 0.33,
};

const sudokuScoreFactor: Record<SudokuDifficulty, number> = {
	easy: 1,
	medium: 2,
	hard: 4,
};

const hintDuration = 4000;

type GamePair = {
	game: string;
	solvedGame: string;
};

const games: Record<SudokuDifficulty, GamePair[]> = {
	easy: [],
	medium: [],
	hard: [],
};

interface GameOptions {
	settingsManager: SudokuSettingsManager;
	difficulty: SudokuDifficulty;
	onWin: () => void;
	fromGame?: { game: string; solvedGame: string; difficulty: SudokuDifficulty };
	timer: Timer;
}

export class SudokuGame {
	wasRevealed = $state(false);
	timer: Timer;
	settingsManager: SudokuSettingsManager;
	private _difficulty: SudokuDifficulty = 'easy';
	readonly possibleValues = SudokuGame.getPossibleValues(9);
	readonly rows = 9;
	readonly columns = 9;
	private onWin: () => void;
	private _selectedItem: GridItem | null = $state(null);
	private _hint: SudokuHint | null = $state(null);
	private _isTakingNotes = $state(false);
	private history = $state() as Undoable<SudokuState>;
	private undoableListener: UndoableKeyboardListener;
	private solvedGame!: SolvedSudokuBoard;
	private initialGame!: SudokuBoard;
	private _isWon = $state(false);
	private _ready = $state(false);
	private _hintsUsed = $state(0);
	private _cellsRevealed = $state(0);
	private _notesRevealed = $state(0);
	private _usedAutoNotes = $state(false);
	private _usedAutoCheck = $state(false);
	private cleanUpEffect: () => void;

	constructor(options: GameOptions) {
		this.undoableListener = new UndoableKeyboardListener(
			() => this.undo(),
			() => this.redo(),
		);
		this.timer = options.timer;
		this.onWin = options.onWin;
		this.settingsManager = options.settingsManager;
		this.initState(options.fromGame);
		// this.debugInitCloseToWinState();
		this.initListeners();
		this.cleanUpEffect = $effect.root(() => {
			$effect(() => {
				if (this.settingsManager.settings.autoNotes) {
					this.isTakingNotes = false;
				}
			});

			$effect(() => {
				if (this.timer.started && this.settingsManager.settings.autoNotes) {
					untrack(() => {
						this._usedAutoNotes = true;
					});
				}

				if (this.timer.started && this.settingsManager.settings.autoCheck) {
					untrack(() => {
						this._usedAutoCheck = true;
					});
				}
			});
		});
	}

	private _completionMap = $derived(SudokuGame.getCompletionMap(this.board));

	get completionMap() {
		return this._completionMap;
	}

	get difficulty() {
		return this._difficulty;
	}

	get canPerformActions() {
		return (
			(this.timer.running || !this.timer.started) && this.ready && this.started && !this.isOver
		);
	}

	get ready() {
		return this._ready;
	}

	set ready(ready: boolean) {
		this._ready = ready;
	}

	get started() {
		return true;
	}

	get isWon() {
		return this._isWon;
	}

	set isWon(isWon: boolean) {
		this._isWon = isWon;

		if (this.isWon) {
			this.timer.stop();
			this.onWin();
		}
	}

	get isOver() {
		return this.isWon;
	}

	get isTakingNotes() {
		return this._isTakingNotes;
	}

	set isTakingNotes(isTakingNotes: boolean) {
		this._isTakingNotes = isTakingNotes;
	}

	get clusteredBoard() {
		return SudokuGame.getClusters(this.board);
	}

	get selectedItem() {
		return this._selectedItem;
	}

	set selectedItem(item: GridItem | null) {
		this._selectedItem = item;
	}

	get selectedClusterIndex() {
		if (this.selectedItem === null) {
			return null;
		}

		const { row, column } = this.selectedItem;

		return 3 * Math.floor(row / 3) + Math.floor(column / 3);
	}

	get selectedItemValue() {
		if (!this.selectedItem) {
			return null;
		}

		return this.board[this.selectedItem.row][this.selectedItem.column];
	}

	set selectedItemValue(value: number | null) {
		if (this.isOver) {
			return;
		}

		if (!this.selectedItem) {
			return;
		}

		if (this.setItemValue(this.selectedItem, value)) {
			if (this.isHint(this.selectedItem)) {
				this.hideHint();
			}
		}
	}

	get selectedNotes() {
		if (!this.selectedItem) {
			return null;
		}

		return this.notes[this.selectedItem.row][this.selectedItem.column];
	}

	get hint() {
		return this._hint;
	}

	set hint(newHint: SudokuHint | null) {
		this._hint = newHint;
	}

	get board() {
		return this.history.state.board;
	}

	get canEditNotes() {
		return !this.settingsManager.settings.autoNotes;
	}

	get notes() {
		if (!this.ready || !this.settingsManager.settings.autoNotes) {
			return this.stateNotes;
		}

		return SudokuGame.getAllNotes(this.board);
	}

	private get stateNotes() {
		return this.history.state.notes;
	}

	get errors() {
		return this.history.state.errors;
	}

	/** Smaller is better */
	get score() {
		if (this.wasRevealed) {
			return Infinity;
		}

		const missingNumbers = this.initialGame
			.flatMap((row) => {
				return row.reduce((acc: number, num) => {
					return acc + (num === null ? 1 : 0);
				}, 0);
			})
			.reduce((acc, value) => {
				return acc + value;
			}, 0);

		const autoNotesPenalty = this._usedAutoNotes ? missingNumbers : 0;
		const autoCheckPenalty = this._usedAutoCheck ? missingNumbers : 0;

		return Math.max(
			0,
			Math.floor(
				(this._hintsUsed * 10 +
					this._notesRevealed +
					autoNotesPenalty +
					autoCheckPenalty +
					Math.floor(this.timer.elapsedTime / 1000) -
					missingNumbers) /
					sudokuScoreFactor[this.difficulty],
			),
		);
	}

	deselectItem() {
		this.selectedItem = null;
	}

	revealSelectedItem() {
		if (!this.selectedItem) {
			return;
		}
		const { row, column } = this.selectedItem;
		const solution = this.solvedGame[row][column];

		if (this.selectedItemValue === solution) {
			return;
		}

		this._cellsRevealed += 1;
		this.setItemValue(this.selectedItem, solution);
	}

	revealSelectedItemNotes() {
		if (!this.selectedItem || this.selectedItemValue || !this.canEditNotes) {
			return;
		}

		this._notesRevealed += 1;
		this.setNotes(this.selectedItem, SudokuGame.getNotesAt(this.board, this.selectedItem));
	}

	revealGame() {
		this.wasRevealed = true;
		this.history.add({
			board: clone2DGrid(this.solvedGame),
			errors: get2DGrid(this.board.length),
			notes: get2DGrid(this.rows, this.columns),
			selectedItem: this.selectedItem,
		});
		this.checkIfIsWon();
	}

	async showHint() {
		if (this.hint === null) {
			this._hintsUsed += 1;
			this.showAndApplyHint();

			await wait(hintDuration);

			this.hideHint();
		}
	}

	private showAndApplyHint() {
		if (this.isOver) {
			return;
		}

		const getRandomHint = (): SudokuHint => {
			const randomRow = Math.floor(Math.random() * this.rows);
			const randomColumn = Math.floor(Math.random() * this.columns);
			const randomItem = this.board[randomRow][randomColumn];
			const randomItemHasError = this.errors[randomRow][randomColumn];
			const isImmutable = this.isImmutable({ row: randomRow, column: randomColumn });

			if (!isImmutable && (randomItem === null || randomItemHasError)) {
				return {
					position: { row: randomRow, column: randomColumn },
					value: this.solvedGame[randomRow][randomColumn],
				};
			}

			return getRandomHint();
		};

		this.hint = getRandomHint();
		this.selectedItem = this.hint.position;
		this.setItemValue(this.hint.position, this.hint.value);
	}

	private hideHint() {
		this.hint = null;
	}

	deleteSelectedItem(): boolean {
		if (this.selectedItem) {
			if (this.selectedItemValue) {
				return this.deleteItem(this.selectedItem);
			}

			return this.setNotes(this.selectedItem, null);
		}

		return false;
	}

	toggleIsTakingNotes(newValue = !this.isTakingNotes) {
		if (!this.canEditNotes) {
			return;
		}

		this.isTakingNotes = newValue;
	}

	handleValue(value: number, asNotes = false) {
		if (asNotes || this.isTakingNotes) {
			if (this.selectedItem) {
				this.toggleNote(this.selectedItem, value);
			}
		} else {
			this.toggleSelectedItemValue(value);
		}
	}

	getPosition(clusterPosition: GridItem, clusterIndex: number): GridItem;
	getPosition(globalPosition: GridItem): GridItem;
	getPosition(position: GridItem, clusterIndex?: number): GridItem {
		if (typeof clusterIndex === 'number') {
			return SudokuGame.getGlobalPosition(position, clusterIndex);
		}

		return position;
	}

	getValue(clusterPosition: GridItem, clusterIndex: number): number | null;
	getValue(globalPosition: GridItem): number | null;
	getValue(position: GridItem, clusterIndex?: number): number | null {
		const positionToUse = this.getPosition(position, clusterIndex!);

		return this.board[positionToUse.row][positionToUse.column];
	}

	getNotes(clusterPosition: GridItem, clusterIndex: number): number[] | null;
	getNotes(globalPosition: GridItem): number[] | null;
	getNotes(position: GridItem, clusterIndex?: number): number[] | null {
		const positionToUse = this.getPosition(position, clusterIndex!);

		return this.notes[positionToUse.row][positionToUse.column];
	}

	deleteItem(clusterPosition: GridItem, clusterIndex: number): boolean;
	deleteItem(globalPosition: GridItem): boolean;
	deleteItem(position: GridItem, clusterIndex?: number): boolean {
		const positionToUse = this.getPosition(position, clusterIndex!);

		const isImmutable = this.isImmutable(positionToUse);

		if (isImmutable) {
			return false;
		}

		const isHint = this.isHint(positionToUse);

		if (isHint) {
			this.hideHint();
		}

		this.setItemValue(position, null);
		return true;
	}

	selectItem(clusterPosition: GridItem, clusterIndex: number): boolean;
	selectItem(globalPosition: GridItem): boolean;
	selectItem(position: GridItem, clusterIndex?: number): boolean {
		const positionToUse = this.getPosition(position, clusterIndex!);

		this.selectedItem = positionToUse;

		return true;
	}

	isSelected(clusterPosition: GridItem, clusterIndex: number): boolean;
	isSelected(globalPosition: GridItem): boolean;
	isSelected(position: GridItem, clusterIndex?: number): boolean {
		const positionToUse = this.getPosition(position, clusterIndex!);

		return (
			this.selectedItem?.row === positionToUse?.row &&
			this.selectedItem?.column === positionToUse?.column
		);
	}

	isHighlighted(clusterPosition: GridItem, clusterIndex: number): boolean;
	isHighlighted(globalPosition: GridItem): boolean;
	isHighlighted(position: GridItem, clusterIndex?: number): boolean {
		const positionToUse = this.getPosition(position, clusterIndex!);

		return (
			this.selectedClusterIndex === clusterIndex ||
			this.selectedItem?.row === positionToUse.row ||
			this.selectedItem?.column === positionToUse.column
		);
	}

	matchesSelectedValue(clusterPosition: GridItem, clusterIndex: number): boolean;
	matchesSelectedValue(globalPosition: GridItem): boolean;
	matchesSelectedValue(position: GridItem, clusterIndex?: number): boolean {
		const value = this.getValue(position, clusterIndex!);
		const selectedItemValue = this.selectedItemValue;
		const positionToUse = this.getPosition(position, clusterIndex!);

		if (
			positionToUse?.row === this.selectedItem?.row &&
			positionToUse?.column === this.selectedItem?.column
		) {
			return false;
		}

		if (value === null || selectedItemValue === null) {
			return false;
		}

		return value === selectedItemValue;
	}

	matchesSolution(clusterPosition: GridItem, clusterIndex: number): boolean;
	matchesSolution(globalPosition: GridItem): boolean;
	matchesSolution(position: GridItem, clusterIndex?: number): boolean {
		const positionToUse = this.getPosition(position, clusterIndex!);

		return (
			this.board[positionToUse.row][positionToUse.column] ===
			this.solvedGame[positionToUse.row][positionToUse.column]
		);
	}

	hasError(clusterPosition: GridItem, clusterIndex: number): boolean;
	hasError(globalPosition: GridItem): boolean;
	hasError(position: GridItem, clusterIndex?: number): boolean {
		const positionToUse = this.getPosition(position, clusterIndex!);

		return !!this.errors[positionToUse.row][positionToUse.column];
	}

	isImmutable(clusterPosition: GridItem, clusterIndex: number): boolean;
	isImmutable(globalPosition: GridItem): boolean;
	isImmutable(position: GridItem, clusterIndex?: number): boolean {
		const positionToUse = this.getPosition(position, clusterIndex!);

		return this.initialGame[positionToUse.row][positionToUse.column] !== null;
	}

	isHint(clusterPosition: GridItem, clusterIndex: number): boolean;
	isHint(globalPosition: GridItem): boolean;
	isHint(position: GridItem, clusterIndex?: number): boolean {
		if (this.hint === null) {
			return false;
		}

		const positionToUse = this.getPosition(position, clusterIndex!);

		if (
			positionToUse.row === this.hint.position.row &&
			positionToUse.column === this.hint.position.column
		) {
			return true;
		}

		return false;
	}

	getItemState(clusterPosition: GridItem, clusterIndex: number): SudokuItemState;
	getItemState(globalPosition: GridItem): SudokuItemState;
	getItemState(position: GridItem, clusterIndex?: number): SudokuItemState {
		const isImmutable = this.isImmutable(position, clusterIndex!);
		const isSelected = this.isSelected(position, clusterIndex!);
		const isHighlighted = this.isHighlighted(position, clusterIndex!);
		const isHint = this.isHint(position, clusterIndex!);
		const hasError = this.hasError(position, clusterIndex!);

		if (isHint) {
			return 'hint';
		}

		if (isImmutable) {
			return 'immutable';
		}

		if (isSelected) {
			return 'selected';
		}

		if (hasError) {
			return 'error';
		}

		if (isHighlighted) {
			return 'highlighted';
		}

		return 'normal';
	}

	canUndo() {
		if (this.isOver) {
			return false;
		}

		return this.history.canUndo();
	}

	canRedo() {
		if (this.isOver) {
			return false;
		}

		return this.history.canRedo();
	}

	undo() {
		if (!this.canPerformActions) {
			return;
		}

		this.history.undo();
		this.selectedItem = this.history.state.selectedItem;
	}

	redo() {
		if (!this.canPerformActions) {
			return;
		}

		this.history.redo();
		this.selectedItem = this.history.state.selectedItem;
	}

	reset() {
		this.deselectItem();
		this.history.reset();
		this.isWon = false;
		this.timer.reset();
		this.wasRevealed = false;
		this._hintsUsed = 0;
		this._cellsRevealed = 0;
		this._notesRevealed = 0;
		this._usedAutoNotes = false;
		this._usedAutoCheck = false;
		this.hideHint();
	}

	dispose() {
		this.cleanUpEffect?.();
		window.removeEventListener('keydown', this.handleKeydown);
		this.undoableListener.dispose();
	}

	private checkIfIsWon(): boolean {
		const hasErrors = this.errors.some((row) => row.some((number) => number === true));

		if (hasErrors) {
			return false;
		}

		const isBoardFull = this.board.every((row) => row.every((number) => number !== null));

		this.isWon = isBoardFull;

		return isBoardFull;
	}

	private setItemValue(globalPosition: GridItem, value: number | null): boolean {
		if (!this.canPerformActions) {
			return false;
		}

		if (this.isImmutable(globalPosition)) {
			return false;
		}

		const newBoard = clone2DGrid(this.board);
		const newNotes = clone2DGrid(this.stateNotes);

		newBoard[globalPosition.row][globalPosition.column] = value;
		newNotes[globalPosition.row][globalPosition.column] = null;

		this.history.add({
			board: newBoard,
			notes: newNotes,
			errors: SudokuGame.getErrors(newBoard),
			selectedItem: this.selectedItem,
		});
		this.timer.start();

		this.checkIfIsWon();

		return true;
	}

	private setNotes(globalPosition: GridItem, value: number[] | null) {
		if (!this.canPerformActions) {
			return false;
		}

		if (this.isImmutable(globalPosition)) {
			return false;
		}

		const newBoard = clone2DGrid(this.board);
		const newNotes = clone2DGrid(this.stateNotes);

		newBoard[globalPosition.row][globalPosition.column] = null;
		newNotes[globalPosition.row][globalPosition.column] = value;

		this.history.add({
			board: newBoard,
			notes: newNotes,
			errors: SudokuGame.getErrors(newBoard),
			selectedItem: this.selectedItem,
		});
		this.timer.start();

		return true;
	}

	private toggleNote(globalPosition: GridItem, value: number) {
		if (!this.canEditNotes) {
			return;
		}

		if (this.getNotes(globalPosition)?.includes(value)) {
			this.removeNote(globalPosition, value);
		} else {
			this.addNote(globalPosition, value);
		}
	}

	private addNote(globalPosition: GridItem, value: number) {
		if (!this.canEditNotes) {
			return;
		}

		this.setNotes(
			globalPosition,
			[...new Set([...(this.notes[globalPosition.row][globalPosition.column] ?? []), value])].sort(
				(a, b) => a - b,
			),
		);
	}

	private removeNote(globalPosition: GridItem, value: number) {
		if (!this.canEditNotes) {
			return;
		}

		if (this.getNotes(globalPosition) !== null) {
			const filteredValue = this.getNotes(globalPosition)?.filter((item) => item !== value) ?? null;

			const newValue = filteredValue?.length === 0 ? null : filteredValue;

			this.setNotes(globalPosition, newValue);
		}
	}

	private toggleSelectedItemValue(value: number) {
		if (!this.selectedItem) {
			return false;
		}

		if (this.selectedItemValue === value) {
			this.deleteSelectedItem();
		} else {
			this.selectedItemValue = value;
		}
	}

	private handleKeydown = (event: KeyboardEvent) => {
		if (!this.canPerformActions) {
			return;
		}

		const key = event.key.toLocaleLowerCase();
		const direction = DirectionListener.getDirectionFromKeyboardEvent(event);

		// Delete
		if (key === 'backspace' || key === 'delete') {
			this.deleteSelectedItem();
			return;
		}

		// Direction
		if (direction && this.selectedItem) {
			const nextItem = getNextGridItemOnDirection(this.selectedItem, direction, {
				row: this.rows,
				column: this.columns,
			});

			if (nextItem) {
				this.selectedItem = nextItem;
			}
			return;
		}

		// Numbers
		this.possibleValues.forEach((value) => {
			if (event.code === `Digit${value}`) {
				this.handleValue(value);
			}
		});
	};

	private initListeners() {
		window.addEventListener('keydown', this.handleKeydown);
		this.undoableListener.listen();
	}

	private initGameStateFromBoards(newGame: SudokuBoard, solvedGame: SudokuBoard) {
		const state: SudokuState = {
			board: newGame,
			notes: get2DGrid(this.rows, this.columns),
			errors: get2DGrid(this.rows, this.columns),
			selectedItem: null,
		};

		this.initialGame = newGame;
		this.solvedGame = solvedGame as SolvedSudokuBoard;
		this.history = new Undoable<SudokuState>(state);
	}

	private initEmptyGameState() {
		// Empty game
		const solvedGame: SudokuBoard = get2DGrid(this.rows, this.columns);
		const newGame: SudokuBoard = get2DGrid(this.rows, this.columns);

		this.initGameStateFromBoards(newGame, solvedGame);
	}

	private initFallbackGameState() {
		const { solvedGame, newGame } = SudokuGame.getNewGame(this.difficulty);

		this.initGameStateFromBoards(newGame, solvedGame);
	}

	private async initState(fromGame?: {
		game: string;
		solvedGame: string;
		difficulty: SudokuDifficulty;
	}) {
		this.initEmptyGameState();

		if (fromGame) {
			const newGame = SudokuGame.fromStringToBoard(fromGame.game);
			const solvedGame = SudokuGame.fromStringToBoard(fromGame.solvedGame);
			this._difficulty = fromGame.difficulty;
			this.initGameStateFromBoards(newGame, solvedGame);
			this.ready = true;
		} else {
			this._difficulty = this.settingsManager.settings.difficulty;

			// Get real game
			try {
				const games = (
					await supabase
						.rpc('random_sudoku', {
							// Get difficulty from settings, not from current game
							difficulty_level: this.settingsManager.settings.difficulty,
						})
						.select('game,solution')
						.throwOnError()
				).data;

				if (!games || games.length === 0) {
					throw new Error('Game not found');
				}

				const game = games[0];
				const newGame = SudokuGame.fromStringToBoard(game.game);
				const solvedGame = SudokuGame.fromStringToBoard(game.solution);

				this.initGameStateFromBoards(newGame, solvedGame);
			} catch (error: any) {
				handleError(error);

				// Fallback to legacy version
				try {
					const sudoku = await SudokuGame.legacyFetchGame(this.difficulty);
					const newGame = SudokuGame.fromStringToBoard(sudoku.newGame);
					const solvedGame = SudokuGame.fromStringToBoard(sudoku.solvedGame);

					this.initGameStateFromBoards(newGame, solvedGame);
				} catch (error: any) {
					handleError(error);
					// Fallback to random game
					this.initFallbackGameState();
				}
			}

			this.ready = true;
		}
	}

	private debugInitCloseToWinState() {
		const { solvedGame } = SudokuGame.getNewGame(this.difficulty);
		const newGame = clone2DGrid(solvedGame);
		const notes: SudokuNotes = get2DGrid(this.rows, this.columns);
		notes[0][0] = [newGame[0][0]!];
		newGame[0][0] = null;

		const state: SudokuState = {
			board: newGame,
			notes,
			errors: get2DGrid(this.rows, this.columns),
			selectedItem: null,
		};

		this.initialGame = newGame;
		this.solvedGame = solvedGame as SolvedSudokuBoard;
		this.history = new Undoable<SudokuState>(state);
		this.ready = true;
	}

	static getNotesAt(board: SudokuBoard, position: GridItem) {
		const unavailableNumbers = [
			...SudokuGame.getNumbersOnRow(board, position.row),
			...SudokuGame.getNumbersOnColumn(board, position.column),
			...SudokuGame.getNumbersOnCluster(board, position),
		];
		const availableNumbers = SudokuGame.getPossibleValues(board.length).filter(
			(number) => !unavailableNumbers.includes(number),
		);
		return availableNumbers;
	}

	static getAllNotes(board: SudokuBoard) {
		return board.map((row, rowIndex) =>
			row.map((item, columnIndex) => {
				if (!item) {
					return SudokuGame.getNotesAt(board, { row: rowIndex, column: columnIndex });
				}

				return null;
			}),
		);
	}

	static async legacyFetchGame(difficulty: SudokuDifficulty) {
		if (games[difficulty].length === 0) {
			let csv: string;

			switch (difficulty) {
				case 'easy': {
					csv = await fetch(
						'https://static.lofiandgames.com/generated-games/sudoku/sudoku-easy.csv',
					).then((res) => res.text());
					break;
				}
				case 'medium': {
					csv = await fetch(
						'https://static.lofiandgames.com/generated-games/sudoku/sudoku-medium.csv',
					).then((res) => res.text());
					break;
				}
				case 'hard': {
					csv = await fetch(
						'https://static.lofiandgames.com/generated-games/sudoku/sudoku-hard.csv',
					).then((res) => res.text());
					break;
				}
			}

			const lines = csv.split('\n').map((line) => line.replace('\r', ''));
			const [_, ...gamePairsString] = lines;
			const gamePairs = gamePairsString.map((pair) => pair.split(',')); // [game, solvedGame]
			games[difficulty] = gamePairs.map((pair) => ({
				game: pair[0],
				solvedGame: pair[1],
			}));
		}

		const randomGamePair = getRandomItemAt(games[difficulty]);

		if (!randomGamePair) {
			throw new Error(`No Sudoku game found for '${difficulty}' difficulty`);
		}

		return {
			newGame: randomGamePair.game,
			solvedGame: randomGamePair.solvedGame,
			difficulty,
		};
	}

	static fromStringToBoard(game: string) {
		const board: SudokuBoard = get2DGrid(9);

		[...game].forEach((value, index) => {
			if (value !== '0') {
				const row = Math.floor(index / 9);
				const column = index % 9;
				board[row][column] = +value;
			}
		});

		return board;
	}

	static getNewGame(difficulty: SudokuDifficulty): {
		solvedGame: SudokuBoard;
		newGame: SudokuBoard;
	} {
		const solvedGame = SudokuGame.getNewSolvedGame();
		const newGame = clone2DGrid(solvedGame);
		const gameSize = solvedGame.length;
		const amountOfNumberstoRemove = Math.floor(
			(1 - sudokuMinInitialBoardCompletion[difficulty]) * gameSize * gameSize,
		);
		let numbersRemoved = 0;

		while (numbersRemoved < amountOfNumberstoRemove) {
			const randomRow = Math.floor(Math.random() * gameSize);
			const randomColumn = Math.floor(Math.random() * gameSize);
			const randomItem = newGame[randomRow][randomColumn];

			if (randomItem !== null) {
				newGame[randomRow][randomColumn] = null;
				numbersRemoved += 1;
			}
		}

		return {
			solvedGame,
			newGame,
		};
	}

	static getNewSolvedGame(): SudokuBoard {
		const board: SudokuBoard = get2DGrid(9);
		const gridSize = board.length;

		const solveGame = (row: number, column: number): boolean => {
			if (row >= gridSize || column >= gridSize) {
				return true;
			}

			const validNumbers = shuffle(SudokuGame.getValidNumbersOnPosition(board, { row, column }));

			let nextRow = row;
			let nextColumn = column + 1;

			if (nextColumn === gridSize) {
				nextRow = row + 1;
				nextColumn = 0;
			}

			for (let index = 0; index < validNumbers.length; index += 1) {
				const currentNumber = validNumbers[index];

				board[row][column] = currentNumber;

				if (solveGame(nextRow, nextColumn)) {
					return true;
				}
			}

			board[row][column] = null;
			return false;
		};

		solveGame(0, 0);

		return board;
	}

	static getValidNumbersOnPosition(board: SudokuBoard, globalPosition: GridItem): number[] {
		if (board[globalPosition.row][globalPosition.column] !== null) {
			return [];
		}

		const numbersOnRow = SudokuGame.getNumbersOnRow(board, globalPosition.row);
		const numbersOnColumn = SudokuGame.getNumbersOnColumn(board, globalPosition.column);
		const numbersOnCluster = SudokuGame.getNumbersOnCluster(board, globalPosition);
		const forbiddenNumbers = [...numbersOnRow, ...numbersOnColumn, ...numbersOnCluster];
		const validNumbers = SudokuGame.getPossibleValues(board.length).filter(
			(num) => !forbiddenNumbers.includes(num),
		);

		return validNumbers;
	}

	static getNumbersOnRow(board: SudokuBoard, row: number): number[] {
		return board[row].filter((num) => num !== null) as number[];
	}

	static getNumbersOnColumn(board: SudokuBoard, column: number): number[] {
		return board.flatMap((row) => row[column]).filter((num) => num !== null) as number[];
	}

	static getNumbersOnCluster(board: SudokuBoard, globalPosition: GridItem): number[] {
		const clusters = SudokuGame.getClusters(board);
		const clusterPosition = SudokuGame.getClusterPosition(globalPosition);

		return clusters[clusterPosition.clusterIndex].flatMap(
			(row) => row.filter((num) => num !== null) as number[],
		);
	}

	static getClusters(board: SudokuBoard) {
		const rows = board.map((row) => [row.slice(0, 3), row.slice(3, 6), row.slice(6, 9)]);
		const clusters = [
			// Cluster row 1
			[rows[0][0], rows[1][0], rows[2][0]],
			[rows[0][1], rows[1][1], rows[2][1]],
			[rows[0][2], rows[1][2], rows[2][2]],
			// Cluster row 2
			[rows[3][0], rows[4][0], rows[5][0]],
			[rows[3][1], rows[4][1], rows[5][1]],
			[rows[3][2], rows[4][2], rows[5][2]],
			// Cluster row 3
			[rows[6][0], rows[7][0], rows[8][0]],
			[rows[6][1], rows[7][1], rows[8][1]],
			[rows[6][2], rows[7][2], rows[8][2]],
		];

		return clusters;
	}

	static getGlobalPosition(clusterPosition: GridItem, clusterIndex: number): GridItem {
		const globalRow = 3 * Math.floor(clusterIndex / 3) + clusterPosition.row;
		const globalColumn = (clusterIndex % 3) * 3 + clusterPosition.column;

		return {
			row: globalRow,
			column: globalColumn,
		};
	}

	static getClusterPosition(globalPosition: GridItem): SudokuClusterPosition {
		const clusterRow = globalPosition.row % 3;
		const clusterColumn = globalPosition.column % 3;
		const clusterIndex =
			3 * Math.floor(globalPosition.row / 3) + Math.floor(globalPosition.column / 3);

		return {
			position: {
				row: clusterRow,
				column: clusterColumn,
			},
			clusterIndex,
		};
	}

	static getPossibleValues(gridSize: number) {
		return Array(gridSize)
			.fill(0)
			.map((_, i) => i + 1);
	}

	static getErrors(board: SudokuBoard): SudokuErrors {
		return board.map((row, rowIndex) => {
			const rowNumbers = SudokuGame.getNumbersOnRow(board, rowIndex);

			return row.map((number, columnIndex) => {
				if (number === null) {
					return false;
				}

				return (
					rowNumbers.filter((rowNumber) => rowNumber === number).length > 1 ||
					SudokuGame.getNumbersOnColumn(board, columnIndex).filter(
						(columnNumber) => columnNumber === number,
					).length > 1 ||
					SudokuGame.getNumbersOnCluster(board, {
						row: rowIndex,
						column: columnIndex,
					}).filter((clusterNumber) => clusterNumber === number).length > 1
				);
			});
		});
	}

	static getCompletionMap(board: SudokuBoard) {
		return board
			.flatMap((row) => row)
			.reduce(
				(acc, number) => {
					if (number === null) {
						return acc;
					}

					acc[number] = (acc[number] ?? 0) + 1;
					return acc;
				},
				{} as Record<number, number>,
			);
	}
}
