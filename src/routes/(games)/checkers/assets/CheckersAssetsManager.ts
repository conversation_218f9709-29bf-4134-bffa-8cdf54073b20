import { Assets, Spritesheet, Texture, type SpritesheetData } from 'pixi.js';

export type CheckerPieceColor = 'black' | 'red' | 'blue' | 'white';
export const checkerPieceColors: CheckerPieceColor[] = ['black', 'white', 'red', 'blue'];

export class CheckersAssetsManager {
	private _spritesheet?: Spritesheet<typeof checkersAtlasData>;

	get atlas() {
		return checkersAtlasData;
	}

	get spritesheet() {
		return this._spritesheet;
	}

	async loadAndUse() {
		const image = this.atlas.meta.image;

		await Assets.load(image);

		this._spritesheet = new Spritesheet(Texture.from(image), this.atlas);

		await this.spritesheet!.parse();
	}

	async unload() {
		return Assets.unload(checkersAtlasData.meta.image);
	}

	pieceTexture(color: CheckerPieceColor): Texture {
		return this.spritesheet!.textures[color];
	}

	shadowTexture(): Texture {
		return this.spritesheet!.textures.shadow;
	}

	crownTexture(): Texture {
		return this.spritesheet!.textures.crown;
	}
}

export const checkersAtlasData = {
	frames: {
		shadow: {
			frame: {
				x: 0,
				y: 5,
				w: 112,
				h: 112,
			},
		},
		crown: {
			frame: {
				x: 122,
				y: 0,
				w: 112,
				h: 122,
			},
		},
		red: {
			frame: {
				x: 244,
				y: 0,
				w: 112,
				h: 122,
			},
		},
		blue: {
			frame: {
				x: 366,
				y: 0,
				w: 112,
				h: 122,
			},
		},
		black: {
			frame: {
				x: 488,
				y: 0,
				w: 112,
				h: 122,
			},
		},
		white: {
			frame: {
				x: 610,
				y: 0,
				w: 112,
				h: 122,
			},
		},
	},
	meta: {
		image: 'https://static.lofiandgames.com/images/checkers/checkers-2x.png',
		format: 'RGBA8888',
		size: { w: 722, h: 122 },
		scale: 1,
	},
} satisfies SpritesheetData;
