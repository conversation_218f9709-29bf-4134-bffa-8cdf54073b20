import { CheckersCpu } from '../model/CheckersCpu';
import { CheckersGame, type CheckersGameJson, type Move } from '../model/CheckersGame.svelte';

export interface CpuRequest {
	game: CheckersGameJson;
}

interface CpuResponse {
	bestMove: { id: number; move: Move } | undefined;
	error?: string;
}

self.onmessage = async (event: MessageEvent<CpuRequest>) => {
	const { game } = event.data;

	try {
		const restoredGame = CheckersGame.fromJson(game);

		const cpu = new CheckersCpu();

		// Run the cpu turn
		cpu.start(restoredGame);

		const bestMove = cpu.getBestMove();

		// Send back response
		const response: CpuResponse = {
			bestMove,
		};

		self.postMessage(response);
	} catch (error) {
		const response: CpuResponse = {
			bestMove: undefined,
			error: (error as Error).message || 'Unknown error',
		};
		self.postMessage(response);
	}
};

export {};
