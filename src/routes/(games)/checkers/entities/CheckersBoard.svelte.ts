import type { GridItem } from '$lib/models/GridItem';
import { theme, type Theme } from '$lib/stores/theme.svelte';
import { Application, Container, Graphics } from 'pixi.js';
import type { AnimationSystem } from '../../solitaire/systems/AnimationSystem';
import { CheckerBoardSquare } from './CheckerBoardSquare';

interface Props {
	app: Application;
	gridSize: number;
	animationSystem: AnimationSystem;
	onPointerUp: (gridItem: GridItem) => void;
}

export const checkersBoardSettings = {
	maxSize: 576,
	border: 4,
	canvasPadding: 16,
	marginTop: 128,
};

const squareColors: Record<Theme, { light: string; dark: string; highlight: string }> = {
	light: {
		light: '#F9D8AD',
		dark: '#F7BB76',
		highlight: '#FFFFFF',
	},
	'light-classic': {
		light: '#F9D8AD',
		dark: '#F7BB76',
		highlight: '#FFFFFF',
	},
	dark: {
		light: '#ecf9ff',
		dark: '#15191e',
		highlight: '#ecf9ff',
	},
	'dark-classic': {
		light: '#9CA3B1',
		dark: '#1D2128',
		highlight: '#FFFFFF',
	},
};

export class CheckersBoard {
	app: Application;
	view = new Container();
	private _animationSystem: AnimationSystem;
	private _gridSize: number;
	private _outerBg: Graphics;
	private _innerBg: Graphics;
	private _squares: CheckerBoardSquare[] = [];
	private _clearThemeListener: () => void;
	private _onPointerUp: (gridItem: GridItem) => void;

	constructor({ app, gridSize, animationSystem, onPointerUp }: Props) {
		this.app = app;
		this._animationSystem = animationSystem;
		this._gridSize = gridSize;
		this._outerBg = new Graphics().rect(0, 0, 1, 1).fill(squareColors.light.dark);
		this._innerBg = new Graphics().rect(0, 0, 1, 1).fill(squareColors.light.light);
		this._onPointerUp = onPointerUp;

		this.view.addChild(this._outerBg);
		this.view.addChild(this._innerBg);

		this.createBoard();
		this.resize();

		this._clearThemeListener = $effect.root(() => {
			$effect(() => {
				const colors = squareColors[theme.value];

				this._outerBg.clear().rect(0, 0, 1, 1).fill(colors.dark);
				this._innerBg.clear().rect(0, 0, 1, 1).fill(colors.light);

				this._squares.forEach((square) => {
					square.changeColor(colors.dark, colors.highlight);
				});

				this.resize();
			});
		});
	}

	get squares() {
		return this._squares;
	}

	private createBoard() {
		this._squares.forEach((square) => square.view.destroy()); // Clear previous squares
		this._squares = [];

		// Create squares with correct placement
		for (let row = 0; row < this._gridSize; row++) {
			for (let column = 0; column < this._gridSize; column++) {
				if ((row + column) % 2 === 1) {
					const square = new CheckerBoardSquare({
						row,
						column,
						animationSystem: this._animationSystem,
						highlightColor: squareColors[theme.value].highlight,
					});

					square.view.on('pointerup', () => {
						this._onPointerUp({ row, column: column });
					});

					this._squares.push(square);
					this.view.addChild(square.view);
				}
			}
		}
	}

	getSquareAt({ row, column }: GridItem) {
		return this._squares.find((square) => square.row === row && square.column === column) || null;
	}

	get checkerSize() {
		return this._squares[0]?.view.width ?? 0;
	}

	get checkerOffset() {
		return checkersBoardSettings.border;
	}

	resize() {
		const availableHeight = this.app.renderer.height - checkersBoardSettings.marginTop;
		const availableWidth = this.app.renderer.width;
		const boardSize =
			Math.min(availableWidth, availableHeight) - checkersBoardSettings.canvasPadding * 2;
		const newSize = Math.min(boardSize, checkersBoardSettings.maxSize);

		this.view.position.set(
			this.app.renderer.width / 2 - newSize / 2,
			(checkersBoardSettings.marginTop + this.app.renderer.height) / 2 - newSize / 2,
		);

		this._outerBg.setSize(newSize, newSize);
		this._innerBg.setSize(
			newSize - checkersBoardSettings.border * 2,
			newSize - checkersBoardSettings.border * 2,
		);
		this._innerBg.position.set(checkersBoardSettings.border, checkersBoardSettings.border);

		const squareSize = (newSize - checkersBoardSettings.border * 2) / this._gridSize;

		let squareIndex = 0;

		for (let row = 0; row < this._gridSize; row++) {
			for (let col = 0; col < this._gridSize; col++) {
				if ((row + col) % 2 === 1) {
					const square = this._squares[squareIndex++];

					square.view.setSize(squareSize, squareSize);
					square.view.position.set(
						checkersBoardSettings.border + col * squareSize,
						checkersBoardSettings.border + row * squareSize,
					);
				}
			}
		}
	}

	dispose() {
		this._clearThemeListener();
		this.view.destroy();
	}
}
