import type { AnyDirection } from '$lib/functions/getNextGridItemOnDirection';
import { isSameGridItem } from '$lib/functions/isSameGridItem';
import type { GridItem } from '$lib/models/GridItem';
import { Grid } from '$lib/util/Grid.svelte';
import { Undoable } from '$lib/util/Undoable/Undoable.svelte';
import { untrack } from 'svelte';

export type CheckerPieceOwner = 'player' | 'opponent';
export type CheckerWinner = CheckerPieceOwner | 'draw';

interface CheckerPieceProps {
	owner: CheckerPieceOwner;
	id: number;
	attached?: CheckerPiece | null;
	attachedBy?: CheckerPiece | null;
	isSpare?: boolean;
}

type CheckerPieceId = number;

export interface CheckerPieceJson {
	id: number;
	owner: CheckerPieceOwner;
	attached: CheckerPieceJson | null;
	isSpare: boolean;
	attachedBy: CheckerPiece<PERSON>son | null;
}

export class CheckerPiece {
	id: CheckerPieceId;
	owner: CheckerPieceOwner;
	attached: CheckerPiece | null = null;
	attachedBy: CheckerPiece | null = null;
	isSpare: boolean;

	constructor({ owner, id, attached, attachedBy, isSpare = false }: CheckerPieceProps) {
		this.owner = owner;
		this.attached = attached || null;
		this.isSpare = isSpare;
		this.attachedBy = attachedBy || null;
		this.id = id;
	}

	isSame(other?: CheckerPiece | null) {
		if (!other) {
			return false;
		}

		return this.id === other.id;
	}

	cloneAndAttatch(other: CheckerPiece): { self: CheckerPiece; other: CheckerPiece } {
		const self = this.clone();
		const newOther = other.clone();

		self.attached = newOther;
		newOther.attachedBy = self;

		return {
			self,
			other: newOther,
		};
	}

	cloneAndDettach(): { self: CheckerPiece; other: CheckerPiece | null } {
		if (this.attached) {
			const self = this.clone();
			const newOther = this.attached?.clone();

			self.attached = null;
			newOther.attachedBy = null;

			return {
				self,
				other: newOther,
			};
		}

		return {
			self: this,
			other: this.attached,
		};
	}

	get isKing() {
		return this.attached !== null;
	}

	clone(): CheckerPiece {
		return new CheckerPiece({
			owner: this.owner,
			id: this.id,
			attached: this.attached,
			isSpare: this.isSpare,
			attachedBy: this.attachedBy,
		});
	}

	toJson(props?: { attachedBy?: CheckerPieceJson }): CheckerPieceJson {
		const json: CheckerPieceJson = {
			id: this.id,
			owner: this.owner,
			isSpare: this.isSpare,
			attached: null,
			attachedBy: props?.attachedBy ?? null,
		};

		if (this.attached) {
			json.attached = this.attached.toJson({ attachedBy: json });
		}

		return json;
	}

	static fromJson(json: CheckerPieceJson): CheckerPiece {
		const piece = new CheckerPiece({
			owner: json.owner,
			id: json.id,
			isSpare: json.isSpare,
			attached: json.attached ? CheckerPiece.fromJson(json.attached) : null,
			attachedBy: null,
		});

		if (piece.attached) {
			piece.attached.attachedBy = piece;
		}

		return piece;
	}
}

export type CheckersDifficulty = 'easy' | 'medium' | 'hard' | 'very-hard';

export interface CheckersGameJson {
	grid: Array<Array<CheckerPieceJson | null>>;
	capturedPlayerPieces: CheckerPieceJson[];
	capturedOpponentPieces: CheckerPieceJson[];
	turn: 'player' | 'opponent';
	pieceOnCaptureSequence?: CheckerPieceJson;
	pieceCaptureSequences: Array<{
		moves: Array<
			Omit<Move, 'capture'> & {
				capture?: CheckerPieceJson;
			}
		>;
	}>;
	captureSequenceMoveIndex: number;
	pendingCaptureMoves: Array<
		Omit<Move, 'capture'> & {
			capture?: CheckerPieceJson;
		}
	>;
	difficulty: CheckersDifficulty;
	rules: CheckerRules;
}

interface CheckerProps {
	difficulty: CheckersDifficulty;
	rules: CheckerRules;
	grid?: Grid<CheckerPiece>;
}

export type Move = GridItem & {
	capture?: CheckerPiece;
	capturePosition?: GridItem;
};

export type MoveSequence = {
	moves: Move[];
};

export type CheckersPromoteStrategy =
	| 'promote-and-stop-capture-sequence'
	| 'promote-and-continue-capture-sequence'
	| 'promote-if-capture-sequence-ended';

export type CheckersCaptureStrategy = 'remove-piece-at-capture' | 'remove-piece-at-end-of-turn';

export interface CheckerRules {
	size: number;
	mandatoryCapture: boolean;
	canCaptureBackwards: boolean;
	allowLongKingMoves: boolean;
	allowLongKingMovesAfterCapture: boolean;
	promoteStrategy: CheckersPromoteStrategy;
	captureStrategy: CheckersCaptureStrategy;
	maximizeCaptureSequence: boolean;
}

export interface CheckersGameState {
	grid: Grid<CheckerPiece>;
	/**
	 * Holds player pieces captured by opponent pieces.
	 * When a player piece becomes king, the top captured
	 * piece is attatched to the king piece
	 **/
	capturedPlayerPieces: CheckerPiece[];
	/**
	 * Holds opponent pieces captured by player pieces.
	 * When an opponent piece becomes king, the top captured
	 * piece is attatched to the king piece
	 **/
	capturedOpponentPieces: CheckerPiece[];
	turn: CheckerPieceOwner;
	pieceOnCaptureSequence: CheckerPiece | null;
	pieceCaptureSequences: MoveSequence[];
	captureSequenceMoveIndex: number;
	/**
	 * Holds pieces to be captured by then end of turn
	 * when captureStrategy is remove-piece-at-end-of-turn
	 **/
	pendingCaptureMoves: Move[];
}

export class CheckersGame {
	difficulty: CheckersDifficulty;
	history: Undoable<CheckersGameState>;
	totalMoves = $state(0);
	rules: CheckerRules;
	private _requestedDraw = $state(false);
	private globalId = 0;
	private _selectedPiece: CheckerPiece | null = $state(null);
	private _pendingSequence: MoveSequence | null = null;

	constructor({ difficulty, rules, grid }: CheckerProps) {
		this.difficulty = difficulty;
		this.rules = rules;
		this.history = new Undoable({
			grid:
				grid || new Grid<CheckerPiece>({ rows: rules.size, columns: rules.size, reactive: false }),
			capturedOpponentPieces: [],
			capturedPlayerPieces: [],
			turn: 'player',
			pieceOnCaptureSequence: null,
			pieceCaptureSequences: [],
			captureSequenceMoveIndex: 0,
			pendingCaptureMoves: [],
		});

		if (!grid) {
			this.initGrid();
		}
	}

	get size() {
		return this.rules.size;
	}

	get canRequestDraw(): boolean {
		if (this.isOver()) {
			return false;
		}

		if (this.history.timeline.past.length > this.size * this.size * 1.5) {
			return true;
		}

		// If every piece is king, the player can request a draw
		return this.history.state.grid.grid.every((row) => {
			return row.every((piece) => {
				if (!piece) {
					return true;
				}

				return piece.isKing;
			});
		});
	}

	requestDraw() {
		this._requestedDraw = true;
	}

	private initGrid() {
		if (this.size % 2 !== 0) {
			throw new Error(`Cannot init board with odd size ${this.size}`);
		}

		const filledRows = this.size / 2 - 1;

		if (filledRows <= 0) {
			throw new Error(`Cannot init board with size ${this.size}`);
		}

		for (let row = 0; row < filledRows; row++) {
			for (let column = 0; column < this.size; column++) {
				if ((row + column) % 2 !== 0) {
					this.grid.set(
						{ row, column },
						new CheckerPiece({ owner: 'opponent', id: this.globalId++ }),
					);
				}
			}
		}

		for (let row = this.size - filledRows; row < this.size; row++) {
			for (let column = 0; column < this.size; column++) {
				if ((row + column) % 2 !== 0) {
					this.grid.set(
						{ row, column },
						new CheckerPiece({ owner: 'player', id: this.globalId++ }),
					);
				}
			}
		}
	}

	getPiecePositionOnGrid(piece: CheckerPiece): GridItem | null {
		return CheckersGame.getPiecePositionOnGrid({ piece, grid: this.grid });
	}

	getPieceById(id: CheckerPieceId): CheckerPiece | null {
		return (
			this.grid.find((item) => item?.id === id)?.item ||
			(this.capturedPlayerPieces.find((piece) => piece.id === id) ?? null) ||
			(this.capturedOpponentPieces.find((piece) => piece.id === id) ?? null)
		);
	}

	get turnPieces() {
		return this.grid.grid.flatMap((row) => {
			return row.filter((piece) => piece !== null && piece.owner === this.turn) as CheckerPiece[];
		});
	}

	get playerPiecesOnGrid() {
		return this.grid.grid.flatMap((row) => {
			return row.filter((piece) => piece !== null && piece.owner === 'player') as CheckerPiece[];
		});
	}

	get opponentPiecesOnGrid() {
		return this.grid.grid.flatMap((row) => {
			return row.filter((piece) => piece !== null && piece.owner === 'opponent') as CheckerPiece[];
		});
	}

	get captureSequencesMap() {
		return CheckersGame.getCaptureSequencesMap({
			pieces: this.turnPieces,
			rules: this.rules,
			state: this.history.state,
		});
	}

	get playerHasCaptureMovesOnTurn() {
		return (
			this.turn === 'player' &&
			Array.from(this.captureSequencesMap.values()).some((sequences) => sequences.length > 0)
		);
	}

	get maxCaptureSequenceMap() {
		return CheckersGame.getMaxCaptureSequencesMap({
			pieces: this.turnPieces,
			rules: this.rules,
			state: this.history.state,
			captureSequencesMap: this.captureSequencesMap,
		});
	}

	get captureSequenceAccordingToRulesMap() {
		return this.rules.mandatoryCapture && this.rules.maximizeCaptureSequence
			? this.maxCaptureSequenceMap
			: this.captureSequencesMap;
	}

	get selectedPieceCaptureSequences() {
		if (!this.selectedPiece) {
			return [];
		}

		if (this.pieceOnCaptureSequence && !this.pieceOnCaptureSequence.isSame(this.selectedPiece)) {
			return [];
		}

		if (this.pieceCaptureSequences.length > 0) {
			return this.pieceCaptureSequences;
		}

		const position = this.getPiecePositionOnGrid(this.selectedPiece);

		if (!position) {
			return [];
		}

		return this.captureSequenceAccordingToRulesMap.get(this.selectedPiece.id) ?? [];
	}

	get allowedPieceMovesForTurn(): Map<CheckerPieceId, Move[]> {
		if (this.pieceOnCaptureSequence && this.pieceCaptureSequences.length > 0) {
			const pieceMoves = this.pieceCaptureSequences
				.map((sequence) => {
					const nextMove = sequence.moves[this.captureSequenceMoveIndex];

					return nextMove;
				})
				.filter(Boolean);

			if (pieceMoves.length === 0) {
				return new Map<CheckerPieceId, Move[]>();
			}

			return new Map<CheckerPieceId, Move[]>([[this.pieceOnCaptureSequence.id, pieceMoves]]);
		}

		const map = new Map<CheckerPieceId, Move[]>();

		if (this.rules.mandatoryCapture) {
			if (this.rules.maximizeCaptureSequence) {
				const sequenceMap = CheckersGame.getMaxCaptureSequencesMap({
					pieces: this.turnPieces,
					state: this.history.state,
					rules: this.rules,
				});

				Array.from(sequenceMap.entries()).forEach(([id, sequences]) => {
					map.set(
						id,
						sequences.map((sequence) => sequence.moves[0]),
					);
				});
			} else {
				this.turnPieces.forEach((piece) => {
					let pieceMoves = this.getPossibleMoves(piece, { captureOnly: true });

					if (pieceMoves.length > 0) {
						map.set(piece.id, pieceMoves);
					}
				});
			}
		}

		if (map.size === 0) {
			// No capture moves, allow any move
			this.turnPieces.forEach((piece) => {
				let pieceMoves = this.getPossibleMoves(piece);

				if (pieceMoves.length > 0) {
					map.set(piece.id, pieceMoves);
				}
			});
		}

		return map;
	}

	get selectedPieceMoves(): Move[] {
		if (this.selectedPiece) {
			return this.allowedPieceMovesForTurn.get(this.selectedPiece.id) ?? [];
		}

		return [];
	}

	get winner(): CheckerWinner | null {
		if (this._requestedDraw) {
			return 'draw';
		}

		if (this.allowedPieceMovesForTurn.size === 0) {
			if (this.turn === 'player') {
				return 'opponent';
			} else {
				return 'player';
			}
		}

		return null;
	}

	private static getMaxCaptureSequencesMap({
		pieces,
		state,
		rules,
		captureSequencesMap: captureSequencesMapFromProps,
	}: {
		state: CheckersGameState;
		rules: CheckerRules;
		pieces: CheckerPiece[];
		captureSequencesMap?: Map<CheckerPieceId, MoveSequence[]>;
	}) {
		const captureSequencesMap =
			captureSequencesMapFromProps || CheckersGame.getCaptureSequencesMap({ pieces, state, rules });
		const map = new Map<CheckerPieceId, MoveSequence[]>();

		let maxSequenceSize = -Infinity;

		Array.from(captureSequencesMap.values()).forEach((sequences) => {
			maxSequenceSize = Math.max(
				maxSequenceSize,
				...sequences.map((sequence) => sequence.moves.length),
			);
		});

		Array.from(captureSequencesMap.entries()).forEach(([id, sequences]) => {
			const maxSequences = sequences.filter(
				(sequence) => sequence.moves.length === maxSequenceSize,
			);

			if (maxSequences.length > 0) {
				map.set(id, maxSequences);
			}
		});

		return map;
	}

	private static getCaptureSequencesMap({
		pieces,
		state,
		rules,
	}: {
		state: CheckersGameState;
		rules: CheckerRules;
		pieces: CheckerPiece[];
	}): Map<CheckerPieceId, MoveSequence[]> {
		const captureSequencesMap = new Map<CheckerPieceId, MoveSequence[]>();

		pieces.forEach((piece) => {
			const sequences = CheckersGame.getCaptureSequences({
				piece,
				state,
				rules,
			});

			captureSequencesMap.set(piece.id, sequences);
		});

		return captureSequencesMap;
	}

	/**
	 * Get all move sequences the piece can do, respecting the rules.
	 **/
	private getPossibleMoves(piece: CheckerPiece, options?: { captureOnly: boolean }): Move[] {
		return CheckersGame.getPossibleMoves({
			piece,
			captureOnly: options?.captureOnly ?? false,
			state: this.history.state,
			rules: this.rules,
		});
	}

	private static getCaptureSequences({
		state,
		piece,
		rules,
		currentSequence = { moves: [] },
	}: {
		state: CheckersGameState;
		piece?: CheckerPiece | null;
		rules: CheckerRules;
		currentSequence?: MoveSequence;
	}): MoveSequence[] {
		if (!piece) {
			return currentSequence?.moves.length > 0 ? [currentSequence] : [];
		}

		const captureMoves = CheckersGame.getPossibleMoves({
			state,
			rules,
			piece,
			captureOnly: true,
		});

		if (captureMoves.length === 0) {
			return currentSequence?.moves.length > 0 ? [currentSequence] : [];
		}

		const sequences: MoveSequence[] = [];

		captureMoves.forEach((move) => {
			const nextState = CheckersGame.moveTo({
				state,
				position: move,
				rules,
				selectedPiece: piece,
				possibleMoves: captureMoves,
			});

			if (nextState) {
				sequences.push(
					...CheckersGame.getCaptureSequences({
						state: nextState.state,
						piece: nextState.selectedPiece,
						rules,
						currentSequence: {
							moves: [...currentSequence.moves, move],
						},
					}),
				);
			}
		});

		return sequences;
	}

	private static getPiecePositionOnGrid({
		piece,
		grid,
	}: {
		piece: CheckerPiece;
		grid: Grid<CheckerPiece>;
	}): GridItem | null {
		return grid.find((item) => piece.isSame(item))?.gridItem;
	}

	private static getMovesWithoutCaptureOnDirection({
		direction,
		grid,
		piece,
		rules,
	}: {
		direction: AnyDirection;
		grid: Grid<CheckerPiece>;
		piece: CheckerPiece;
		rules: CheckerRules;
	}): Move[] {
		const position = CheckersGame.getPiecePositionOnGrid({ piece, grid });

		if (!position) {
			return [];
		}

		const moves: Move[] = [];
		const movesOnDirection = grid.getAllGridItemsOnDirection(
			position,
			direction,
			({ item, distance }) => {
				if (!piece.isKing && distance > 1) {
					return false;
				}
				if (piece.isKing && !rules.allowLongKingMoves && distance > 1) {
					return false;
				}
				// The next position has no piece in it, so the move is valid
				if (item === null) {
					return true;
				}

				return false;
			},
		);

		if (movesOnDirection.length > 0) {
			moves.push(...movesOnDirection);
		}

		return moves;
	}

	private static getCaptureMovesOnDirection = ({
		piece,
		direction,
		state,
		rules,
	}: {
		piece: CheckerPiece;
		direction: AnyDirection;
		state: CheckersGameState;
		rules: CheckerRules;
	}): Move[] => {
		const position = CheckersGame.getPiecePositionOnGrid({ piece, grid: state.grid });

		if (!position) {
			return [];
		}

		const captureOnDirection: Move[] = [];

		state.grid.getAllGridItemsOnDirection(
			position,
			direction,
			({ distance, item, position, previousItem, previousPosition }) => {
				if (!piece.isKing && distance > 2) {
					return false;
				}
				if (piece.isKing && !rules.allowLongKingMoves && distance > 2) {
					return false;
				}

				// There are two consecutive items, cannot capture
				if (previousItem && item) {
					return false;
				}

				// Previous item was a piece from the same player
				if (previousItem && previousItem.owner === piece.owner) {
					return false;
				}

				// Previous item was an opponent piece and current item is null
				// so the movement is valid
				if (
					previousItem &&
					previousItem.owner !== piece.owner &&
					!item &&
					previousPosition &&
					// Cannot capture previously captured pieces
					!state.pendingCaptureMoves.some((move) => move.capture?.isSame(previousItem))
				) {
					captureOnDirection.push({
						...position,
						capture: previousItem,
						capturePosition: previousPosition,
					});

					if (rules.allowLongKingMoves && rules.allowLongKingMovesAfterCapture && piece.isKing) {
						const validMoves = state.grid
							.getAllGridItemsOnDirection(position, direction, ({ item: nextItem }) => {
								if (nextItem) {
									return false;
								}

								return true;
							})
							.map((move) => {
								return {
									...move,
									capture: previousItem,
									capturePosition: previousPosition,
								};
							});

						captureOnDirection.push(...validMoves);
					}

					return false;
				}

				return true;
			},
		);

		return captureOnDirection;
	};

	private static getPossibleMoves({
		state,
		rules,
		piece,
		captureOnly = false,
	}: {
		state: CheckersGameState;
		rules: CheckerRules;
		piece: CheckerPiece;
		captureOnly?: boolean;
	}): Move[] {
		const position = CheckersGame.getPiecePositionOnGrid({ piece, grid: state.grid });

		if (!position) {
			return [];
		}

		const moves: Move[] = [];

		if (!captureOnly) {
			const directionsWithoutCapture: AnyDirection[] = piece.isKing
				? ['up-left', 'up-right', 'down-left', 'down-right']
				: piece.owner === 'player'
					? ['up-left', 'up-right']
					: ['down-left', 'down-right'];

			const movesWithoutCapture: Move[] = directionsWithoutCapture.flatMap((direction) =>
				CheckersGame.getMovesWithoutCaptureOnDirection({
					direction,
					grid: state.grid,
					piece,
					rules,
				}),
			);

			if (movesWithoutCapture.length > 0) {
				moves.push(...movesWithoutCapture);
			}
		}

		const directionsWithCapture: AnyDirection[] =
			piece.isKing || rules.canCaptureBackwards
				? ['up-left', 'up-right', 'down-left', 'down-right']
				: piece.owner === 'player'
					? ['up-left', 'up-right']
					: ['down-left', 'down-right'];

		const getAllCaptures = (): Move[] => {
			return directionsWithCapture
				.flatMap((direction) => {
					let captures = CheckersGame.getCaptureMovesOnDirection({
						state,
						direction,
						piece,
						rules,
					});

					return captures;
				})
				.filter(Boolean);
		};

		moves.push(...getAllCaptures());

		return moves;
	}

	private static isOnPromoteToKingRegion({
		piece,
		grid,
	}: {
		piece: CheckerPiece;
		grid: Grid<CheckerPiece>;
	}) {
		const position = CheckersGame.getPiecePositionOnGrid({ piece, grid });

		if (!position) {
			return false;
		}

		if (piece.owner === 'player') {
			return position.row === 0;
		} else {
			return position.row === grid.size.rows - 1;
		}
	}

	private static getPromotedToKingStateIfNeeded({
		piece,
		state,
		rules,
	}: {
		piece: CheckerPiece;
		state: CheckersGameState;
		rules: CheckerRules;
	}) {
		const piecePosition = CheckersGame.getPiecePositionOnGrid({ piece, grid: state.grid });

		if (!piecePosition) {
			return null;
		}

		if (CheckersGame.isOnPromoteToKingRegion({ piece, grid: state.grid }) && !piece.isKing) {
			if (
				rules.promoteStrategy === 'promote-if-capture-sequence-ended' &&
				state.pieceOnCaptureSequence
			) {
				return null;
			}

			const nextState: CheckersGameState = {
				grid: untrack(() => state.grid.clone()),
				capturedOpponentPieces: [...state.capturedOpponentPieces],
				capturedPlayerPieces: [...state.capturedPlayerPieces],
				pieceOnCaptureSequence: state.pieceOnCaptureSequence,
				turn: state.turn,
				pieceCaptureSequences: state.pieceCaptureSequences,
				captureSequenceMoveIndex: state.captureSequenceMoveIndex,
				pendingCaptureMoves: state.pendingCaptureMoves,
			};

			const availablePiece =
				(piece.owner === 'player'
					? nextState.capturedPlayerPieces.pop()
					: nextState.capturedOpponentPieces.pop()) ||
				new CheckerPiece({
					owner: piece.owner,
					isSpare: true,
					id: 1000 + Math.random() * 1e20,
				});

			const { self: newPiece } = piece.cloneAndAttatch(availablePiece);

			nextState.grid.set(piecePosition, newPiece);

			if (state.pieceOnCaptureSequence) {
				if (rules.promoteStrategy === 'promote-and-continue-capture-sequence') {
					nextState.turn = state.pieceOnCaptureSequence.owner;
				} else if (rules.promoteStrategy === 'promote-and-stop-capture-sequence') {
					nextState.turn = state.pieceOnCaptureSequence.owner === 'player' ? 'opponent' : 'player';
					nextState.pieceOnCaptureSequence = null;
					nextState.pieceCaptureSequences = [];
					nextState.captureSequenceMoveIndex = 0;

					CheckersGame.commitPendingCaptureMoves({ state: nextState });
				}
			}

			if (nextState.pieceOnCaptureSequence) {
				nextState.pieceOnCaptureSequence = newPiece;
			}

			return nextState;
		}

		return null;
	}

	private static commitPendingCaptureMoves({ state }: { state: CheckersGameState }) {
		if (state.pendingCaptureMoves.length === 0) {
			return;
		}

		state.pendingCaptureMoves.forEach((move) => {
			CheckersGame.commitCapture({ move, state });
		});

		state.pendingCaptureMoves = [];
	}

	private static commitCapture({
		move,
		state,
	}: {
		move: Pick<Move, 'capture' | 'capturePosition'>;
		state: Pick<CheckersGameState, 'capturedOpponentPieces' | 'capturedPlayerPieces' | 'grid'>;
	}) {
		if (move.capture && move.capturePosition) {
			const pile =
				move.capture.owner === 'opponent'
					? state.capturedOpponentPieces
					: state.capturedPlayerPieces;

			state.grid.set(move.capturePosition, null);

			if (move.capture.isKing) {
				const { self: captured, other } = move.capture.cloneAndDettach();
				pile.push(
					...([captured, other].filter(Boolean).filter((p) => !p?.isSpare) as CheckerPiece[]),
				);
			} else {
				pile.push(move.capture);
			}
		}
	}

	private static moveTo({
		state,
		position,
		rules,
		selectedPiece,
		possibleMoves,
	}: {
		position: GridItem;
		selectedPiece: CheckerPiece;
		state: CheckersGameState;
		rules: CheckerRules;
		possibleMoves: Move[];
	}): {
		state: CheckersGameState;
		selectedPiece: CheckerPiece | null;
	} | null {
		const move = possibleMoves.find((move) => isSameGridItem(move, position));

		if (!move || !selectedPiece) {
			return null;
		}

		const newGrid = untrack(() => state.grid.clone());
		let newCapturedPlayerPieces = [...state.capturedPlayerPieces];
		let newCapturedOpponentPieces = [...state.capturedOpponentPieces];
		let newPendingCaptureMoves = [...state.pendingCaptureMoves];

		// Update selected piece location
		newGrid.set(
			CheckersGame.getPiecePositionOnGrid({ piece: selectedPiece, grid: newGrid })!,
			null,
		);
		newGrid.set(move, selectedPiece);

		// Create next state
		let nextState: CheckersGameState = {
			...state,
			grid: newGrid,
			capturedOpponentPieces: newCapturedOpponentPieces,
			capturedPlayerPieces: newCapturedPlayerPieces,
			pendingCaptureMoves: newPendingCaptureMoves,
			pieceCaptureSequences: [...state.pieceCaptureSequences],
		};

		if (move.capture) {
			if (rules.captureStrategy === 'remove-piece-at-capture') {
				CheckersGame.commitCapture({
					move,
					state: nextState,
				});
			} else {
				newPendingCaptureMoves.push(move);
			}
		}

		const pieceOnCaptureSequence =
			move.capture &&
			CheckersGame.getPossibleMoves({
				state: nextState,
				piece: selectedPiece,
				rules: rules,
				captureOnly: true,
			}).length > 0
				? selectedPiece
				: null;

		if (pieceOnCaptureSequence) {
			nextState.pieceOnCaptureSequence = pieceOnCaptureSequence;
			nextState.captureSequenceMoveIndex += 1;
		} else {
			nextState.turn = state.turn === 'player' ? 'opponent' : 'player';
			nextState.pieceOnCaptureSequence = null;
			nextState.pieceCaptureSequences = [];
			nextState.captureSequenceMoveIndex = 0;
		}

		const stateWithPromotedToKingPiece = CheckersGame.getPromotedToKingStateIfNeeded({
			rules: rules,
			piece: selectedPiece,
			state: nextState,
		});

		if (stateWithPromotedToKingPiece) {
			nextState = stateWithPromotedToKingPiece;
		}

		if (rules.captureStrategy === 'remove-piece-at-end-of-turn' && state.turn !== nextState.turn) {
			CheckersGame.commitPendingCaptureMoves({ state: nextState });
		}

		let nextSelectedPiece: CheckerPiece | null = null;

		if (pieceOnCaptureSequence) {
			nextSelectedPiece = nextState.pieceOnCaptureSequence;
		}

		return {
			selectedPiece: nextSelectedPiece,
			state: nextState,
		};
	}

	move(position: GridItem) {
		if (this.hasPendingSequenceMoves) {
			return false;
		}

		return this.moveToPositionOnSequence(position) || this.moveTo(position);
	}

	private moveToPositionOnSequence(position: GridItem): boolean {
		if (!this.selectedPiece || this.selectedPieceCaptureSequences.length === 0) {
			return false;
		}

		const sequence = (
			this.pieceCaptureSequences.length > 0
				? this.pieceCaptureSequences
				: this.selectedPieceCaptureSequences
		).find((sequence) =>
			sequence.moves.some((move, index) => {
				if (index < this.captureSequenceMoveIndex) {
					return false;
				}

				return isSameGridItem(move, position);
			}),
		);

		if (sequence && sequence.moves.length > 0) {
			const targetIndex = sequence.moves.findIndex(
				(move, i) => isSameGridItem(move, position) && i >= this.captureSequenceMoveIndex,
			);

			const pendingSequence: MoveSequence = {
				...sequence,
				moves: sequence.moves.filter((_, i) => {
					if (i < this.captureSequenceMoveIndex || i > targetIndex) {
						return false;
					}

					return true;
				}),
			};

			const nextMove = pendingSequence.moves.shift();

			if (pendingSequence.moves.length > 0) {
				this._pendingSequence = pendingSequence;
			} else {
				this._pendingSequence = null;
			}

			if (nextMove) {
				return this.moveTo(nextMove);
			}
		}

		return false;
	}

	moveToNextOnPendingSequence() {
		if (!this._pendingSequence) {
			return;
		}

		const move = this._pendingSequence.moves.shift();

		if (this._pendingSequence.moves.length === 0) {
			this._pendingSequence = null;
		}

		if (move) {
			return this.moveTo(move);
		}

		return false;
	}

	private moveTo(position: GridItem): boolean {
		if (!this.selectedPiece) {
			return false;
		}

		const nextState = CheckersGame.moveTo({
			state: this.history.state,
			position,
			possibleMoves: this.selectedPieceMoves,
			rules: this.rules,
			selectedPiece: this.selectedPiece,
		});

		if (!nextState) {
			return false;
		}

		const justStartedCaptureSequence =
			!this.pieceOnCaptureSequence && nextState.state.pieceOnCaptureSequence;

		if (justStartedCaptureSequence) {
			if (this.rules.mandatoryCapture && this.rules.maximizeCaptureSequence) {
				const map = CheckersGame.getMaxCaptureSequencesMap({
					pieces: this.turnPieces,
					state: this.history.state,
					rules: this.rules,
				});

				const sequences = map.get(this.selectedPiece.id);

				if (sequences) {
					nextState.state.pieceCaptureSequences = sequences;
				}
			} else {
				nextState.state.pieceCaptureSequences = CheckersGame.getCaptureSequences({
					state: this.history.state,
					rules: this.rules,
					piece: this.selectedPiece,
				}).filter((sequence) => isSameGridItem(sequence.moves[0], position));
			}
		}

		this._selectedPiece = nextState.selectedPiece;
		this.history.add(nextState.state);
		this.totalMoves += 1;

		return true;
	}

	toggleSelectedPiece(piece: CheckerPiece | null) {
		if (this.selectedPiece && piece && this.selectedPiece.isSame(piece)) {
			this.selectedPiece = null;
		} else {
			this.selectedPiece = piece;
		}
	}

	get pendingSequence() {
		return this._pendingSequence;
	}

	get hasPendingSequenceMoves() {
		return !!this._pendingSequence && this._pendingSequence.moves.length > 0;
	}

	get selectedPiece() {
		return this._selectedPiece;
	}

	set selectedPiece(piece: CheckerPiece | null) {
		if (this.hasPendingSequenceMoves) {
			return;
		}

		if (piece) {
			if (piece.owner !== this.turn) {
				return;
			}

			if (!this.allowedPieceMovesForTurn.has(piece.id)) {
				return;
			}
		} else {
			if (!this.rules.mandatoryCapture && this.pieceOnCaptureSequence && this.selectedPiece) {
				let nextState: CheckersGameState = {
					grid: this.grid,
					capturedOpponentPieces: this.capturedOpponentPieces,
					capturedPlayerPieces: this.capturedPlayerPieces,
					pieceOnCaptureSequence: null,
					pieceCaptureSequences: [],
					turn: this.turn === 'player' ? 'opponent' : 'player',
					captureSequenceMoveIndex: 0,
					pendingCaptureMoves: this.history.state.pendingCaptureMoves,
				};

				const stateWithPromotedToKingPiece = CheckersGame.getPromotedToKingStateIfNeeded({
					piece: this.selectedPiece,
					state: nextState,
					rules: this.rules,
				});

				if (stateWithPromotedToKingPiece) {
					nextState = stateWithPromotedToKingPiece;
				}

				if (this.rules.captureStrategy === 'remove-piece-at-end-of-turn') {
					nextState.capturedPlayerPieces = [...nextState.capturedPlayerPieces];
					nextState.capturedOpponentPieces = [...nextState.capturedOpponentPieces];
					CheckersGame.commitPendingCaptureMoves({
						state: nextState,
					});
				}

				this.history.add(nextState);
				this._selectedPiece = null;

				return;
			}
		}

		const pieceOnGameState = piece ? this.getPieceById(piece.id) : null;

		this._selectedPiece = pieceOnGameState;
	}

	get moves(): number {
		return this.history.timeline.past.length;
	}

	get grid() {
		return this.history.state.grid;
	}

	get pieceOnCaptureSequence() {
		return this.history.state.pieceOnCaptureSequence;
	}

	get pieceCaptureSequences() {
		return this.history.state.pieceCaptureSequences;
	}

	get captureSequenceMoveIndex() {
		return this.history.state.captureSequenceMoveIndex;
	}

	get capturedPlayerPieces() {
		return this.history.state.capturedPlayerPieces;
	}

	get capturedOpponentPieces() {
		return this.history.state.capturedOpponentPieces;
	}

	get turn() {
		return this.history.state.turn;
	}

	undo({ untilTurn = false }: { untilTurn?: boolean }) {
		if (!this.canUndo()) {
			return;
		}

		const initialTurn = this.turn;
		this.history.undo();
		this.totalMoves += 1;

		if (untilTurn) {
			while (this.canUndo() && this.turn !== initialTurn) {
				this.history.undo();
				this.totalMoves += 1;
			}
		}

		if (this.pieceOnCaptureSequence) {
			this._selectedPiece = this.pieceOnCaptureSequence;
		} else {
			this._selectedPiece = null;
		}
	}

	redo({ untilTurn = false }: { untilTurn?: boolean }) {
		if (!this.canRedo()) {
			return;
		}

		const initialTurn = this.turn;
		this.history.redo();
		this.totalMoves += 1;

		if (untilTurn) {
			while (this.canRedo() && this.turn !== initialTurn) {
				this.history.redo();
				this.totalMoves += 1;
			}
		}

		if (this.pieceOnCaptureSequence) {
			this._selectedPiece = this.pieceOnCaptureSequence;
		} else {
			this._selectedPiece = null;
		}
	}

	reset() {
		this.history.reset();
		this.totalMoves = 0;
		this._requestedDraw = false;
	}

	canUndo() {
		return !this.isOver() && this.history.canUndo() && !this.hasPendingSequenceMoves;
	}

	canRedo() {
		return !this.isOver() && this.history.canRedo() && !this.hasPendingSequenceMoves;
	}

	isOver() {
		return this.winner !== null;
	}

	cloneWithoutFullHistory() {
		const clone = new CheckersGame({
			difficulty: this.difficulty,
			rules: this.rules,
			grid: this.grid.clone((piece) => (piece ? piece.clone() : null)),
		});

		if (this._pendingSequence) {
			clone._pendingSequence = {
				...this._pendingSequence,
				moves: this._pendingSequence.moves.map((move) => move),
			};
		}

		if (this._selectedPiece) {
			clone._selectedPiece = this._selectedPiece.clone();
		}

		const cloneState = clone.history.state;
		const state = this.history.state;

		cloneState.capturedOpponentPieces = [...state.capturedOpponentPieces];
		cloneState.capturedPlayerPieces = [...state.capturedPlayerPieces];
		cloneState.pieceOnCaptureSequence = state.pieceOnCaptureSequence;
		cloneState.pieceCaptureSequences = [...state.pieceCaptureSequences];
		cloneState.turn = state.turn;
		cloneState.captureSequenceMoveIndex = state.captureSequenceMoveIndex;
		cloneState.pendingCaptureMoves = [...state.pendingCaptureMoves];

		return clone;
	}

	toJson(): CheckersGameJson {
		return {
			grid: this.grid.grid.map((row) => {
				return row.map((piece) => (piece === null ? null : piece.toJson()));
			}),
			capturedPlayerPieces: this.capturedPlayerPieces.map((piece) => piece.toJson()),
			capturedOpponentPieces: this.capturedOpponentPieces.map((piece) => piece.toJson()),
			turn: this.turn,
			pieceOnCaptureSequence: this.pieceOnCaptureSequence?.toJson(),
			pieceCaptureSequences: this.pieceCaptureSequences.map((sequence) => ({
				...sequence,
				moves: sequence.moves.map((move) => ({
					...move,
					capture: move.capture ? move.capture.toJson() : undefined,
				})),
			})),
			captureSequenceMoveIndex: this.captureSequenceMoveIndex,
			pendingCaptureMoves: this.history.state.pendingCaptureMoves.map((move) => ({
				...move,
				capture: move.capture ? move.capture.toJson() : undefined,
			})),
			difficulty: this.difficulty,
			rules: this.rules,
		};
	}

	static fromJson(json: CheckersGameJson): CheckersGame {
		const game = new CheckersGame({
			difficulty: json.difficulty,
			rules: json.rules,
		});

		const restoredGame = game;

		json.grid.forEach((row, rowIndex) =>
			row.forEach((item, columnIndex) => {
				if (item === null) {
					return restoredGame.grid.set({ row: rowIndex, column: columnIndex }, null);
				}

				const piece = CheckerPiece.fromJson(item);
				restoredGame.grid.set({ row: rowIndex, column: columnIndex }, piece);
			}),
		);

		restoredGame.history.state.capturedPlayerPieces = json.capturedPlayerPieces.map(
			CheckerPiece.fromJson,
		);
		restoredGame.history.state.capturedOpponentPieces = json.capturedOpponentPieces.map(
			CheckerPiece.fromJson,
		);
		restoredGame.history.state.turn = json.turn;
		restoredGame.history.state.pieceOnCaptureSequence = json.pieceOnCaptureSequence
			? CheckerPiece.fromJson(json.pieceOnCaptureSequence)
			: null;
		restoredGame.history.state.pieceCaptureSequences = json.pieceCaptureSequences.map(
			(sequence) => ({
				...sequence,
				moves: sequence.moves.map((move) => ({
					...move,
					capture: move.capture ? CheckerPiece.fromJson(move.capture) : undefined,
				})),
			}),
		);
		restoredGame.history.state.captureSequenceMoveIndex = json.captureSequenceMoveIndex;
		restoredGame.history.state.pendingCaptureMoves = json.pendingCaptureMoves.map((move) => ({
			...move,
			capture: move.capture ? CheckerPiece.fromJson(move.capture) : undefined,
		}));

		return restoredGame;
	}
}
