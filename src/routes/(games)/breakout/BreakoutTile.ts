import type { HitBox } from '$lib/util/HitBox';

export class BreakoutTile {
	box: HitBox;
	alive = true;
	color: string;
	private context: CanvasRenderingContext2D;
	private border = 1 * window.devicePixelRatio;

	constructor(box: HitBox, color: string, canvas: HTMLCanvasElement) {
		this.context = canvas.getContext('2d')!;
		this.box = box;
		this.color = color;
	}

	resize() {
		//
	}

	draw() {
		if (!this.alive) {
			return;
		}

		const context = this.context;
		const { x, y, w, h } = this.box;
		const border = this.border;

		context.fillStyle = this.color;
		context.fillRect(x + border, y + border, w - 2 * border, h - 2 * border);
	}
}
