<script lang="ts">
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import SettingsIcon from '$lib/components/Icons/SettingsIcon.svelte';
	import Range from '$lib/components/Range/Range.svelte';
	import { BreakoutGame } from './BreakoutGame.svelte';

	interface Props {
		game: BreakoutGame | undefined | null;
	}

	let { game = $bindable() }: Props = $props();

	let open = $state(false);

	function handleMouseSensitivityChange(event: Event) {
		if (game) {
			const percentage =
				+(
					((event as InputEvent).currentTarget as HTMLInputElement)?.value || game?.mouseSensitivity
				) / 100;
			const range = BreakoutGame.maxMouseSensitivity - BreakoutGame.minMouseSentitivity;
			const value = BreakoutGame.minMouseSentitivity + percentage * range;

			game.mouseSensitivity = value;
		}
	}

	let mouseSensitivity = $derived(
		100 *
			(((game?.mouseSensitivity ?? BreakoutGame.minMouseSentitivity) -
				BreakoutGame.minMouseSentitivity) /
				(BreakoutGame.maxMouseSensitivity - BreakoutGame.minMouseSentitivity)),
	);
</script>

{#if game?.supportsMouseSensitivity}
	<Dropdown bind:open>
		<DropdownButton class="btn-sm">
			<SettingsIcon class="size-5" />
		</DropdownButton>

		<DropdownContent class="gap-2 flex flex-col">
			<DropdownItem class="flex cursor-pointer flex-col items-start gap-2">
				<label for="mouse-sensitivity" class="label text-sm text-base-content py-2">
					Mouse Sensitivity
				</label>

				<Range
					id="mouse-sensitivity"
					class="range-xs"
					value={mouseSensitivity}
					onChange={handleMouseSensitivityChange}
				/>
			</DropdownItem>

			<DropdownItem>
				<button class="btn btn-ghost btn-sm" onclick={() => game?.resetSettings()}>Reset</button>
			</DropdownItem>
		</DropdownContent>
	</Dropdown>
{/if}
