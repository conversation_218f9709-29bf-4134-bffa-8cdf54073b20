<script lang="ts">
	import { fade } from 'svelte/transition';
	import { onMount, onDestroy } from 'svelte';
	import { breakoutSoundResources } from './breakoutSoundResources';
	import MouseLeftIcon from '$lib/components/Icons/MouseLeftIcon.svelte';
	import TouchIcon from '$lib/components/Icons/TouchIcon.svelte';
	import BreakoutSettingsButton from './BreakoutSettingsButton.svelte';
	import BreakoutDifficultyButton from './BreakoutDifficultyButton.svelte';
	import { type BreakoutDifficulty, BreakoutGame } from './BreakoutGame.svelte';
	import GameLayout from '$lib/components/GameLayout/GameLayout.svelte';
	import GameIsland from '$lib/components/GameIsland/GameIsland.svelte';
	import { Stats } from '$lib/util/Stats.svelte';
	import capitalize from 'lodash/capitalize';
	import { wait } from '$lib/functions/wait';
	import { GameContext } from '$lib/util/GameContext/GameContext.svelte';
	import BreakoutInfoModal from './BreakoutInfoModal.svelte';
	import InfoSolidIcon from '$lib/components/Icons/InfoSolidIcon.svelte';
	import { Leaderboard } from '$lib/util/Leaderboard.svelte';
	import MoreGamesButton from '$lib/components/MoreGamesButton/MoreGamesButton.svelte';

	type GameScreen = 'instructions' | 'game' | 'game-over';

	let screen: GameScreen = $state('instructions');
	let canvas = $state<HTMLCanvasElement>();
	let isInfoModalOpen = $state(false);
	const gameOverDuration = 3000;
	const context = new GameContext({
		GameClass: BreakoutGame,
		gameKey: 'breakout',
		settings: {
			defaultSettings: {
				difficulty: 'normal' as BreakoutDifficulty,
				mouseSensitivity: 1,
			},
		},
		sounds: {
			resources: breakoutSoundResources,
		},
		formatted(context) {
			return {
				name: 'Breakout',
				variant: capitalize(
					context.game?.difficulty ?? context.settingsManager.defaultSettings.difficulty,
				),
			};
		},
		defaultGameProps(context) {
			return {
				canvas: canvas!,
				sounds: context.sounds,
				onGameOver,
				onStart,
				settingsManager: context.settingsManager,
				targetElement: canvas!,
				difficulty: context.settingsManager.settings.difficulty,
				timer: context.timer,
			};
		},
		stats({ props, context }) {
			return {
				stats: new Stats({
					...props,
					game: 'breakout',
					gameVariant: context.game?.difficulty ?? ('easy' as BreakoutDifficulty),
					liveStats: {
						score: {
							name: 'Score',
							unit: 'plain',
							value() {
								return context.game?.score ?? 0;
							},
							metrics: {
								total: {
									key: 'totalScore',
									name: 'Total Score',
								},
								average: {
									key: 'averageScore',
									name: 'Average Score',
								},
								max: {
									key: 'bestScore',
									name: 'Best Score',
									useAsBest: true,
								},
								min: {
									key: 'worstScore',
									name: 'Worst Score',
								},
							},
						},
					},
					initialPinnedStats: ['time', 'score'],
				}),
				visibleStats: ['bestScore', 'averageScore', 'totalGames'],
				canUpdateWithGameLost(game) {
					return game.started && screen !== 'game-over';
				},
			};
		},
		leaderboard(context) {
			return {
				leaderboard: new Leaderboard({
					game: context.gameKey,
					order: 'higher-first',
					firstAvailableDate: new Date('2025/05/08'),
				}),
				sendScoreOn: ['lost'],
				getScore(game) {
					return {
						score: game.score,
					};
				},
			};
		},
		onWillCreateGame({ context, newGameOptions, previousGame }) {
			previousGame?.dispose();
			context.settingsManager.settings.difficulty = newGameOptions.difficulty;
		},
		onGameCreated() {
			screen = 'instructions';
		},
		onDispose(context) {
			context.game?.dispose();
		},
	});

	let game = $derived(context.game);

	function onStart() {
		screen = 'game';
		context.timer.start();
	}

	async function onGameOver() {
		screen = 'game-over';
		context.handleGameOver('lost');

		await wait(gameOverDuration);

		context.createGame();
	}

	function createNewGameAndPlaySound(difficulty?: BreakoutDifficulty) {
		context.createGame({ difficulty });
		context.sounds.changeDifficulty.play();
	}

	onMount(() => {
		context.load();
	});

	onDestroy(() => {
		context.dispose();
	});
</script>

<BreakoutInfoModal bind:isOpen={isInfoModalOpen} />

<GameLayout noPadding>
	{#snippet Island()}
		<GameIsland {context} gameOverStrategy="best-stats-update" gameOverIslandDelay={0} />
	{/snippet}

	<section
		class="min-h-screen-no-navbar size-full flex touch-pinch-zoom select-none px-4"
		class:grayscale={screen === 'game-over'}
	>
		<div class="flex w-full items-center">
			<div
				class="relative mx-auto h-3/4 w-full max-w-5xl rounded-lg border-2 border-current aspect-video"
			>
				<canvas bind:this={canvas} class="size-full cursor-pointer rounded-md transition-opacity"
				></canvas>

				<div class="absolute -top-12 left-0 right-0 flex w-full items-center justify-between">
					<div class="flex-center gap-2">
						<BreakoutDifficultyButton
							difficulty={game?.difficulty ?? context.settingsManager.defaultSettings.difficulty}
							onChange={createNewGameAndPlaySound}
						/>

						<BreakoutSettingsButton {game} />

						<button class="btn btn-sm" onclick={() => (isInfoModalOpen = true)}>
							<InfoSolidIcon class="size-5" />
						</button>

						<MoreGamesButton class="btn-sm" />
					</div>

					<!-- Lives -->
					<div class="flex-center gap-2">
						{#each Array(game?.life).fill(0) as life}
							<div class="h-3 w-3 rounded-full bg-current"></div>
						{/each}
					</div>
				</div>

				{#if screen === 'instructions'}
					<div
						transition:fade
						class="card pointer-events-none absolute bottom-1/4 left-1/2 -translate-x-1/2 bg-base-100 p-4"
					>
						<!-- Desktop -->
						<div class="hidden items-end justify-center gap-2 text-center lg:flex">
							<div class="flex-center flex-col gap-2">
								<MouseLeftIcon width="56" height="56" />
								Click to start
							</div>

							<div class="divider divider-horizontal"></div>

							<div class="flex-center flex-col gap-2">
								<span></span>
								<kbd class="kbd kbd-lg px-8">space</kbd>
								Press space to Pause
							</div>
						</div>

						<!-- Mobile -->
						<div class="justify-content flex flex-col items-center gap-2 text-center lg:hidden">
							<TouchIcon width="56" height="56" />
							Touch to start
						</div>
					</div>
				{/if}
			</div>
		</div>
	</section>
</GameLayout>
