import type { Point2D } from '$lib/models/Point2D';
import { HitBox } from '$lib/util/HitBox';

export class BreakoutBall {
	private canvas: HTMLCanvasElement;
	private context: CanvasRenderingContext2D;
	private initialSpeed: Point2D = {
		x: 1 * window.devicePixelRatio,
		y: -1 * window.devicePixelRatio,
	};
	private maxSpeed: number;
	speed: Point2D = new Proxy(
		{ ...this.initialSpeed },
		{
			set: (speed: Point2D, prop: keyof Point2D, value: number) => {
				if (!this.box.enabled) {
					return true;
				}

				if (prop === 'x') {
					speed.x = value > 0 ? Math.min(this.maxSpeed, value) : Math.max(-this.maxSpeed, value);
					return true;
				}
				if (prop === 'y') {
					speed.y = value > 0 ? Math.min(this.maxSpeed, value) : Math.max(-this.maxSpeed, value);
					return true;
				}

				return false;
			},
		},
	);
	box = new HitBox({
		x: 0,
		y: 0,
		w: this.size,
		h: this.size,
	});

	constructor(maxSpeedFactor: number, canvas: HTMLCanvasElement) {
		this.canvas = canvas;
		this.maxSpeed = maxSpeedFactor * window.devicePixelRatio;
		// eslint-disable-next-line @typescript-eslint/no-non-null-assertion
		this.context = canvas.getContext('2d')!;
	}

	get size() {
		return 15 * window.devicePixelRatio;
	}

	get stopped() {
		return !this.box.enabled;
	}

	get running() {
		return this.box.enabled;
	}

	stop() {
		this.box.enabled = false;
	}

	reset() {
		this.box.enabled = true;
		if (this.speed.x / this.initialSpeed.x < 0) {
			this.speed.x *= -1;
		}
		if (this.speed.y / this.initialSpeed.y < 0) {
			this.speed.y *= -1;
		}
	}

	limitBoxPosition() {
		this.box.position.x = Math.max(0, Math.min(this.canvas.width - this.box.w, this.box.x));
		this.box.position.y = Math.max(0, Math.min(this.canvas.height - this.box.h, this.box.y));
	}

	resize() {
		this.box.rect.w = this.size;
		this.box.rect.h = this.size;

		this.limitBoxPosition();
	}

	centralize(y: number) {
		this.box.position.x = (this.canvas.width - this.box.rect.w) / 2;
		this.box.position.y = y;

		this.limitBoxPosition();
	}

	draw() {
		if (this.stopped) {
			return;
		}

		const context = this.context;
		const { x, y, w, h } = this.box;
		const color = window.getComputedStyle(this.context.canvas).color;

		context.fillStyle = color;
		context.fillRect(x, y, w, h);
	}
}
