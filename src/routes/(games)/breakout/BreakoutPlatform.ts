import { HitBox } from '$lib/util/HitBox';

export class BreakoutPlatform {
	box = new HitBox();
	private _speedX = 0;
	private lastMoveTime = -1;
	private canvas: HTMLCanvasElement;
	private context: CanvasRenderingContext2D;
	private widthFactor: number;

	constructor(widthFactor: number, canvas: HTMLCanvasElement) {
		this.canvas = canvas;
		this.widthFactor = widthFactor;
		// eslint-disable-next-line @typescript-eslint/no-non-null-assertion
		this.context = canvas.getContext('2d')!;

		this.resize();
		this.centralize();
	}

	get speedX() {
		const deltaTime = performance.now() - this.lastMoveTime;

		if (deltaTime > 300) {
			return 0;
		}

		return this._speedX;
	}

	moveBy(x: number) {
		const oldX = this.box.x;
		const effectiveX = Math.max(0, Math.min(this.canvas.width - this.box.rect.w, oldX + x));
		this.box.position.x = effectiveX;

		if (this.lastMoveTime < 0) {
			this.lastMoveTime = performance.now();
		} else {
			const deltaTime = performance.now() - this.lastMoveTime;

			if (deltaTime > 0) {
				this._speedX = (effectiveX - oldX) / deltaTime;
				this.lastMoveTime = performance.now();
			}
		}
	}

	moveToPointerX(pointerX: number) {
		this.moveBy(pointerX - this.box.x - this.box.w / 2);
	}

	centralize() {
		this.box.position.x = (this.canvas.width - this.box.w) / 2;
		this.centralizeY();
	}

	centralizeY() {
		this.box.position.y = this.canvas.height - 30 * window.devicePixelRatio;
	}

	resize() {
		const { w, h } = this.getBoxSize();
		this.box.rect.w = w;
		this.box.rect.h = h;
		this.limitBoxPosition();
	}

	draw() {
		const context = this.context;
		const { x, y, w, h } = this.box;
		const color = window.getComputedStyle(this.context.canvas).color;

		context.fillStyle = color;
		context.fillRect(x, y, w, h);
	}

	private getBoxSize(): Pick<HitBox, 'w' | 'h'> {
		return {
			w: Math.max(100, Math.floor(window.devicePixelRatio * this.canvas.width * this.widthFactor)),
			h: 10 * window.devicePixelRatio,
		};
	}

	private limitBoxPosition() {
		this.box.position.x = Math.min(this.canvas.width - this.box.w, this.box.x);
	}
}
