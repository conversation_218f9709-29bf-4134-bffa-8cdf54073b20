<script lang="ts">
	import PageTransition from '$lib/components/PageTransition.svelte';
	import Snake from './Snake.svelte';
	import { MetaTags } from 'svelte-meta-tags';
</script>

<MetaTags
	title="Play Snake Online for Free"
	titleTemplate="%s | Lofi and Games"
	description="Play snake online for free. Beautiful snake game, delightful gaming experience, no download nor registration is required."
	canonical="https://www.lofiandgames.com/snake"
	openGraph={{
		url: 'https://www.lofiandgames.com/snake',
		images: [
			{
				url: 'https://www.lofiandgames.com/share-snake.png',
				width: 1200,
				height: 630,
				alt: 'Snake Game',
			},
		],
		siteName: 'Lofi and Games',
		type: 'game',
	}}
	twitter={{
		cardType: 'summary_large_image',
		title: 'Play Snake on Lofi and Games',
		image: 'https://www.lofiandgames.com/share-snake.png',
		site: 'https://www.lofiandgames.com/snake',
	}}
/>

<PageTransition>
	<Snake />
</PageTransition>
