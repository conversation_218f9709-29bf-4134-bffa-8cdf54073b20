<script lang="ts">
	import InfoModal from '$lib/components/InfoModal.svelte';
	import { snakeSoundResources as sounds } from './snakeSoundResources';

	interface Props {
		isOpen?: boolean;
	}

	let { isOpen = $bindable(false) }: Props = $props();
</script>

<InfoModal {sounds} bind:isOpen>
	<h1>Snake</h1>

	<p>
		The Snake game is a classic that has been around for decades. It is a simple yet addictive game
		where the player controls a snake that moves around the screen, trying to eat as much food as
		possible while avoiding running into the walls or its own tail.
	</p>

	<h2>How to Play Snake: The Game Rules</h2>

	<ul>
		<li>The game begins with a snake appearing on the screen.</li>

		<li>The snake can move in four directions: up, down, left, and right.</li>

		<li>
			The snake's goal is to eat as much food as possible, represented by small dots on the screen.
		</li>

		<li>Each time the snake eats a dot, it grows longer.</li>

		<li>
			The player must avoid running the snake into the walls or its own tail, as this will result in
			game over.
		</li>

		<li>The game is over when the snake crashes into the walls or its own tail.</li>
	</ul>

	<h2>Useful Snake Game Tips</h2>

	<ul></ul>
	<li>Always be aware of the snake's tail. Try to keep it behind you as much as possible.</li>

	<li>
		Plan your moves in advance, as the snake moves quickly and it's easy to run into the walls or
		your own tail.
	</li>

	<li>Try to predict where the food will appear next, and position the snake accordingly.</li>

	<li>
		Take advantage of the snake's ability to move in multiple directions. This can help you avoid
		obstacles and catch more food.
	</li>

	<li>The Snake game is a game of patience. Take your time and don't rush your moves.</li>

	<h2>Conclusion</h2>

	<p>
		The Snake game is a timeless classic that continues to be enjoyed by players of all ages. With
		its simple yet challenging gameplay, it's easy to see why it has stood the test of time. Whether
		you're a seasoned veteran or a newcomer to the game, these tips will help you improve your
		skills and achieve a higher score.
	</p>
</InfoModal>
