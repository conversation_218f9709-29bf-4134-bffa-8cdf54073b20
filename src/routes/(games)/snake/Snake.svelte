<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { SnakeGame, type SnakeSpeed } from './SnakeGame.svelte';
	import { fade, fly } from 'svelte/transition';
	import { wait } from '$lib/functions/wait';
	import SnakeTitle from './SnakeTitle.svelte';
	import TouchIcon from '$lib/components/Icons/TouchIcon.svelte';
	import { snakeSoundResources } from './snakeSoundResources';
	import GameLayout from '$lib/components/GameLayout/GameLayout.svelte';
	import { Stats } from '$lib/util/Stats.svelte';
	import GameIsland from '$lib/components/GameIsland/GameIsland.svelte';
	import capitalize from 'lodash/capitalize';
	import DirectionKeys from '$lib/components/DirectionKeys.svelte';
	import { GameContext } from '$lib/util/GameContext/GameContext.svelte';
	import ChevronUpIcon from '$lib/components/Icons/ChevronUpIcon.svelte';
	import ChevronLeftIcon from '$lib/components/Icons/ChevronLeftIcon.svelte';
	import ChevronRightIcon from '$lib/components/Icons/ChevronRightIcon.svelte';
	import ChevronDownIcon from '$lib/components/Icons/ChevronDownIcon.svelte';
	import type { Direction } from '$lib/util/DirectionListener';
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import SettingsIcon from '$lib/components/Icons/SettingsIcon.svelte';
	import Toggle from '$lib/components/Toggle/Toggle.svelte';
	import SnakeInfoModal from './SnakeInfoModal.svelte';
	import InfoSolidIcon from '$lib/components/Icons/InfoSolidIcon.svelte';
	import { Leaderboard } from '$lib/util/Leaderboard.svelte';
	import MoreGamesButton from '$lib/components/MoreGamesButton/MoreGamesButton.svelte';

	type GameScreen = 'intro' | 'instructions' | 'game' | 'game-won';

	let isInfoModalOpen = $state(false);
	let isSettingsOpen = $state(false);
	let screen = $state<GameScreen>('intro');
	let canvas = $state<HTMLCanvasElement>();
	let gameElement = $state<HTMLElement>();
	let shouldShowGame = $derived(screen === 'instructions' || screen === 'game');
	const gameOverDuration = 3000;

	const context = new GameContext({
		GameClass: SnakeGame,
		gameKey: 'snake',
		settings: {
			defaultSettings: {
				showDirectionButtons: false,
			},
		},
		sounds: {
			resources: snakeSoundResources,
			lifecycle: {
				win: snakeSoundResources.gameWin,
				lose: snakeSoundResources.gameOver,
				createGame: snakeSoundResources.menuSelect,
			},
		},
		formatted(context) {
			return {
				name: 'Snake',
				variant: capitalize(context.game?.speed ?? 'normal'),
				leaderboardVariant: capitalize(context.game?.speed ?? 'normal'),
			};
		},
		defaultGameProps(context) {
			return {
				targetElement: gameElement!,
				canvas: canvas!,
				speed: context.game?.speed ?? 'normal',
				gridRows: 24,
				timer: context.timer,
				onEnd: onGameEnd,
				onScoreChange,
			};
		},
		stats({ context, props }) {
			return {
				stats: new Stats({
					...props,
					gameVariant: context.game?.speed ?? 'normal',
					liveStats: {
						score: {
							name: 'Score',
							unit: 'plain',
							value() {
								return context.game?.score ?? 0;
							},
							metrics: {
								total: {
									key: 'totalScore',
									name: 'Total Score',
								},
								average: {
									key: 'averageScore',
									name: 'Average Score',
								},
								max: {
									key: 'bestScore',
									name: 'Best Score',
									useAsBest: true,
								},
								min: {
									key: 'worstScore',
									name: 'Worst Score',
								},
							},
						},
					},
					initialPinnedStats: ['time', 'score'] as any,
				}),
				canUpdateWithGameLost(game) {
					return game.timer.started && !game.timer.stopped && screen === 'game';
				},
				visibleStats: ['bestScore', 'averageScore', 'totalGames'],
			};
		},
		leaderboard(context) {
			return {
				leaderboard: new Leaderboard({
					game: context.gameKey,
					gameVariant: context.game?.speed ?? 'normal',
					firstAvailableDate: new Date('2025/05/08'),
					order: 'higher-first',
				}),
				sendScoreOn: ['won', 'lost'],
				getScore(game) {
					return {
						score: game.score,
					};
				},
			};
		},
		onWillCreateGame({ previousGame }) {
			previousGame?.dispose();
		},
		onDispose(context) {
			context.game?.dispose();
		},
	});

	let game = $derived(context.game);

	async function changeSpeed(speed: SnakeSpeed) {
		if (screen === 'intro') {
			context.createGame({ speed });

			screen = 'instructions';

			await wait(600);

			game?.draw();
		}
	}

	async function onGameEnd() {
		if (game?.isWon) {
			context.handleGameOver('won');
			screen = 'game-won';
		} else if (game?.isLost) {
			context.handleGameOver('lost');
		}

		await wait(gameOverDuration);

		screen = 'intro';
		context.resetGameState();

		if (context.game) {
			context.game._score = 0;
		}
	}

	function onScoreChange() {
		context.sounds?.scoreChange.play();
	}

	async function startGameIfNeeded() {
		if (screen === 'instructions') {
			screen = 'game';

			context.sounds?.scoreChange.play();

			await wait(300);

			game?.start();
		}
	}

	function handleKeyUp(event: KeyboardEvent) {
		if (['INPUT', 'TEXTAREA', 'SELECT'].includes((event.target as HTMLElement).tagName)) {
			return;
		}

		const key = event.key.toLocaleLowerCase();

		if (screen === 'intro') {
			if (key === 's') {
				changeSpeed('slow');
			} else if (key === 'n') {
				changeSpeed('normal');
			} else if (key === 'f') {
				changeSpeed('fast');
			} else if (key === 'u') {
				changeSpeed('ultra');
			}
		} else if (screen === 'instructions') {
			if (['w', 'a', 's', 'd', 'arrowup', 'arrowdown', 'arrowleft', 'arrowright'].includes(key)) {
				startGameIfNeeded();
			}
		}
	}

	function move(direction: Direction) {
		startGameIfNeeded();
		game?.handleDirectionChange(direction);
	}

	function handlePointerup(event: PointerEvent) {
		if (event.pointerType === 'touch') {
			startGameIfNeeded();
		}
	}

	$effect(function syncSettings() {
		if (game) {
			game.directionListener.disabledTouch = context.settingsManager.settings.showDirectionButtons;
		}
	});

	onMount(() => {
		context.load();
	});

	onDestroy(() => {
		context.dispose();
	});
</script>

<svelte:window onkeyup={handleKeyUp} />

<SnakeInfoModal bind:isOpen={isInfoModalOpen} />

<GameLayout>
	{#snippet Island()}
		<GameIsland {context} gameOverStrategy="best-stats-update" gameOverIslandDelay={0} />
	{/snippet}

	<section
		class="max-h-screen-no-navbar size-full flex items-center touch-pinch-zoom select-none flex-col justify-center gap-2"
		onpointerup={handlePointerup}
		bind:this={gameElement}
	>
		<div
			class="relative mx-auto flex w-full max-w-xs items-center justify-center rounded-lg border-2 border-current aspect-square md:max-w-sm lg:max-w-md"
		>
			<div class="absolute left-0 -top-12 flex gap-2">
				<Dropdown bind:open={isSettingsOpen}>
					<DropdownButton class="btn-sm" aria-label="Show settings">
						<SettingsIcon class="size-5" />
					</DropdownButton>

					<DropdownContent class="w-60">
						<DropdownItem>
							<Toggle
								name="toggle direction buttons"
								bind:checked={context.settingsManager.settings.showDirectionButtons}
							>
								Direction Buttons
							</Toggle>
						</DropdownItem>
					</DropdownContent>
				</Dropdown>

				<button class="btn btn-sm" aria-label="Show info" onclick={() => (isInfoModalOpen = true)}>
					<InfoSolidIcon class="size-5" />
				</button>

				<MoreGamesButton class="btn-sm" />
			</div>

			<canvas
				bind:this={canvas}
				class:opacity-0={!shouldShowGame}
				class="size-full transition-opacity"
			></canvas>

			{#if screen === 'intro'}
				<span
					transition:fade={{ duration: 300 }}
					class="absolute top-1/3 left-1/2 mb-8 block -translate-y-1/2 -translate-x-1/2 select-none text-center text-5xl md:text-6xl lg:text-7xl"
				>
					<SnakeTitle />
				</span>

				<div
					transition:fade={{ duration: 300 }}
					class="absolute left-1/2 bottom-0 grid w-full -translate-x-1/2 grid-cols-2 gap-2 p-4 lg:grid-cols-4"
				>
					<button class="btn-outline btn" onclick={() => changeSpeed('slow')}>
						<span><u class="no-underline lg:underline">S</u>low</span>
					</button>
					<button class="btn-outline btn" onclick={() => changeSpeed('normal')}>
						<span><u class="no-underline lg:underline">N</u>ormal</span>
					</button>
					<button class="btn-outline btn" onclick={() => changeSpeed('fast')}>
						<span><u class="no-underline lg:underline">F</u>ast</span>
					</button>
					<button class="btn-outline btn-error btn" onclick={() => changeSpeed('ultra')}>
						<span><u class="no-underline lg:underline">U</u>ltra</span>
					</button>
				</div>
			{/if}

			{#if screen === 'instructions'}
				<div
					transition:fade={{ delay: 300, duration: 300 }}
					class="absolute bottom-8 left-1/2 mt-8 -translate-x-1/2 items-center justify-center"
				>
					<!-- Desktop -->
					<div class="hidden lg:flex">
						<DirectionKeys />
					</div>

					<!-- Mobile -->
					<div class="justify-content flex flex-col items-center gap-2 lg:hidden">
						<TouchIcon width="32" height="32" />
						Touch to start
					</div>
				</div>
			{/if}

			{#if screen === 'game-won'}
				<span
					transition:fade={{ duration: 300 }}
					class="absolute left-1/2 top-1/2 mb-8 block w-full -translate-y-1/2 -translate-x-1/2 select-none text-center text-5xl md:text-6xl lg:text-7xl"
				>
					YOU WIN
				</span>
			{/if}
		</div>

		{#if context.settingsManager.settings.showDirectionButtons}
			<div class="p-2 flex flex-col items-center justify-center" transition:fly={{ y: 16 }}>
				<button
					class="btn btn-outline btn-circle btn-lg"
					onpointerdown={() => {
						move('up');
					}}
				>
					<ChevronUpIcon />
				</button>

				<div class="flex items-center justify-center">
					<button
						class="btn btn-outline btn-circle btn-lg -mt-2 -mr-2"
						onpointerdown={() => {
							move('left');
						}}
					>
						<ChevronLeftIcon />
					</button>

					<div class="btn btn-circle btn-lg invisible"></div>

					<button
						class="btn btn-outline btn-circle btn-lg -mt-2 -ml-2"
						onpointerdown={() => {
							move('right');
						}}
					>
						<ChevronRightIcon />
					</button>
				</div>

				<button
					class="btn btn-outline btn-circle btn-lg -mt-2"
					onpointerdown={() => {
						move('down');
					}}
				>
					<ChevronDownIcon />
				</button>
			</div>
		{/if}
	</section>
</GameLayout>
