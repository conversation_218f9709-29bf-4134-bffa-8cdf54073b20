<svg class="size-full p-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 498 498">
	<g clip-path="url(#color-memory-cover__a)">
		<path
			class="fill-game-color-memory-1 opacity-60"
			fill-rule="evenodd"
			d="M257 440.522c0 4.542 3.785 8.188 8.312 7.822 97.448-7.865 175.167-85.584 183.032-183.032.366-4.527-3.28-8.312-7.822-8.312H333.821c-4.104 0-7.498 3.12-8.291 7.147-6.086 30.928-30.455 55.297-61.383 61.383-4.027.793-7.147 4.187-7.147 8.291v106.701Z"
			clip-rule="evenodd"
		/>
		<path
			class="fill-game-color-memory-2 opacity-60"
			d="M232.689 49.656C135.241 57.522 57.522 135.24 49.656 232.688c-.365 4.527 3.28 8.312 7.822 8.312H164.18c4.105 0 7.499-3.119 8.291-7.147 6.087-30.928 30.455-55.297 61.384-61.383 4.027-.793 7.147-4.187 7.147-8.291V57.478c0-4.542-3.786-8.188-8.312-7.822Z"
		/>
		<g filter="url(#color-memory-cover__b)">
			<path
				class="fill-game-color-memory-3"
				d="M448.345 232.688C440.48 135.24 362.761 57.522 265.313 49.656c-4.527-.366-8.312 3.28-8.312 7.822v106.701c0 4.104 3.119 7.498 7.147 8.291 30.928 6.086 55.297 30.455 61.383 61.383.793 4.027 4.187 7.147 8.291 7.147h106.701c4.542 0 8.188-3.785 7.822-8.312Z"
			/>
		</g>
		<path
			class="fill-game-color-memory-4"
			d="M49.656 265.312c7.866 97.448 85.585 175.167 183.033 183.032 4.526.366 8.312-3.28 8.312-7.822V333.821c0-4.104-3.12-7.498-7.147-8.291-30.929-6.086-55.297-30.455-61.384-61.383-.792-4.027-4.186-7.147-8.291-7.147h-106.7c-4.542 0-8.188 3.785-7.823 8.312Z"
		/>
	</g>
	<defs>
		<clipPath id="color-memory-cover__a">
			<path fill="#fff" d="M0 0h498v498H0z" />
		</clipPath>
		<filter
			id="color-memory-cover__b"
			width="223.37"
			height="223.37"
			x="241.001"
			y="33.63"
			color-interpolation-filters="sRGB"
			filterUnits="userSpaceOnUse"
		>
			<feFlood flood-opacity="0" result="BackgroundImageFix" />
			<feColorMatrix
				in="SourceAlpha"
				result="hardAlpha"
				values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
			/>
			<feOffset />
			<feGaussianBlur stdDeviation="8" />
			<feComposite in2="hardAlpha" operator="out" />
			<feColorMatrix values="0 0 0 0 0.968627 0 0 0 0 0.4 0 0 0 0 0.4 0 0 0 1 0" />
			<feBlend in2="BackgroundImageFix" result="effect1_dropShadow_282_1443" />
			<feBlend in="SourceGraphic" in2="effect1_dropShadow_282_1443" result="shape" />
		</filter>
	</defs>
</svg>
