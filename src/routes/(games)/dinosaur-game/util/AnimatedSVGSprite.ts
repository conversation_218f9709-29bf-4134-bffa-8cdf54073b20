export class AnimatedSVGSprite {
	readonly width: number;
	readonly height: number;
	private _groups: SVGSprite[];
	private _frameDurationInMs: number;
	private _currentSpriteIndex = 0;
	private _timeOnCurrentFrame = 0;

	constructor(svgGroups: SVGElement[], frameDurationInMs = 80) {
		this._frameDurationInMs = frameDurationInMs;
		this._groups = svgGroups.map((group) => new SVGSprite(group));
		this.width = this._groups[0].width;
		this.height = this._groups[0].height;
	}

	private get currentSprite() {
		return this._groups[this._currentSpriteIndex];
	}

	private goToNextFrame() {
		let newIndex = (this._currentSpriteIndex += 1);
		if (newIndex >= this._groups.length) {
			newIndex = 0;
		}

		this._currentSpriteIndex = newIndex;
	}

	draw(context: CanvasRenderingContext2D) {
		this.currentSprite.draw(context);
	}

	tick(time: number) {
		this._timeOnCurrentFrame += time;

		if (this._timeOnCurrentFrame > this._frameDurationInMs) {
			this.goToNextFrame();
			this._timeOnCurrentFrame = 0;
		}
	}
}

export class SVGSprite {
	readonly width: number;
	readonly height: number;
	private _pathSprites: SVGPathSprite[];

	constructor(svgGroup: SVGElement) {
		this._pathSprites = Array.from(svgGroup.querySelectorAll('path')).map(
			(path) => new SVGPathSprite(path),
		);

		this.width = this._pathSprites[0].width;
		this.height = this._pathSprites[0].height;
	}

	draw(context: CanvasRenderingContext2D) {
		this._pathSprites.forEach((sprite) => {
			sprite.draw(context);
		});
	}
}

export class SVGPathSprite {
	readonly width: number;
	readonly height: number;
	private _pathElement: SVGElement;
	private _path2D: Path2D;
	private _fillRule: CanvasFillRule;

	constructor(path: SVGPathElement) {
		this._pathElement = path;
		this._path2D = new Path2D(path.getAttribute('d')!);
		this._fillRule = (path.getAttribute('fill-rule') as any) ?? 'evenodd';

		// Get svg size and store it
		const svgElement = getAncestorSvgElement(path);

		this.width = +(svgElement?.getAttribute('width') ?? 0);
		this.height = +(svgElement?.getAttribute('height') ?? 0);
	}

	draw(context: CanvasRenderingContext2D) {
		context.save();
		context.scale(window.devicePixelRatio, window.devicePixelRatio);

		const fill = window.getComputedStyle(this._pathElement).fill;

		context.fillStyle = fill;
		context.fill(this._path2D, this._fillRule);

		context.restore();
	}
}

const getAncestorSvgElement = (element: SVGElement): SVGElement | null => {
	let svgElement: SVGElement | HTMLElement | null = element;

	while (svgElement?.tagName !== 'svg') {
		svgElement = svgElement.parentElement;

		if (svgElement === null) {
			break;
		}
	}

	if (svgElement?.tagName === 'svg') {
		return svgElement as SVGElement;
	}

	return null;
};
