import { getRandomItemAt } from '$lib/functions/getRandomItemAt';
import { CanvasGame, type CanvasGameOptions } from './CanvasGame.svelte';
import { Bird } from './game-objects/Bird';
import { CactusBig } from './game-objects/CactusBig';
import { CactusSmall } from './game-objects/CactusSmall';
import { Dino } from './game-objects/Dino';
import type { WorldObject } from './game-objects/WorldObject';
import { InfiniteFloor } from './game-objects/InfiniteFloor';
import { InfiniteClouds } from './game-objects/InifiniteClouds';
import { InfiniteMoon } from './game-objects/InfiniteMoon';
import { InfiniteStars } from './game-objects/InfiniteStars';
import { Score } from './game-objects/Score.svelte';
import { GameOver } from './game-objects/GameOver';
import { IntroScene } from './scenes/IntroScene';
import type { GameSound } from '$lib/util/GameSound.svelte';
import { Timer } from '$lib/util/Timer.svelte';

export type DinosaurGameSounds = {
	jump: GameSound;
	start: GameSound;
	gameOver: GameSound;
};

export type DinosaurGameOptions = CanvasGameOptions & {
	dinoSpriteSheet: SVGElement;
	birdSpriteSheet: SVGElement;
	floorSpriteSheet: SVGElement;
	cactusSmallSpriteSheet: SVGElement;
	cactusBigSpriteSheet: SVGElement;
	cloudSpriteSheet: SVGElement;
	moonSpriteSheet: SVGElement;
	starSpriteSheet: SVGElement;
	retrySpriteSheet: SVGElement;
	eventsTarget: HTMLElement;
	sounds: DinosaurGameSounds;
	timer: Timer;
	onGameStart: () => void;
	getBestScore: () => number | null;
};

const spawnExtraObjectChance = 0.95;
const minWorldSpeed = 5;
const maxWorldSpeed = 13;
const birdsExtraSpeed = 0.2;
const cloudsSpeed = 1;
const moonSpeed = cloudsSpeed / 3;
const starsSpeed = cloudsSpeed / 2;
const acceleration = 0.0008;
const resetGameDelay = 500;

export class DinosaurGame extends CanvasGame {
	timer: Timer;
	private _dino?: Dino;
	private _aliveObjects?: Array<WorldObject> = [];
	private _deadObjects?: Array<WorldObject> = [];
	private _moon?: InfiniteMoon;
	private _clouds?: InfiniteClouds;
	private _floor?: InfiniteFloor;
	private _stars?: InfiniteStars;
	private _score?: Score;
	private _gameOver?: GameOver;
	private _currentSpeed = minWorldSpeed;
	private _introScene: IntroScene;
	private _currentScene: 'intro' | 'game' = 'intro';
	private onGameStart: () => void;
	private _sounds: DinosaurGameSounds;

	constructor({
		dinoSpriteSheet,
		birdSpriteSheet,
		floorSpriteSheet,
		cactusSmallSpriteSheet,
		cactusBigSpriteSheet,
		cloudSpriteSheet,
		moonSpriteSheet,
		starSpriteSheet,
		retrySpriteSheet,
		eventsTarget,
		sounds,
		timer,
		onGameStart,
		getBestScore,
		...options
	}: DinosaurGameOptions) {
		super(options);
		this.timer = timer;
		this._sounds = sounds;
		this.onGameStart = onGameStart;
		this._introScene = new IntroScene({
			context: this.context,
			dinoSpriteSheet,
			floorSpriteSheet,
			onFinish: this._handleIntroSceneFinish,
		});
		this._dino = new Dino(this.context, dinoSpriteSheet, eventsTarget, this.timer);
		this._floor = new InfiniteFloor(this.context, floorSpriteSheet);
		this._aliveObjects = [];
		this._deadObjects = [
			new CactusSmall(this.context, cactusSmallSpriteSheet, 1),
			new CactusSmall(this.context, cactusSmallSpriteSheet, 2),
			new CactusSmall(this.context, cactusSmallSpriteSheet, 3),
			new CactusBig(this.context, cactusBigSpriteSheet, 1),
			new CactusBig(this.context, cactusBigSpriteSheet, 2),
			new CactusBig(this.context, cactusBigSpriteSheet, 3),
			new Bird(this.context, birdSpriteSheet, 'top'),
			window.matchMedia('(min-width: 1024px)').matches
				? new Bird(this.context, birdSpriteSheet, 'center')
				: new Bird(this.context, birdSpriteSheet, 'bottom'),
			new Bird(this.context, birdSpriteSheet, 'bottom'),
		];
		this._clouds = new InfiniteClouds(this.context, cloudSpriteSheet);
		this._moon = new InfiniteMoon(this.context, moonSpriteSheet);
		this._stars = new InfiniteStars(this.context, starSpriteSheet);
		this._score = new Score(this.context, getBestScore);
		this._gameOver = new GameOver(this.context, retrySpriteSheet);

		this._dino.state = 'walk';
		this._dino.onJump = () => {
			if (this._introScene.isFinished) {
				this._sounds.jump.play();
			}
		};
		this._dino.onValidUserInput = (repeat) => {
			if (this._currentScene === 'intro' && !this._introScene.started) {
				this.timer.start();
				this._introScene.start();
				this.onGameStart();
				this._sounds.start.play();
				return;
			}

			if (this._canReset && !repeat) {
				this.reset(true);
				this.onGameStart();
			}
		};

		this._addEventListeners();
		this.start();
	}

	get score() {
		return Math.floor(this._score?.score ?? 0);
	}

	private get _canReset() {
		return this.isOver && performance.now() - this.gameOverAt > resetGameDelay;
	}

	private get _speed() {
		return this._currentSpeed;
	}

	private set _speed(newSpeed: number) {
		if (newSpeed > maxWorldSpeed) {
			newSpeed = maxWorldSpeed;
		}

		if (newSpeed < minWorldSpeed) {
			newSpeed = minWorldSpeed;
		}

		this._currentSpeed = newSpeed;
	}

	private _handleIntroSceneFinish = () => {
		this._currentScene = 'game';
		this.reset();
		this.tick(0);
	};

	draw() {
		super.draw();

		if (this._currentScene === 'intro') {
			this._introScene.draw();
			return;
		}

		this._stars?.draw();
		this._moon?.draw();
		this._clouds?.draw();
		this._floor?.draw();
		this._aliveObjects?.forEach((object) => object.draw());
		this._dino?.draw();
		this._score?.draw();

		if (this.isOver) {
			this._gameOver?.draw();
		}
	}

	tick(time: number) {
		if (this.isOver || (this.timer?.paused && this.timer.started)) {
			return;
		}

		if (this._currentScene === 'intro') {
			this._introScene.tick(time);
			return;
		}

		this._speed += acceleration;
		this._stars?.tick();
		this._moon?.tick();
		this._clouds?.tick();
		this._floor?.tick();
		this._dino?.tick(time);
		this._score?.tick(time);
		this._moveObjects();
		this._updateAliveObjects();
		this._aliveObjects?.forEach((object) => object.tick(time));

		if (this._aliveObjects && this._dino) {
			if (
				this._aliveObjects.some((object) => {
					return this._dino?.collidesWith(object.hitBoxes);
				})
			) {
				this.isLost = true;
				this.timer.stop();
				this._dino.state = 'dead';
				this._sounds.gameOver.play();
			}
		}
	}

	private _moveAliveObjectsAndFloorBy(dx: number) {
		this._aliveObjects?.forEach((object) => {
			object.position.x -= dx;

			if (object instanceof Bird) {
				object.position.x -= birdsExtraSpeed;
			}
		});

		this._floor?.incrementPositionXBy(-dx);
	}

	private _moveObjects() {
		this._moveAliveObjectsAndFloorBy(this._speed * window.devicePixelRatio);
		this._stars?.incrementPositionXBy(-starsSpeed * window.devicePixelRatio);
		this._clouds?.incrementPositionXBy(-cloudsSpeed * window.devicePixelRatio);
		this._moon?.incrementPositionXBy(-moonSpeed * window.devicePixelRatio);
	}

	private _updateAliveObjects() {
		const deadObject = this._aliveObjects?.find((object) => object.state === 'dead');

		if (deadObject) {
			this._aliveObjects = this._aliveObjects?.filter((object) => object !== deadObject);
			this._deadObjects?.push(deadObject);
		}

		if (this._aliveObjects && this._aliveObjects?.length < 1) {
			this._spawnObjectFromDeadPool();

			const speedPercentage = (this._speed - minWorldSpeed) / (maxWorldSpeed - minWorldSpeed);
			const minSpawnWidth = 520 * window.devicePixelRatio * (1 + speedPercentage / 1.5);

			if (Math.random() <= spawnExtraObjectChance) {
				this._spawnObjectFromDeadPool(minSpawnWidth);
			}
			if (Math.random() <= spawnExtraObjectChance) {
				this._spawnObjectFromDeadPool(minSpawnWidth * 1.6);
			}
			if (Math.random() <= spawnExtraObjectChance) {
				this._spawnObjectFromDeadPool(minSpawnWidth * 2.2);
			}
			if (Math.random() <= spawnExtraObjectChance) {
				this._spawnObjectFromDeadPool(minSpawnWidth * 2.9);
			}
		}
	}

	private _spawnObjectFromDeadPool(dx = 0) {
		if (!this._aliveObjects || !this._deadObjects || !this._score) {
			return;
		}

		const objectsToUse =
			this._score.score > 300
				? this._deadObjects
				: this._deadObjects.filter((object) => {
						if (object instanceof Bird) {
							return false;
						}

						if (object instanceof CactusBig && object.variant === 3) {
							return false;
						}

						return true;
					});
		const randomObject = getRandomItemAt(objectsToUse);

		if (randomObject) {
			randomObject.reset();
			// Randomize a little bit the position
			randomObject.position.x += dx + (Math.random() * this.context.canvas.width) / 6;
			this._aliveObjects.push(randomObject);
			this._deadObjects = this._deadObjects.filter((object) => object !== randomObject);
		}
	}

	reset(playSound?: boolean) {
		super.reset();
		this.timer.reset();
		this.timer.start();
		this._sounds.gameOver.stop();
		this._dino?.reset();
		this._score?.reset();

		if (this._deadObjects && this._aliveObjects) {
			this._deadObjects.push(...this._aliveObjects);
			this._aliveObjects = [];
			this._speed = minWorldSpeed;
		}

		if (playSound) {
			this._sounds.start.play();
		}
	}

	private _handleKeyUp = (event: KeyboardEvent) => {
		if (['INPUT', 'TEXTAREA', 'SELECT'].includes((event.target as HTMLElement).tagName)) {
			return;
		}

		if (this.timer.paused && this.timer.started && !this.timer.stopped) {
			return;
		}

		const key = event.key.toLocaleLowerCase();

		if (key === 'enter' && this._canReset) {
			this.reset(true);
		}
	};

	private _addEventListeners = () => {
		window.addEventListener('keyup', this._handleKeyUp);
	};

	dispose(): void {
		super.dispose();
		window.removeEventListener('keyup', this._handleKeyUp);
		this._dino?.dispose();
	}
}
