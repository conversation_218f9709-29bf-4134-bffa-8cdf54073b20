<script lang="ts">
	import { fade } from 'svelte/transition';
	import { onMount, onDestroy } from 'svelte';
	import { DinosaurGame } from './DinosaurGame.svelte';
	import DinoSpriteSheet from './spritesheets/DinoSpriteSheet.svelte';
	import BirdSpriteSheet from './spritesheets/BirdSpriteSheet.svelte';
	import FloorSpriteSheet from './spritesheets/FloorSpriteSheet.svelte';
	import CactusSmallSpriteSheet from './spritesheets/CactusSmallSpriteSheet.svelte';
	import CactusBigSpriteSheet from './spritesheets/CactusBigSpriteSheet.svelte';
	import CloudSpriteSheet from './spritesheets/CloudSpriteSheet.svelte';
	import MoonSpriteSheet from './spritesheets/MoonSpriteSheet.svelte';
	import StarSpriteSheet from './spritesheets/StarSpriteSheet.svelte';
	import RetrySpriteSheet from './spritesheets/RetrySpriteSheet.svelte';
	import TouchIcon from '$lib/components/Icons/TouchIcon.svelte';
	import { dinosaurSoundResources } from './dinosaurSoundResources';
	import GameLayout from '$lib/components/GameLayout/GameLayout.svelte';
	import { Stats } from '$lib/util/Stats.svelte';
	import GameIsland from '$lib/components/GameIsland/GameIsland.svelte';
	import DirectionKeys from '$lib/components/DirectionKeys.svelte';
	import { GameContext } from '$lib/util/GameContext/GameContext.svelte';
	import { wait } from '$lib/functions/wait';
	import DinosaurInfoModal from './DinosaurInfoModal.svelte';
	import InfoSolidIcon from '$lib/components/Icons/InfoSolidIcon.svelte';
	import { Leaderboard } from '$lib/util/Leaderboard.svelte';
	import { islandSettings } from '$lib/stores/islandSettings.svelte';
	import MoreGamesButton from '$lib/components/MoreGamesButton/MoreGamesButton.svelte';

	let gameOverDuration = $derived.by(() => {
		if (
			islandSettings.settings.leaderboards &&
			islandSettings.settings.showLeaderboardsOnGameOver
		) {
			return 4000;
		}

		return 2500;
	});

	let canvas = $state<HTMLCanvasElement>();
	let gameElement = $state<HTMLElement>();
	let dinoSpriteSheet = $state<SVGElement>();
	let birdSpriteSheet = $state<SVGElement>();
	let floorSpriteSheet = $state<SVGElement>();
	let cactusSmallSpriteSheet = $state<SVGElement>();
	let cactusBigSpriteSheet = $state<SVGElement>();
	let cloudSpriteSheet = $state<SVGElement>();
	let moonSpriteSheet = $state<SVGElement>();
	let starSpriteSheet = $state<SVGElement>();
	let retrySpriteSheet = $state<SVGElement>();
	let shouldShowInstructions = $state(true);
	let isInfoModalOpen = $state(false);

	const context = new GameContext({
		GameClass: DinosaurGame,
		gameKey: 'dinosaur',
		settings: {
			defaultSettings: {},
		},
		sounds: {
			resources: dinosaurSoundResources,
		},
		formatted() {
			return {
				name: 'Dinosaur',
			};
		},
		defaultGameProps(context) {
			return {
				canvas: canvas!,
				sounds: context.sounds,
				timer: context.timer,
				onGameStart() {
					shouldShowInstructions = false;
					context.resetGameState({ resetTimer: false });
				},
				getBestScore() {
					return context.stats?.customStats.bestScore?.value ?? null;
				},
				async onGameOver() {
					context.handleGameOver('lost');

					await wait(gameOverDuration);

					if (context.game?.isOver) {
						context.resetGameState({ resetTimer: false });
					}
				},
				onRenderUi() {
					// Ignore
				},
				dinoSpriteSheet: dinoSpriteSheet!,
				birdSpriteSheet: birdSpriteSheet!,
				floorSpriteSheet: floorSpriteSheet!,
				cactusSmallSpriteSheet: cactusSmallSpriteSheet!,
				cactusBigSpriteSheet: cactusBigSpriteSheet!,
				cloudSpriteSheet: cloudSpriteSheet!,
				moonSpriteSheet: moonSpriteSheet!,
				starSpriteSheet: starSpriteSheet!,
				retrySpriteSheet: retrySpriteSheet!,
				eventsTarget: gameElement!,
			};
		},
		stats({ props, context }) {
			return {
				stats: new Stats({
					...props,
					gameVariant: 'default',
					liveStats: {
						score: {
							name: 'Score',
							unit: 'plain',
							value() {
								return context.game?.score ?? 0;
							},
							metrics: {
								total: {
									key: 'totalScore',
									name: 'Total Score',
								},
								average: {
									key: 'averageScore',
									name: 'Average Score',
								},
								max: {
									key: 'bestScore',
									name: 'Best Score',
									useAsBest: true,
								},
								min: {
									key: 'worstScore',
									name: 'Worst Score',
								},
							},
						},
					},
					initialPinnedStats: ['time', 'bestScore'],
				}),
				canUpdateWithGameLost(game) {
					return game.timer.started && !game.isOver;
				},
				visibleStats: ['bestScore', 'averageScore', 'totalGames'],
			};
		},
		leaderboard(context) {
			return {
				leaderboard: new Leaderboard({
					game: context.gameKey,
					firstAvailableDate: new Date('2025/05/08'),
					order: 'higher-first',
				}),
				sendScoreOn: ['lost'],
				getScore(game) {
					return {
						score: game.score,
					};
				},
			};
		},
		onWillCreateGame({ previousGame }) {
			previousGame?.dispose();
			shouldShowInstructions = true;
		},
		onDispose(context) {
			context.game?.dispose();
		},
	});

	onMount(() => {
		context.load();
	});

	onDestroy(() => {
		context.dispose();
	});
</script>

<DinosaurInfoModal bind:isOpen={isInfoModalOpen} />

<DinoSpriteSheet bind:svg={dinoSpriteSheet!} />
<BirdSpriteSheet bind:svg={birdSpriteSheet!} />
<FloorSpriteSheet bind:svg={floorSpriteSheet!} />
<CactusSmallSpriteSheet bind:svg={cactusSmallSpriteSheet!} />
<CactusBigSpriteSheet bind:svg={cactusBigSpriteSheet!} />
<CloudSpriteSheet bind:svg={cloudSpriteSheet!} />
<MoonSpriteSheet bind:svg={moonSpriteSheet!} />
<StarSpriteSheet bind:svg={starSpriteSheet!} />
<RetrySpriteSheet bind:svg={retrySpriteSheet!} />

<GameLayout noPadding mobileOrientation="all">
	{#snippet Island()}
		<GameIsland {context} gameOverIslandDelay={0} gameOverStrategy="best-stats-update" />
	{/snippet}

	<div class="flex flex-row gap-2 absolute bottom-4 right-4">
		<MoreGamesButton class="btn-sm" />

		<button class="btn btn-sm" onclick={() => (isInfoModalOpen = true)}>
			<InfoSolidIcon class="size-5" />
		</button>
	</div>

	<section
		class="max-h-screen-no-navbar size-full flex-center touch-pinch-zoom select-none px-4 md:px-0"
		bind:this={gameElement}
	>
		<!-- rounded-lg outline outline-2 outline-current -->
		<div class="relative mx-auto flex h-[150px] w-full max-w-[600px] items-center justify-center">
			<canvas bind:this={canvas} class="size-full"></canvas>

			{#if shouldShowInstructions}
				<div
					transition:fade
					class="card pointer-events-none absolute left-0 bottom-0 right-0 z-20 p-4 lg:right-10"
				>
					<!-- Desktop -->
					<div class="hidden items-end justify-center gap-2 text-center lg:flex">
						<div class="flex-center flex-col gap-2">
							<DirectionKeys variant="vertical" />
						</div>

						<div class="divider divider-horizontal">OR</div>

						<div class="flex-center flex-col gap-2">
							<kbd class="kbd kbd-lg px-8">space</kbd>
						</div>
					</div>

					<!-- Mobile -->
					<div
						class="justify-content flex w-full flex-col items-center gap-2 text-center lg:hidden"
					>
						<TouchIcon width="56" height="56" />
						Touch to start
					</div>
				</div>
			{/if}
		</div>
	</section>
</GameLayout>
