import { fixCanvasDpi } from '$lib/functions/fixCanvasDpi';

export type CanvasGameOptions = {
	canvas: HTMLCanvasElement;
	onRenderUi: () => void;
	onGameOver: () => void;
};

export class CanvasGame {
	protected canvas: HTMLCanvasElement;
	protected context: CanvasRenderingContext2D;
	protected disposed = false;
	protected foregroundColor = '#000';
	protected backgroundColor = '#fff';
	private _lastTickTimeStamp = -1;
	private _animationFrame = -1;
	private _running = $state(false);
	private _paused = $state(false);
	private _started = $state(false);
	private _isLost = $state(false);
	private _isWon = $state(false);
	private _lostAt = -1;
	private _wonAt = -1;
	protected onGameOver: () => void;
	protected onRenderUi: () => void;
	private fps = 120;
	private timePerFrame = 1000 / this.fps;

	constructor({ canvas, onRenderUi, onGameOver }: CanvasGameOptions) {
		this.canvas = canvas;
		this.canvas.classList.add('bg-base-100');
		// eslint-disable-next-line @typescript-eslint/no-non-null-assertion
		this.context = canvas.getContext('2d')!;
		this.onRenderUi = onRenderUi;
		this.onGameOver = onGameOver;
		fixCanvasDpi(this.canvas);
		this.initGame();
		this._addListeners();
		this._onResize();
		this.tick(0);
		window.requestAnimationFrame(this.loop);
	}

	get isWon() {
		return this._isWon;
	}

	set isWon(isWon: boolean) {
		this._isWon = isWon;

		if (isWon) {
			this._wonAt = performance.now();
			this.onRenderUi();
			this.onGameOver();
		}
	}

	get wonAt() {
		return this._wonAt;
	}

	get isLost() {
		return this._isLost;
	}

	set isLost(isLost: boolean) {
		this._isLost = isLost;

		if (isLost) {
			this._wonAt = performance.now();
			this.onRenderUi();
			this.onGameOver();
		}
	}

	get lostAt() {
		return this._lostAt;
	}

	get isOver() {
		return this.isLost || this.isWon;
	}

	get gameOverAt() {
		return Math.max(this._lostAt, this._wonAt);
	}

	get running() {
		return this._running;
	}

	set running(running: boolean) {
		if (this.disposed && running) {
			return;
		}

		this._running = running;
		this._lastTickTimeStamp = -1;
	}

	get started() {
		return this._started;
	}

	set started(started: boolean) {
		this._started = started;
		this.onRenderUi();
	}

	get paused() {
		return this._paused;
	}

	set paused(paused: boolean) {
		if (this.disposed || this.isOver) {
			return;
		}

		this._paused = paused;
		if (this.started) {
			this.running = !paused;
		}
		this.onRenderUi();
	}

	resume() {
		this.paused = false;
	}

	start() {
		if (this.running || this.isOver) {
			return;
		}

		this.running = true;
		this.started = true;
	}

	stop() {
		this.running = false;
	}

	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	protected tick(time: number) {
		//
	}

	protected clearDraw() {
		const canvasSize = this.context.canvas.width;
		this.context.clearRect(0, 0, canvasSize, canvasSize);
	}

	protected draw() {
		this.foregroundColor = window.getComputedStyle(this.canvas).color;
		this.backgroundColor = window.getComputedStyle(this.canvas).backgroundColor;
		fixCanvasDpi(this.canvas);
		this.clearDraw();
	}

	protected loop = () => {
		if (document.hidden || this.disposed) {
			return;
		}

		const now = performance.now();

		if (this.running) {
			if (this._lastTickTimeStamp < 0) {
				this._lastTickTimeStamp = now;
			}

			const timeSinceLastUpdate = now - this._lastTickTimeStamp;

			// Limit fps
			if (timeSinceLastUpdate > this.timePerFrame) {
				this.tick(timeSinceLastUpdate);
				this._lastTickTimeStamp = now;
			}
		}

		this.draw();

		this._animationFrame = window.requestAnimationFrame(this.loop);
	};

	reset() {
		this._isLost = false;
		this._isWon = false;
		this.start();
	}

	protected initGame() {
		//
	}

	private _onResize = () => {
		this.canvas.setAttribute('width', `${this.canvas.width}`);
		this.canvas.setAttribute('height', `${this.canvas.height}`);
		fixCanvasDpi(this.canvas);

		this.draw();
	};

	private _onOrientationChange = () => {
		setTimeout(() => {
			this._onResize();
		}, 300);
	};

	private _onVisibilityChange = () => {
		this._lastTickTimeStamp = -1;
	};

	private _addListeners() {
		window.addEventListener('resize', this._onResize);
		window.addEventListener('orientationchange', this._onOrientationChange);
		window.addEventListener('visibilitychange', this._onVisibilityChange);
	}

	dispose() {
		this.disposed = true;
		this.running = false;
		window.cancelAnimationFrame(this._animationFrame);
		window.removeEventListener('resize', this._onResize);
		window.removeEventListener('orientationchange', this._onOrientationChange);
		window.removeEventListener('visibilitychange', this._onVisibilityChange);
	}
}
