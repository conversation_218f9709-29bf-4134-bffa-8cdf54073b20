<script lang="ts">
	interface Props {
		svg: SVGElement;
	}

	let { svg = $bindable() }: Props = $props();
</script>

<svg
	bind:this={svg}
	class="hidden"
	width="40"
	height="40"
	viewBox="0 0 40 40"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
>
	<g id="moon">
		<path
			id="moon-1"
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M30 0H24V1V2H20V4H18V6H16V8H14V11H12V14H10V26H12V29H14V32H16V34H18V36H20V38H24V40H27H30V39H27V37H24V36V35H21V34V32V31H19V29H17V26H16V14H17V11H18H19V9H20H21V5H24V3H27V1H30V0Z"
			class="fill-transparent dark:fill-base-content"
		/>
		<path
			id="moon-2"
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M24 0H30V2H27V4H25V6H23V8H22V11H21V14V26V29H22V32H23V34H25V36H27V38H30V40H24V38H20V36H18V34H16V32H14V29H12V26H10V14H12V11H14V8H16V6H18V4H20V2H24V0Z"
			class="fill-transparent dark:fill-base-content"
		/>
		<path
			id="moon-3"
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M30 0H24V2H20V4H18V6H16V8H14V11H12V14H10V26H12V29H14V32H16V34H18V36H20V38H24V40H30V38V37H28V36V35H27V34V32V29V26V14V11V8V6H28V4V3H30V2V0Z"
			class="fill-transparent dark:fill-base-content"
		/>
		<path
			id="moon-4"
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M14 0H26V2H30V4H32V6H34V8H36V11H38V14H40V26H38V29H36V32H34V34H32V36H30V38H26V40H14V38H10V36H8V34H6V32H4V29H2V26H0V14H2V11H4V8H6V6H8V4H10V2H14V0Z"
			class="fill-transparent dark:fill-base-content"
		/>
		<path
			id="moon-5"
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M10 40L16 40L16 38L20 38L20 36L22 36L22 34L13 34L13 35L12 35L12 36L12 37L10 37L10 38L10 40ZM13 32L13 34L24 34L24 32L26 32L26 29L28 29L28 26L30 26L30 14L28 14L28 11L26 11L26 8L24 8L24 6L22 6L22 4L20 4L20 2L16 2L16 -1.22392e-06L10 -1.74846e-06L10 2L10 3L12 3L12 4L12 6L13 6L13 8L13 11L13 14L13 26L13 29L13 32Z"
			class="fill-transparent dark:fill-base-content"
		/>
		<path
			id="moon-6"
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M16 0H10V2H13V4H15V6H17V8H18V11H19V14V26V29H18V32H17V34H15V36H13V38H10V40H16V38H20V36H22V34H24V32H26V29H28V26H30V14H28V11H26V8H24V6H22V4H20V2H16V0Z"
			class="fill-transparent dark:fill-base-content"
		/>
		<path
			id="moon-7"
			fill-rule="evenodd"
			clip-rule="evenodd"
			d="M10 0H16V1V2H20V4H22V6H24V8H26V11H28V14H30V26H28V29H26V32H24V34H22V36H20V38H16V40H13H10V39H13V37H16V36V35H19V34V32V31H21V29H23V26H24V14H23V11H22H21V9H20H19V5H16V3H13V1H10V0Z"
			class="fill-transparent dark:fill-base-content"
		/>
	</g>
</svg>
