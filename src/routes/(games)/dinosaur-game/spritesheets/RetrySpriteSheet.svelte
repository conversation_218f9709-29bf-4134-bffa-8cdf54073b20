<script lang="ts">
	interface Props {
		svg: SVGElement;
	}

	let { svg = $bindable() }: Props = $props();
</script>

<svg
	bind:this={svg}
	class="hidden"
	width="36"
	height="32"
	viewBox="0 0 36 32"
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
>
	<path
		fill-rule="evenodd"
		clip-rule="evenodd"
		d="M3 1H33V2H34V3H35V29H34V30H33V31H3V30H2V29H1V3H2V2H3V1Z"
		fill="currentColor"
	/>
	<path
		fill-rule="evenodd"
		clip-rule="evenodd"
		d="M33 0H3V1H2V2H1V3H0V29H1V30H2V31H3V32H33V31H34V30H35V29H36V3H35V2H34V1H33V0ZM33 1V2H34V3H35V29H34V30H33V31H3V30H2V29H1V3H2V2H3V1H33ZM15 6H16V7H17V8H18V9H19V10H20V11H19V12H18V13H17V14H16V15H15V12H11V20H25V12H23V11V10V9H26V10H27V11H28V12V20V21H27V22H26V23H11H10V22H9V21H8V11H9V10H10V9H11H15V6Z"
		fill="currentColor"
		class="fill-base-100"
	/>
</svg>
