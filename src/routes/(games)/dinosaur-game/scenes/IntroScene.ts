/* eslint-disable @typescript-eslint/no-non-null-assertion */
import type { Point2D } from '$lib/models/Point2D';
import { AnimatedSVGSprite } from '../util/AnimatedSVGSprite';
import { gravity, positionX, friction, maxSpeedY } from '../game-objects/Dino';
import { Floor } from '../game-objects/Floor';
import { getFloorPosition } from '../game-objects/getFloorPosition';

export type IntroSceneOptions = {
	context: CanvasRenderingContext2D;
	onFinish: () => void;
	dinoSpriteSheet: SVGElement;
	floorSpriteSheet: SVGElement;
};

export class IntroScene {
	private _dino: IntroSceneDino;
	private _floor: IntroSceneFloor;

	constructor({ context, dinoSpriteSheet, floorSpriteSheet, onFinish }: IntroSceneOptions) {
		this._dino = new IntroSceneDino(context, dinoSpriteSheet, onFinish);
		this._floor = new IntroSceneFloor(context, floorSpriteSheet);
	}

	get isFinished() {
		return this._dino.isFinished;
	}

	get started() {
		return this._dino.started;
	}

	start() {
		this._dino.start();
	}

	draw() {
		this._floor.draw(this._dino.progress);
		this._dino.draw();
	}

	tick(time: number) {
		this._dino.tick(time);
		this._floor.tick();
	}
}

// Floor
class IntroSceneFloor {
	private _context: CanvasRenderingContext2D;
	private _floor: Floor;
	private _minW = 40;

	constructor(context: CanvasRenderingContext2D, spriteSheet: SVGElement) {
		this._context = context;
		this._floor = new Floor(context, spriteSheet);
	}

	draw(progress: number) {
		const context = this._context;
		context.save();
		context.beginPath();
		context.rect(
			0,
			0,
			Math.max(this._minW * devicePixelRatio, context.canvas.width * progress),
			context.canvas.height,
		);
		context.clip();
		this._floor.draw();
		context.restore();
	}

	tick() {
		this._floor.tick();
	}
}

// Dino
type IntroSceneDinoState = 'idle' | 'jump' | 'walk';

const _positionXPercentageIncrement = 0.05;

class IntroSceneDino {
	private _context: CanvasRenderingContext2D;
	private _animations: Record<IntroSceneDinoState, AnimatedSVGSprite>;
	private _state: IntroSceneDinoState = 'idle';
	private _speedY = 0;
	readonly position: Point2D = {
		x: 0,
		y: 0,
	};
	private _onFinish: () => void;

	started = false;
	progress = 0;
	isFinished = false;

	constructor(context: CanvasRenderingContext2D, spriteSheet: SVGElement, onFinish: () => void) {
		this._context = context;
		this._onFinish = onFinish;
		const idle = new AnimatedSVGSprite([spriteSheet.querySelector('[id=idle]')!]);

		this._animations = {
			idle,
			jump: idle,
			walk: new AnimatedSVGSprite([
				spriteSheet.querySelector('[id=dino-walk-1]')!,
				spriteSheet.querySelector('[id=dino-walk-2]')!,
			]),
		};
		this.position.y = this._maxY;
	}

	private get _currentAnimation() {
		return this._animations[this._state];
	}

	private get _maxY() {
		return getFloorPosition(this._context, this._animations.idle.height) + devicePixelRatio;
	}

	private get _isGrounded() {
		return Math.abs(this.position.y - this._maxY) < 0.01;
	}

	draw() {
		const context = this._context;
		context.save();
		context.translate(this.position.x, this.position.y);
		this._currentAnimation.draw(context);
		context.restore();
	}

	tick(time: number) {
		this._currentAnimation.tick(time);

		if (this.isFinished) {
			return;
		}

		this._speedY -= gravity * devicePixelRatio;
		this.position.x = this.progress * positionX * devicePixelRatio;
		this.position.y -= this._speedY;
		this._speedY *= friction; // friction

		if (this.position.y > this._maxY) {
			this.position.y = this._maxY;
			this._speedY = 0;
		}

		if (this._isGrounded && this._state === 'jump') {
			this._state = 'walk';
		}

		if (this._state === 'walk') {
			this.progress += _positionXPercentageIncrement;

			if (this.progress >= 1) {
				this.isFinished = true;
				this._onFinish();
			}
		}
	}

	start() {
		if (this.started) {
			return;
		}
		this._state = 'jump';
		this._speedY = maxSpeedY * devicePixelRatio;
		this.started = true;
	}
}
