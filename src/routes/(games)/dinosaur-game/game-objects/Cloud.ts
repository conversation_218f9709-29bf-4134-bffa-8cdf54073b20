import { SVGSprite } from '../util/AnimatedSVGSprite';
import type { HitBox } from '$lib/util/HitBox';
import { WorldObject } from './WorldObject';

export class Cloud extends WorldObject {
	hitBoxes: HitBox[] = [];
	private _sprite: SVGSprite;
	private _yFactor = Math.random();

	constructor(context: CanvasRenderingContext2D, spriteSheet: SVGElement) {
		super(context);
		this._context = context;
		this._sprite = new SVGSprite(spriteSheet);
	}

	draw() {
		const context = this._context;
		context.save();

		context.translate(this.position.x, this.position.y);
		context.globalAlpha = 0.3;
		this._sprite.draw(this._context);

		context.restore();
	}

	tick() {
		const maxY = this._context.canvas.height / 2 - this._sprite.height;
		this.position.y = maxY * this._yFactor;
	}

	resetYFactor() {
		this._yFactor = Math.random();
	}

	get width() {
		return this._sprite.width;
	}
}
