import { MinesweeperGridItem, type MinesweeperGridItemOptions } from './MinesweeperGridItem';

export type MinesweeperNumberType = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8;

export type MinesweeperNumberOptions = MinesweeperGridItemOptions & {
	number: MinesweeperNumberType;
	color: string;
};

export class MinesweeperNumber extends MinesweeperGridItem {
	number: MinesweeperNumberType = 1;
	color = '#000';

	constructor({ number, color, ...options }: MinesweeperNumberOptions) {
		super(options);

		this.number = number;
		this.color = color;
	}

	draw() {
		const context = this.context;
		const itemSize = this.size;
		const fontAdjustment = itemSize / 2;

		context.font = `bold ${0.5 * itemSize}px ${
			window.getComputedStyle(this.context.canvas).fontFamily
		}`;
		context.textBaseline = 'middle';
		context.textAlign = 'center';
		context.fillStyle = this.color;
		context.fillText(
			`${this.number}`,
			this.gridStart.x + this.column * itemSize + fontAdjustment,
			this.gridStart.y + this.row * itemSize + fontAdjustment,
		);
	}
}
