import { MinesweeperGridItem } from './MinesweeperGridItem';

export class MinesweeperBomb extends MinesweeperGridItem {
	exploded = false;

	draw() {
		const context = this.context;
		const itemSize = this.size;
		const fontAdjustment = itemSize / 2;

		context.font = `bold ${0.5 * itemSize}px ${
			window.getComputedStyle(this.context.canvas).fontFamily
		}`;
		context.textBaseline = 'middle';
		context.textAlign = 'center';
		context.fillText(
			this.exploded ? '💥' : '💣',
			this.gridStart.x + this.column * itemSize + fontAdjustment,
			this.gridStart.y + this.row * itemSize + fontAdjustment,
		);
	}
}
