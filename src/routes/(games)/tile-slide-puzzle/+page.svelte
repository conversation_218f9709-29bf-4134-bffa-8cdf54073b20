<script lang="ts">
	import PageTransition from '$lib/components/PageTransition.svelte';
	import TileSlidePuzzle from './TileSlidePuzzle.svelte';
	import { MetaTags } from 'svelte-meta-tags';
</script>

<MetaTags
	title="Play Tile Slide Puzzle Game Online for Free"
	titleTemplate="%s | Lofi and Games"
	description="Play tile slide puzzle game online for free. Beautiful tile slide puzzle game, delightful gaming experience, no download nor registration is required."
	canonical="https://www.lofiandgames.com/tile-slide-puzzle"
	openGraph={{
		url: 'https://www.lofiandgames.com/tile-slide-puzzle',
		images: [
			{
				url: 'https://www.lofiandgames.com/share-tile-slide-puzzle.png',
				width: 1200,
				height: 630,
				alt: 'Tile Slide Puzzle Game',
			},
		],
		siteName: 'Lofi and Games',
		type: 'game',
	}}
	twitter={{
		cardType: 'summary_large_image',
		title: 'Play Tile Slide Puzzle Game on Lofi and Games',
		image: 'https://www.lofiandgames.com/share-tile-slide-puzzle.png',
		site: 'https://www.lofiandgames.com/tile-slide-puzzle',
	}}
/>

<PageTransition>
	<TileSlidePuzzle />
</PageTransition>
