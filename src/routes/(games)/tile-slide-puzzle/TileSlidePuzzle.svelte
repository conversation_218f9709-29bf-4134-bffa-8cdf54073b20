<script lang="ts">
	import { onDestroy, onMount, untrack } from 'svelte';
	import { quintIn } from 'svelte/easing';
	import { TileSlidePuzzleGame } from './TileSlidePuzzleGame.svelte';
	import { heroTranslate } from '$lib/functions/heroTranslate';
	import { tileSlidePuzzleSoundResources } from './tileSlidePuzzleSoundResources';
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import GameLayout from '$lib/components/GameLayout/GameLayout.svelte';
	import GameIsland from '$lib/components/GameIsland/GameIsland.svelte';
	import TileSlidePuzzleInstructionsIsland from './TileSlidePuzzleInstructionsIsland.svelte';
	import { pickRandom, Smush32 } from '@thi.ng/random';
	import { GameContext } from '$lib/util/GameContext/GameContext.svelte';
	import { Stats } from '$lib/util/Stats.svelte';
	import SettingsIcon from '$lib/components/Icons/SettingsIcon.svelte';
	import Toggle from '$lib/components/Toggle/Toggle.svelte';
	import InfoSolidIcon from '$lib/components/Icons/InfoSolidIcon.svelte';
	import TileSlidePuzzleInfoModal from './TileSlidePuzzleInfoModal.svelte';
	import { Leaderboard } from '$lib/util/Leaderboard.svelte';
	import MoreGamesButton from '$lib/components/MoreGamesButton/MoreGamesButton.svelte';

	let gameElement = $state<HTMLElement>();
	let isGridSizeDropdownOpen = $state(false);
	let isSettingsOpen = $state(false);
	let isInfoModalOpen = $state(false);

	const defaultHeroTransitionTime = 200;
	const defaultSlideDuration = defaultHeroTransitionTime / 3;
	let slideDuration = $state(defaultSlideDuration);

	const context = new GameContext({
		GameClass: TileSlidePuzzleGame,
		gameKey: 'tile-slide-puzzle',
		sounds: {
			resources: tileSlidePuzzleSoundResources,
			lifecycle: {
				createGame: tileSlidePuzzleSoundResources.replay,
				win: tileSlidePuzzleSoundResources.gameWin,
			},
		},
		settings: {
			defaultSettings: {
				size: 3,
				animated: true,
			},
		},
		formatted(context) {
			return {
				name: 'Tile Slide Puzzle',
				variant: `${context.game?.size ?? 3}x${context.game?.size ?? 3}`,
				leaderboardVariant: `${context.game?.size ?? 3}x${context.game?.size ?? 3}`,
			};
		},
		defaultGameProps(context) {
			return {
				audios: context.sounds,
				onGameOver,
				size: context.settingsManager.settings.size,
				slideDuration,
				targetElement: gameElement!,
				timer: context.timer,
			};
		},
		stats({ props, context }) {
			const size = context.game?.size ?? 3;
			const game = context.game;

			return {
				stats: new Stats({
					...props,
					gameVariant: `${size}x${size}`,
					liveStats: {
						moves: {
							name: 'Moves',
							unit: 'plain',
							value: () => game?.moves ?? 0,
							metrics: {
								total: {
									key: 'totalMoves',
									name: 'Total Moves',
								},
								average: {
									key: 'averageMoves',
									name: 'Average Moves',
								},
								max: {
									key: 'mostMoves',
									name: 'Most Moves',
								},
								min: {
									key: 'fewestMoves',
									name: 'Fewest Moves',
									useAsBest: true,
								},
							},
						},
					},
					initialPinnedStats: ['time', 'moves'],
				}),
				canUpdateWithGameLost(game: TileSlidePuzzleGame) {
					return !game.isWon && game.moves >= 10;
				},
				visibleStats: [
					'bestTime',
					'averageTime',
					'fewestMoves',
					'averageMoves',
					'wonGames',
					'totalGames',
				],
			};
		},
		dailyGame(context) {
			return {
				type: 'seed',
				firstAvailableGameDate: new Date('2025/01/11'),
				toProps(seed: number) {
					const random = new Smush32(seed as number);
					const sizes = [4, 5, 6];
					const size = pickRandom(sizes, random);

					return {
						targetElement: gameElement!,
						size,
						slideDuration,
						audios: context.sounds,
						timer: context.timer,
						seed,
						onGameOver,
					};
				},
			};
		},
		leaderboard: (context) => {
			return {
				leaderboard: new Leaderboard({
					game: context.gameKey,
					firstAvailableDate: new Date('2025/05/08'),
					hasMoves: true,
					gameVariant: `${context.game?.size}x${context.game?.size}`,
					order: 'lower-first',
				}),
				sendScoreOn: ['won'],
				getScore: (game) => {
					return {
						score: game.score,
						moves: game.moves,
					};
				},
			};
		},
		onWillCreateGame({ context, newGameOptions, previousGame }) {
			previousGame?.dispose();
			isGridSizeDropdownOpen = false;

			if (!newGameOptions.isDaily) {
				context.settingsManager.settings.size = newGameOptions.size;
			}
		},
		onDispose(context) {
			context.game?.dispose();
		},
	});

	let game = $derived(context.game);

	let [send, receive] = $derived.by(() => {
		return heroTranslate({
			fallback: () => {
				return {
					duration: context.settingsManager.settings.animated ? defaultHeroTransitionTime : 0,
					easing: quintIn,
					css: (t) => `
					opacity: ${t}
				`,
				};
			},
			duration: context.settingsManager.settings.animated ? defaultHeroTransitionTime : 0,
		});
	});

	$effect(function syncSettings() {
		slideDuration = context.settingsManager.settings.animated ? defaultSlideDuration : 0;

		untrack(() => {
			if (game) {
				game.slideDuration = slideDuration;
			}
		});
	});

	function onGameOver() {
		context.handleGameOver('won');
	}

	onMount(() => {
		context.load();
	});

	onDestroy(() => {
		context.dispose();
	});
</script>

<TileSlidePuzzleInfoModal bind:isOpen={isInfoModalOpen} />

<GameLayout>
	{#snippet Island()}
		<GameIsland {context}>
			{#snippet InstructionsIsland()}
				<TileSlidePuzzleInstructionsIsland />
			{/snippet}
		</GameIsland>
	{/snippet}

	<section
		class="max-h-screen-no-navbar flex items-center justify-center size-full select-none touch-pinch-zoom"
		bind:this={gameElement}
	>
		<div class="grow w-full max-w-md">
			<div class="relative size-full gap-1 bg-base-300 aspect-square">
				<div class="absolute -top-10 left-0 flex items-center gap-2">
					<Dropdown bind:open={isGridSizeDropdownOpen}>
						<DropdownButton class="btn-sm">
							{#if game}
								{game?.size}x{game?.size}
							{/if}
						</DropdownButton>

						<DropdownContent menu>
							<DropdownItem>
								<button
									class:menu-active={game?.size === 3}
									onclick={() => context.createGame({ size: 3 })}>3x3</button
								>
							</DropdownItem>
							<DropdownItem>
								<button
									class:menu-active={game?.size === 4}
									onclick={() => context.createGame({ size: 4 })}>4x4</button
								>
							</DropdownItem>
							<DropdownItem>
								<button
									class:menu-active={game?.size === 5}
									onclick={() => context.createGame({ size: 5 })}>5x5</button
								>
							</DropdownItem>
							<DropdownItem>
								<button
									class:menu-active={game?.size === 6}
									onclick={() => context.createGame({ size: 6 })}>6x6</button
								>
							</DropdownItem>
						</DropdownContent>
					</Dropdown>

					<Dropdown bind:open={isSettingsOpen}>
						<DropdownButton class="btn-sm" aria-label="Show settings">
							<SettingsIcon class="size-5" />
						</DropdownButton>

						<DropdownContent class="w-60">
							<DropdownItem>
								<Toggle
									name="toggle animations"
									bind:checked={context.settingsManager.settings.animated}>Animations</Toggle
								>
							</DropdownItem>
						</DropdownContent>
					</Dropdown>

					<button class="btn btn-sm" onclick={() => (isInfoModalOpen = true)}>
						<InfoSolidIcon class="size-5" />
					</button>

					<MoreGamesButton class="btn-sm" />
				</div>

				<!-- Game -->
				<div class="size-full cursor-pointer">
					{#each game?.board ?? [] as row, rowIndex (`${rowIndex} - ${game?.id ?? 1}`)}
						{#each row as item, columnIndex (`${columnIndex} - ${item}`)}
							{#if item !== TileSlidePuzzleGame.emptyItem}
								<button
									in:send|global={{ key: `${item}` }}
									out:receive|global={{ key: `${item}` }}
									class="absolute flex aspect-square"
									style="
										width: {100 / (game?.size ?? 3)}%;
										top: {(100 * rowIndex) / (game?.size ?? 1)}%;
										left: {(100 * columnIndex) / (game?.size ?? 1)}%;
									"
									onclick={() => game?.moveItem?.({ row: rowIndex, column: columnIndex })}
								>
									<div class="w-full grow border-2 border-transparent h-full">
										<div
											class="flex-center size-full bg-current text-center font-bold"
											class:text-3xl={game && game.size > 3}
											class:md:text-4xl={game && game.size > 3}
											class:text-5xl={game && game.size === 3}
										>
											<span class="text-base-100">
												{item ?? ''}
											</span>
										</div>
									</div>
								</button>
							{/if}
						{/each}
					{/each}
				</div>
			</div>
		</div>
	</section>
</GameLayout>
