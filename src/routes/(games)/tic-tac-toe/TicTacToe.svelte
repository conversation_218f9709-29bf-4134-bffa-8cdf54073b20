<script lang="ts">
	import { TicTacToeGame } from './TicTacToeGame.svelte';
	import type { Difficulty, Player } from './TicTacToeGame.svelte';
	import TicTacToeField from './TicTacToeField.svelte';
	import TicTacToeX from './TicTacToeX.svelte';
	import TicTacToeCircle from './TicTacToeCircle.svelte';
	import TicTacToeScore from './TicTacToeScore.svelte';
	import TicTacToePositionedLine from './TicTacToePositionedLine.svelte';
	import TicTacToeReplay from './TicTacToeReplay.svelte';
	import { ticTacToeSoundResources } from './ticTacToeSoundResources';
	import TicTacToeInfoModal from './TicTacToeInfoModal.svelte';
	import GameLayout from '$lib/components/GameLayout/GameLayout.svelte';
	import GameIsland from '$lib/components/GameIsland/GameIsland.svelte';
	import { Stats } from '$lib/util/Stats.svelte';
	import capitalize from 'lodash/capitalize';
	import { onDestroy, onMount } from 'svelte';
	import { GameContext } from '$lib/util/GameContext/GameContext.svelte';
	import InfoSolidIcon from '$lib/components/Icons/InfoSolidIcon.svelte';
	import OpponentButton, { type DifficultyOption } from '$lib/components/OpponentButton.svelte';
	import MoreGamesButton from '$lib/components/MoreGamesButton/MoreGamesButton.svelte';

	type TicTacToeOpponent = 'cpu' | 'player2';
	const playWaitTime = 500;
	let player1VsPlayer2Score = $state(0);
	let player1VsCpuScore = $state(0);
	let player2Score = $state(0);
	let cpuScore = $state(0);
	let turnStart: Player = 'x';
	let lastCpuPlayTime = -1;
	let isFirstCpuPlay = false;
	let isInfoModalOpen = $state(false);

	const difficulties: DifficultyOption<Difficulty>[] = [
		{ value: 'easy', label: 'Easy' },
		{ value: 'normal', label: 'Normal' },
		{ value: 'hard', label: 'Hard' },
		{ value: 'impossible', label: 'Impossible' },
	];

	const context = new GameContext({
		GameClass: TicTacToeGame,
		gameKey: 'tic-tac-toe',
		settings: {
			defaultSettings: {
				difficulty: 'normal' as Difficulty,
				opponent: 'cpu' as TicTacToeOpponent,
			},
		},
		sounds: {
			resources: ticTacToeSoundResources,
		},
		formatted(context) {
			return {
				name: 'Tic Tac Toe',
				variant: `You vs ${context.settingsManager.settings.opponent === 'cpu' ? `CPU ${capitalize(context.settingsManager.settings.difficulty)}` : 'Player 2'}`,
			};
		},
		stats({ context, props }) {
			return {
				stats: new Stats({
					...props,
					gameVariant:
						context.settingsManager.settings.opponent === 'cpu'
							? `cpu-${context.settingsManager.settings.difficulty}`
							: 'player-2',
					liveStats: {
						player1: {
							name: 'You (X)',
							unit: 'plain',
							value() {
								return context.settingsManager.settings.opponent === 'cpu'
									? player1VsCpuScore
									: player1VsPlayer2Score;
							},
						},
						opponent: {
							name:
								context.settingsManager.settings.opponent === 'cpu' ? 'CPU (O)' : 'Player 2 (O)',
							unit: 'plain',
							value() {
								return context.settingsManager.settings.opponent === 'cpu'
									? cpuScore
									: player2Score;
							},
						},
					},
					initialPinnedStats: ['player1', 'opponent'],
				}),
				canUpdateWithGameLost() {
					return false;
				},
				visibleStats: ['wonGames', 'totalGames'],
			};
		},
		defaultGameProps(context) {
			return {
				difficulty: context.settingsManager.settings.difficulty,
				turn: 'x' as Player,
				timer: context.timer,
			};
		},
		onWillCreateGame({ context, newGameOptions }) {
			context.settingsManager.settings.difficulty = newGameOptions.difficulty;
		},
	});

	let game = $derived(context.game);

	function changeOpponent(newOpponent: TicTacToeOpponent, newDifficulty?: Difficulty) {
		context.settingsManager.settings.opponent = newOpponent;

		if (newDifficulty) {
			context.settingsManager.settings.difficulty = newDifficulty;
		}

		context.createGame();
		context.sounds.changeDifficulty.play();
	}

	function replay() {
		if (!game) {
			return;
		}

		if (game.gameOverAt && performance.now() - game.gameOverAt > playWaitTime * 2) {
			isFirstCpuPlay = false;
			turnStart = turnStart === 'x' ? 'o' : 'x';

			context.createGame({ difficulty: game.difficulty, turn: turnStart });

			if (context.settingsManager.settings.opponent === 'cpu' && turnStart === 'o') {
				isFirstCpuPlay = true;

				setTimeout(() => {
					playCPUTurn();
				}, playWaitTime / 2);
			}
		}
	}

	function increaseScoreIfNeeded() {
		if (!game) {
			return;
		}

		if (game.winner) {
			if (game.winner.player === 'x') {
				context.sounds?.victory?.play();
				if (context.settingsManager.settings.opponent === 'cpu') {
					player1VsCpuScore += 1;
				} else {
					player1VsPlayer2Score += 1;
				}
			} else if (context.settingsManager.settings.opponent === 'cpu') {
				context.sounds?.defeat?.play();
				cpuScore += 1;
			} else {
				context.sounds?.victory?.play();
				player2Score += 1;
			}

			context.handleGameOver(game.winner.player === 'x' ? 'won' : 'lost', {
				handleConfetti: false,
			});
		} else {
			context.handleGameOver('lost');
			context.sounds?.tie?.play();
		}
	}

	function playCPUTurn() {
		if (!game) {
			return;
		}

		lastCpuPlayTime = performance.now();

		const cpuPlay = game.playCPUTurn();

		if (cpuPlay !== null) {
			context.sounds?.o?.play();

			if (game.isOver()) {
				setTimeout(() => {
					increaseScoreIfNeeded();
				}, playWaitTime);
			}
		}
	}

	function handleClick(row: number, column: number) {
		if (!game) {
			return;
		}

		/**
		 * Prevent user from immediatly playing after the CPU has made the first move.
		 * Without this safe guard, it seems that the CPU has played twice
		 */
		if (isFirstCpuPlay && performance.now() - lastCpuPlayTime < playWaitTime * 2) {
			return;
		}

		let playAs: Player = 'x';

		if (context.settingsManager.settings.opponent === 'player2' && game.turn === 'o') {
			playAs = 'o';
		}

		if (game.isOver()) {
			replay();
		} else if (game.playAt(row, column, playAs)) {
			isFirstCpuPlay = false;

			if (playAs === 'x') {
				context.sounds?.x?.play();
			} else {
				context.sounds?.o?.play();
			}

			if (game.isOver()) {
				setTimeout(increaseScoreIfNeeded, playWaitTime);
			} else if (context.settingsManager.settings.opponent === 'cpu') {
				setTimeout(playCPUTurn, playWaitTime);
			}
		}
	}

	onMount(() => {
		context.load();
	});

	onDestroy(() => {
		context.dispose();
	});
</script>

<GameLayout>
	{#snippet Island()}
		<GameIsland {context} gameOverStrategy="none" onNewGame={replay} />
	{/snippet}

	<div class="flex-center w-full flex-col">
		<div class="max-w-[402px] mx-auto w-full flex items-center gap-2">
			<OpponentButton
				dropdownButtonClass="btn-sm"
				opponent={context.settingsManager.settings.opponent === 'player2'
					? 'local-multiplayer'
					: 'cpu'}
				difficulty={context.settingsManager.settings.difficulty}
				{difficulties}
				onOpponentChange={(opponent, difficulty) => {
					changeOpponent(opponent === 'local-multiplayer' ? 'player2' : 'cpu', difficulty);
				}}
			/>

			<button class="btn btn-sm" onclick={() => (isInfoModalOpen = true)} aria-label="show info">
				<InfoSolidIcon class="size-5" />
			</button>

			<MoreGamesButton class="btn-sm" />
		</div>

		<section
			class="field relative mx-auto my-12 flex w-full flex-col items-center justify-center overflow-hidden px-4 z-10"
		>
			{#if game?.isOver()}
				<TicTacToeReplay />
			{/if}

			<div
				class:opacity-70={game?.tie}
				class="relative flex size-full transition-opacity aspect-square"
			>
				{#if game}
					<TicTacToePositionedLine winner={game.winner} />

					<div class="z-10 grid size-full cursor-pointer grid-cols-3">
						{#each game.board as row, rowIndex}
							{#each row as item, colIndex}
								<button
									class="play-container flex items-center justify-center aspect-square cursor-pointer"
									onclick={() => handleClick(rowIndex, colIndex)}
									aria-label="row {rowIndex + 1} column {colIndex + 1}"
								>
									{#if item === 'o'}
										<TicTacToeCircle />
									{/if}
									{#if item === 'x'}
										<TicTacToeX />
									{/if}
								</button>
							{/each}
						{/each}
					</div>
				{/if}

				<TicTacToeField />
			</div>
		</section>

		<TicTacToeScore
			playerScore={context.settingsManager.settings.opponent === 'cpu'
				? player1VsCpuScore
				: player1VsPlayer2Score}
			{cpuScore}
			{player2Score}
			opponent={context.settingsManager.settings.opponent}
			turn={context.game?.turn ?? 'x'}
		/>
	</div>
</GameLayout>

<TicTacToeInfoModal bind:isOpen={isInfoModalOpen} />

<style>
	.field {
		max-width: 402px;
		max-height: 402px;
	}

	/* Fix size on Safari */
	.play-container::after {
		content: '';
		padding-top: 100%;
	}
</style>
