import { get2DGrid } from '$lib/functions/get2DGrid';
import { shuffleMutate } from '$lib/functions/shuffle';
import type { GridSize } from '$lib/models/GridSize';
import type { GameSound } from '$lib/util/GameSound.svelte';
import type { Language } from '$lib/util/languages';
import type { WordLocation, WordSearchGameStrategy, WordSearchHints } from './WordSearchTypes';
import { ClassicWordSearchStrategy } from './strategies/ClassicWordSearchStrategy';
import { MazeWordSearchStrategy } from './strategies/MazeWordSearchStrategy';

export type WordSearchAudios = {
	replay: GameSound;
	gameWin: GameSound;
	move1: GameSound;
	move2: GameSound;
};

export const wordSearchDifficulties = ['easy', 'medium', 'hard'] as const;

export type WordSearchDifficulty = (typeof wordSearchDifficulties)[number];

export const wordSearchLanguages = ['en', 'pt'] as Language[];

export type GameMode = 'classic' | 'maze';

const desiredColumnsByDifficultyOnDesktop: Record<WordSearchDifficulty, number> = {
	easy: 7,
	medium: 12,
	hard: 18,
};

const desiredColumnsByDifficultyOnTablet: Record<WordSearchDifficulty, number> = {
	easy: 7,
	medium: 10,
	hard: 14,
};

const desiredColumnsByDifficultyOnMobile: Record<WordSearchDifficulty, number> = {
	easy: 6,
	medium: 8,
	hard: 12,
};

const maxWordsPerDifficulty: Record<WordSearchDifficulty, number> = {
	easy: 6,
	medium: 12,
	hard: 20,
};

const wordsCache: Partial<Record<Language, string[]>> = {};

function getGridSize(
	difficulty: WordSearchDifficulty,
	mode: GameMode,
	gameRect: DOMRect,
): GridSize {
	const minSizeInPx = mode === 'classic' ? 32 : 40;
	const { width, height } = gameRect;

	if (width <= 0 || height <= 0) {
		throw new Error('Cannot get grid size for given constraints');
	}

	let columns = desiredColumnsByDifficultyOnMobile[difficulty];

	if (window.matchMedia('(min-width: 768px)').matches) {
		columns = desiredColumnsByDifficultyOnTablet[difficulty];
	}

	if (window.matchMedia('(min-width: 1024px)').matches) {
		columns = desiredColumnsByDifficultyOnDesktop[difficulty];
	}

	let rows = 1;

	while (width / columns < minSizeInPx) {
		columns -= 1;
	}

	const columnSizeInPx = width / columns;

	while (height / rows > columnSizeInPx) {
		rows += 1;
	}

	rows -= 1;

	return {
		columns,
		rows,
	};
}

interface Props {
	language: Language;
	difficulty: WordSearchDifficulty;
	mode: GameMode;
	gameRect: DOMRect;
	onReady: () => void;
}

export class WordSearchGame {
	id = Math.floor(Math.random() * 1e9);
	grid: string[][] = $state([]);
	words: string[] = $state([]);
	sortedWords: string[] = $state([]);
	gridSize: GridSize = $state({
		columns: 0,
		rows: 0,
	});
	foundWords: WordLocation[] = $state([]);
	currentSelection: WordLocation = $state({
		word: '',
		positions: [],
	});
	wordHints: WordSearchHints = $state({});
	difficulty: WordSearchDifficulty = $state('easy');
	gameRect: DOMRect;
	language: Language;
	mode: GameMode;
	strategy?: WordSearchGameStrategy;
	onReady: () => void;

	constructor({ difficulty, gameRect, language, mode, onReady }: Props) {
		this.words = [];
		this.mode = mode;
		this.language = language;
		this.gameRect = gameRect;
		this.gridSize = getGridSize(difficulty, mode, gameRect);
		this.grid = get2DGrid(this.gridSize.rows, this.gridSize.columns, () => '');
		this.foundWords = [];
		this.difficulty = difficulty;
		this.onReady = onReady;
		this.init();
	}

	private async init() {
		this.words = shuffleMutate(await this.fetchWords());
		this.strategy =
			this.mode === 'classic'
				? new ClassicWordSearchStrategy(this.grid)
				: new MazeWordSearchStrategy(this.grid);
		const placeWordsResult = this.strategy.placeWords(
			this.words,
			maxWordsPerDifficulty[this.difficulty],
		);
		this.words = placeWordsResult.words;
		this.wordHints = placeWordsResult.hints;
		this.updateSortedWords();
		this.fillEmptySpaces();
		this.onReady();
	}

	private updateSortedWords() {
		const foundWords = this.foundWords
			.map((location) => location.word)
			.sort((a, b) => a.localeCompare(b));
		const notFoundWords = this.words
			.filter((word) => !foundWords.includes(word))
			.sort((a, b) => a.localeCompare(b));

		this.sortedWords = [...notFoundWords, ...foundWords];
	}

	private fillEmptySpaces(): void {
		// TODO: Get letters with diacritics from words and add them to the letters string
		const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
		for (let y = 0; y < this.gridSize.rows; y++) {
			for (let x = 0; x < this.gridSize.columns; x++) {
				if (this.grid[y][x] === '') {
					this.grid[y][x] = letters[Math.floor(Math.random() * letters.length)];
				}
			}
		}
	}

	public printGrid(): void {
		for (const row of this.grid) {
			console.log(row.join(' '));
		}
	}

	public startSelection(startX: number, startY: number): void {
		if (this.isGameWon()) {
			return;
		}

		this.currentSelection.positions = [
			{
				x: startX,
				y: startY,
			},
		];

		this.currentSelection.word = this.grid[startY][startX];
	}

	public continueSelection(x: number, y: number): void {
		if (this.isGameWon()) {
			return;
		}

		if (this.currentSelection.positions.length === 0) {
			this.startSelection(x, y);
		}

		this.currentSelection = this.strategy!.continueSelection({
			currentSelection: this.currentSelection,
			x: x,
			y: y,
		});
	}

	public endSelection(): boolean {
		if (this.isGameWon()) {
			return false;
		}

		if (
			this.words.includes(this.currentSelection.word as string) &&
			!this.foundWords.some((location) => location.word === this.currentSelection.word)
		) {
			this.foundWords.push({
				...(this.currentSelection as WordLocation),
			});
			this.updateSortedWords();
			this.currentSelection = {
				word: '',
				positions: [],
			};
			return true;
		}
		this.currentSelection = {
			word: '',
			positions: [],
		};
		return false;
	}

	public isGameWon(): boolean {
		return this.foundWords.length === this.words.length;
	}

	private async fetchWords(): Promise<string[]> {
		if (!wordsCache[this.language]) {
			let allWords: string;

			switch (this.language) {
				case 'pt': {
					allWords = await fetch(
						'https://static.lofiandgames.com/generated-games/word-search/pt.txt',
					).then((res) => res.text());
					break;
				}
				case 'en': {
					allWords = await fetch(
						'https://static.lofiandgames.com/generated-games/word-search/en.txt',
					).then((res) => res.text());
					break;
				}
				default:
					allWords = await fetch(
						'https://static.lofiandgames.com/generated-games/word-search/en.txt',
					).then((res) => res.text());
					break;
			}

			const words = allWords
				.split('\n')
				.map((line) => line.replace('\r', ''))
				.map((word) => word.toLocaleUpperCase());
			wordsCache[this.language] = words;
		}

		return wordsCache[this.language] ?? [];
	}
}
