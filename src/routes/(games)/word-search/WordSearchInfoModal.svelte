<script lang="ts">
	import { wordSearchSoundResources as sounds } from './wordSearchSoundResources';
	import InfoModal from '$lib/components/InfoModal.svelte';
	interface Props {
		isOpen?: boolean;
	}

	let { isOpen = $bindable(false) }: Props = $props();
</script>

<InfoModal {sounds} bind:isOpen>
	<h1>Word Search</h1>

	<p>
		Word search is a puzzle game where players aim to find a list of words hidden within a grid of
		letters. These words can be placed in various directions, including horizontally, vertically,
		diagonally, and even backward. The challenge lies in spotting these words amidst the seemingly
		random arrangement of letters. It's a game of observation, patience, and a bit of strategy.
	</p>

	<p>
		Word search puzzles can be found in newspapers, magazines, puzzle books, and online platforms.
		They come in various themes, such as animals, foods, famous landmarks, and more, making them an
		educational tool as well as an entertaining pastime.
	</p>

	<h2>How to Play Word Search</h2>
	<h3>Step-by-Step Guide:</h3>

	<ol>
		<li>
			<strong>Select Your Puzzle:</strong> Choose a word search puzzle based on your preferred difficulty
			level or theme. Beginners might opt for smaller grids, while seasoned players can tackle larger,
			more complex puzzles.
		</li>

		<li>
			<strong>Review the Word List:</strong> Each puzzle comes with a list of words that you need to
			find within the grid. Familiarize yourself with this list before starting.
		</li>

		<li>
			<strong>Scan the Grid:</strong> Begin scanning the grid of letters to locate the words from the
			list. Start with the first letter of each word and look for matches in the grid.
		</li>

		<li>
			<strong>Identify Directions:</strong> Words can be hidden in multiple directions. Check horizontally
			(left to right and right to left), vertically (top to bottom and bottom to top), and diagonally.
		</li>

		<li>
			<strong>Highlight Found Words:</strong> Once you find a word, mark it off in the grid.
		</li>

		<li>
			<strong>Repeat:</strong> Continue this process until all words are found. Take your time and enjoy
			the challenge.
		</li>
	</ol>

	<h2>Tips for Mastering Word Search</h2>
	<h3>Enhance Your Word Search Skills:</h3>

	<ol>
		<li>
			<strong>Practice Regularly:</strong> Like any skill, regular practice will improve your word search
			abilities. Try solving different puzzles with varying difficulty levels.
		</li>

		<li>
			<strong>Pattern Recognition:</strong> Train your eyes to recognize patterns. This includes common
			word formations and familiar letter pairings.
		</li>

		<li>
			<strong>Use a Systematic Approach:</strong> Develop a methodical approach to scanning the grid.
			For instance, you can scan each row from left to right and top to bottom, ensuring you cover every
			section of the grid.
		</li>

		<li>
			<strong>Focus on Unique Letters:</strong> Start with words that contain unique or uncommon letters,
			as these are easier to spot in the grid.
		</li>

		<li>
			<strong>Stay Relaxed:</strong> Word search is meant to be enjoyable. If you're stuck, take a break
			and return with fresh eyes.
		</li>
	</ol>

	<h2>Conclusion</h2>

	<p>
		Word search puzzles are more than just a fun game; they offer numerous cognitive benefits,
		including improved memory, increased concentration, and enhanced vocabulary. By incorporating
		word search into your regular routine, you can enjoy these advantages while indulging in a
		satisfying and stimulating activity.
	</p>

	<p>
		Whether you're a seasoned puzzle enthusiast or a newcomer to the world of word search, there's
		always something new to discover and enjoy. So grab a puzzle book or open an online word search
		game and start finding those hidden words today!
	</p>

	<p>Happy puzzling!</p>
</InfoModal>
