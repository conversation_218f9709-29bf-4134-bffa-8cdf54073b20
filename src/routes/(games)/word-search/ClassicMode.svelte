<script lang="ts">
	interface Props {
		selected?: boolean;
	}

	let { selected = false }: Props = $props();
</script>

<svg
	class="size-full"
	class:selected
	fill="none"
	xmlns="http://www.w3.org/2000/svg"
	viewBox="0 0 196 196"
>
	<g clip-path="url(#classic-mode__a)">
		<path
			d="M27 27h141"
			class="text-blue-200 opacity-60 dark:text-blue-700 dark:opacity-40"
			stroke="currentColor"
			stroke-width="32"
			stroke-linecap="round"
		/>
		<path
			d="M168 27L27 168"
			class="text-red-200 opacity-60 dark:text-red-700 dark:opacity-40"
			stroke="currentColor"
			stroke-width="32"
			stroke-linecap="round"
			style="--index:1;--offset:200"
		/>
		<path
			d="M27 168h141"
			class="text-green-200 opacity-60 dark:text-green-700 dark:opacity-40"
			stroke="currentColor"
			stroke-width="32"
			stroke-linecap="round"
			style="--index:2"
		/>
		<circle cx="27" cy="27" r="5" stroke="currentColor" stroke-width="2" />
		<circle cx="74" cy="27" r="5" stroke="currentColor" stroke-width="2" />
		<circle cx="121" cy="27" r="5" stroke="currentColor" stroke-width="2" />
		<circle cx="168" cy="27" r="5" stroke="currentColor" stroke-width="2" />
		<circle cx="27" cy="74" r="5" stroke="currentColor" stroke-width="2" />
		<circle cx="74" cy="74" r="5" stroke="currentColor" stroke-width="2" />
		<circle cx="121" cy="74" r="5" stroke="currentColor" stroke-width="2" />
		<circle cx="168" cy="74" r="5" stroke="currentColor" stroke-width="2" />
		<circle cx="27" cy="121" r="5" stroke="currentColor" stroke-width="2" />
		<circle cx="74" cy="121" r="5" stroke="currentColor" stroke-width="2" />
		<circle cx="121" cy="121" r="5" stroke="currentColor" stroke-width="2" />
		<circle cx="168" cy="121" r="5" stroke="currentColor" stroke-width="2" />
		<circle cx="27" cy="168" r="5" stroke="currentColor" stroke-width="2" />
		<circle cx="74" cy="168" r="5" stroke="currentColor" stroke-width="2" />
		<circle cx="121" cy="168" r="5" stroke="currentColor" stroke-width="2" />
		<circle cx="168" cy="168" r="5" stroke="currentColor" stroke-width="2" />
	</g>
	<defs>
		<clipPath id="classic-mode__a">
			<path fill="#fff" d="M0 0h196v196H0z" />
		</clipPath>
	</defs>
</svg>

<style>
	@keyframes dash {
		to {
			stroke-dashoffset: 0;
		}
	}

	.selected path {
		--duration: 300ms;
		--offset: 141;
		--index: 0;

		stroke-dasharray: var(--offset);
		stroke-dashoffset: var(--offset);
		animation: dash var(--duration) ease-in-out forwards;
		animation-delay: calc(var(--duration) * (var(--index)));
	}
</style>
