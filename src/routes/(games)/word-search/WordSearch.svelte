<script lang="ts">
	import { fly } from 'svelte/transition';
	import CloseIcon from '$lib/components/Icons/CloseIcon.svelte';
	import { onDestroy, onMount } from 'svelte';
	import {
		WordSearchGame,
		wordSearchDifficulties,
		wordSearchLanguages,
		type GameMode,
		type WordSearchDifficulty,
	} from './game/WordSearchGame.svelte';
	import { shuffle } from '$lib/functions/shuffle';
	import WordSearchInfoModal from './WordSearchInfoModal.svelte';
	import { quadInOut, quintInOut } from 'svelte/easing';
	import { flip } from 'svelte/animate';
	import { languageToFlag } from '$lib/util/languages';
	import type { Language } from '$lib/util/languages';
	import type { Point2D } from '$lib/models/Point2D';
	import ClassicMode from './ClassicMode.svelte';
	import MazeMode from './MazeMode.svelte';
	import Dropdown from '$lib/components/Dropdown/Dropdown.svelte';
	import DropdownButton from '$lib/components/Dropdown/DropdownButton.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import GameLayout from '$lib/components/GameLayout/GameLayout.svelte';
	import Backdrop from '$lib/components/Backdrop.svelte';
	import GameIsland from '$lib/components/GameIsland/GameIsland.svelte';
	import { Stats } from '$lib/util/Stats.svelte';
	import capitalize from 'lodash/capitalize';
	import { GameContext } from '$lib/util/GameContext/GameContext.svelte';
	import { wordSearchSoundResources } from './wordSearchSoundResources';
	import InfoSolidIcon from '$lib/components/Icons/InfoSolidIcon.svelte';
	import { cn } from '$lib/util/cn';
	import CheckIcon from '$lib/components/Icons/CheckIcon.svelte';
	import { Leaderboard } from '$lib/util/Leaderboard.svelte';
	import MoreGamesButton from '$lib/components/MoreGamesButton/MoreGamesButton.svelte';

	let isInfoModalOpen = $state(false);

	let colors = $state([
		'text-game-word-search-1',
		'text-game-word-search-2',
		'text-game-word-search-3',
		'text-game-word-search-4',
		'text-game-word-search-5',
		'text-game-word-search-6',
		'text-game-word-search-7',
		'text-game-word-search-8',
		'text-game-word-search-9',
		'text-game-word-search-10',
		'text-game-word-search-11',
		'text-game-word-search-12',
		'text-game-word-search-13',
		'text-game-word-search-14',
		'text-game-word-search-15',
		'text-game-word-search-16',
		'text-game-word-search-17',
	]);
	let buttonsColors = [
		'bg-game-word-search-1 hover:bg-game-word-search-1',
		'bg-game-word-search-2 hover:bg-game-word-search-2',
		'bg-game-word-search-3 hover:bg-game-word-search-3',
		'bg-game-word-search-4 hover:bg-game-word-search-4',
		'bg-game-word-search-5 hover:bg-game-word-search-5',
		'bg-game-word-search-6 hover:bg-game-word-search-6',
		'bg-game-word-search-7 hover:bg-game-word-search-7',
		'bg-game-word-search-8 hover:bg-game-word-search-8',
		'bg-game-word-search-9 hover:bg-game-word-search-9',
		'bg-game-word-search-10 hover:bg-game-word-search-10',
		'bg-game-word-search-11 hover:bg-game-word-search-11',
		'bg-game-word-search-12 hover:bg-game-word-search-12',
		'bg-game-word-search-13 hover:bg-game-word-search-13',
		'bg-game-word-search-14 hover:bg-game-word-search-14',
		'bg-game-word-search-15 hover:bg-game-word-search-15',
		'bg-game-word-search-16 hover:bg-game-word-search-16',
		'bg-game-word-search-17 hover:bg-game-word-search-17',
	];

	const difficultyToLabel: Record<WordSearchDifficulty, string> = {
		easy: 'Easy',
		medium: 'Medium',
		hard: 'Hard',
	};

	const difficultyToScoreFactor: Record<WordSearchDifficulty, number> = {
		easy: 1,
		medium: 2,
		hard: 3,
	};

	let wordToButtonColor: Record<string, string> = $state({});

	let shouldShowAllWords = $state(false);
	let gameSectionElement = $state<HTMLElement>()!;
	let currentTouch: { rowIndex: number; columnIndex: number } | undefined;
	let wordToHint = $state('');
	let modeDialog = $state<HTMLDialogElement>()!;
	let isReady = $state(false);
	let isDifficultyDropdownOpen = $state(false);
	let isLanguageDropdownOpen = $state(false);
	let modeDialogGameMode: GameMode = $state('classic');
	let hintsUsed = $state(0);

	const context = new GameContext({
		gameKey: 'word-search',
		GameClass: WordSearchGame,
		settings: {
			defaultSettings: {
				difficulty: 'easy' as WordSearchDifficulty,
				language: 'en' as Language,
				mode: 'classic' as GameMode,
			},
		},
		sounds: {
			resources: wordSearchSoundResources,
			lifecycle: {
				createGame: wordSearchSoundResources.replay,
				win: wordSearchSoundResources.gameWin,
			},
		},
		defaultGameProps(context) {
			return {
				language: context.game?.language ?? context.settingsManager.settings.language,
				difficulty: context.game?.difficulty ?? context.settingsManager.settings.difficulty,
				mode: context.game?.mode ?? context.settingsManager.settings.mode,
				gameRect: gameSectionElement.getBoundingClientRect(),
				onReady,
				timer: context.timer,
			};
		},
		formatted(context) {
			const variant = `${capitalize(context.game?.mode)} - ${capitalize(context.game?.difficulty)}`;

			return {
				name: 'Word Search',
				variant,
				leaderboardVariant: variant,
			};
		},
		stats({ context, props }) {
			return {
				stats: new Stats({
					...props,
					gameVariant: `${context.game?.mode}-${context.game?.difficulty}`,
					liveStats: {
						foundWords: {
							name: 'Found words',
							unit: 'plain',
							value() {
								return context.game?.foundWords.length ?? 0;
							},
						},
						remainingWords: {
							name: 'Remaining Words',
							unit: 'plain',
							value() {
								return (context.game?.words?.length ?? 0) - (context.game?.foundWords?.length ?? 0);
							},
						},
					},
					initialPinnedStats: ['time', 'foundWords'],
				}),
				visibleStats: ['bestTime', 'averageTime', 'wonGames', 'totalGames'],
				canUpdateWithGameLost(game) {
					return game.foundWords.length > 0;
				},
			};
		},
		leaderboard(context) {
			return {
				leaderboard: new Leaderboard({
					game: context.gameKey,
					gameVariant: `${context.game?.mode}-${context.game?.difficulty}`,
					firstAvailableDate: new Date('2025/05/08'),
					order: 'lower-first',
				}),
				sendScoreOn: ['won'],
				getScore(game) {
					const score = Math.max(
						0,
						Math.floor(
							(hintsUsed * 100 + Math.floor(context.timer.elapsedTime / 100) - game.words.length) /
								difficultyToScoreFactor[game.difficulty],
						),
					);

					return {
						score,
					};
				},
			};
		},
		isGameReady() {
			return isReady;
		},
		onWillCreateGame({ context, newGameOptions }) {
			context.settingsManager.settings.difficulty = newGameOptions.difficulty;
			context.settingsManager.settings.language = newGameOptions.language;
			context.settingsManager.settings.mode = newGameOptions.mode;
			isDifficultyDropdownOpen = false;
			isLanguageDropdownOpen = false;
			isReady = false;
			colors = shuffle(colors);
			buttonsColors = shuffle(buttonsColors);
			shouldShowAllWords = false;
			modeDialogGameMode = newGameOptions.mode;
			hintsUsed = 0;
		},
	});

	let game = $derived(context.game);

	async function onGameOver() {
		context.handleGameOver('won');
	}

	async function onReady() {
		wordToButtonColor = {};
		game?.words.forEach((word, index) => {
			wordToButtonColor[word] = buttonsColors[index % buttonsColors.length];
		});
		isReady = true;
	}

	onMount(() => {
		context.load();
	});

	onDestroy(() => {
		context.dispose();
	});

	const handleStartSelection = (rowIndex: number, columnIndex: number) => {
		if (isNaN(rowIndex) || isNaN(columnIndex) || !isReady) {
			return;
		}

		context.sounds.move1.play();
		game?.startSelection(columnIndex, rowIndex);
		wordToHint = '';
		context.timer.start();
	};

	const handleContinueSelection = (rowIndex: number, columnIndex: number) => {
		if (isNaN(rowIndex) || isNaN(columnIndex) || !isReady) {
			return;
		}

		game?.continueSelection(columnIndex, rowIndex);
		wordToHint = '';
	};

	const handleEndSelection = (e: Event) => {
		e?.preventDefault();
		wordToHint = '';

		if (!isReady) {
			return;
		}

		if (game?.endSelection()) {
			context.sounds.move2.play();

			if (game.isGameWon()) {
				onGameOver();
			}
		}
	};

	/**
	 * Create new game if none was created due to
	 * the RotateScreen component
	 **/
	const handleResize = () => {
		if (!game) {
			context.createGame();
		}
	};

	const onMouseDown = (e: MouseEvent) => {
		const target = e.target as HTMLElement;
		const rowIndex = Number(target.dataset.rowIndex);
		const columnIndex = Number(target.dataset.columnIndex);
		handleStartSelection(rowIndex, columnIndex);
	};

	const onMouseMove = (e: MouseEvent) => {
		if (e.buttons !== 1 && e.type !== 'touchmove') return;
		const target = e.target as HTMLElement;
		const rowIndex = Number(target.dataset.rowIndex);
		const columnIndex = Number(target.dataset.columnIndex);
		handleContinueSelection(rowIndex, columnIndex);
	};

	const onTouchStart = (e: TouchEvent) => {
		const touch = e.touches[0];
		const target = touch.target as HTMLElement;
		const rowIndex = Number(target.dataset.rowIndex);
		const columnIndex = Number(target.dataset.columnIndex);
		currentTouch = { rowIndex, columnIndex };
		handleStartSelection(rowIndex, columnIndex);
	};

	const onTouchMove = (e: TouchEvent) => {
		if (!currentTouch) return;

		const x = e.touches[0].pageX;
		const y = e.touches[0].pageY;
		const element = document.elementFromPoint(x, y) as HTMLElement | null;

		if (element?.dataset.rowIndex !== undefined && element?.dataset.columnIndex !== undefined) {
			const rowIndex = Number(element.dataset.rowIndex);
			const columnIndex = Number(element.dataset.columnIndex);

			currentTouch = { rowIndex, columnIndex };
			handleContinueSelection(rowIndex, columnIndex);
		}
	};

	const getPathData = (positions: Point2D[], offset = 0.5): string => {
		if (positions.length === 1) {
			positions = [positions[0], positions[0]];
		}
		const commands: string[] = positions.map(({ x, y }, index) => {
			const command = index === 0 ? 'M' : 'L';

			return `${command}${x + offset} ${y + offset}`;
		});

		return commands.join(' ');
	};
</script>

<svelte:window
	onpointerup={handleEndSelection}
	onpointercancel={handleEndSelection}
	onresize={handleResize}
/>

<WordSearchInfoModal bind:isOpen={isInfoModalOpen} />

<GameLayout noPadding>
	{#snippet Island()}
		<GameIsland {context} />
	{/snippet}

	<dialog
		bind:this={modeDialog}
		class="modal modal-bottom
sm:modal-middle"
		onclose={() => {
			if (game) {
				modeDialogGameMode = game?.mode;
			}
		}}
	>
		<div class="modal-box">
			<form method="dialog">
				<button class="btn btn-sm btn-circle btn-ghost absolute right-4 top-4" aria-label="close">
					<CloseIcon />
				</button>
			</form>

			<h3 class="mb-4 text-lg font-bold">Game Mode</h3>

			<div class="grid w-full grid-cols-2 gap-2 sm:gap-4">
				<div class="indicator flex w-auto">
					<button
						aria-label="classic mode"
						class={cn(
							'cursor-pointer h-auto w-full flex flex-col items-center rounded-xl border-2 p-2 outline-hidden duration-300 transition-all sm:p-4',
							{
								'btn-outline border-primary': modeDialogGameMode === 'classic',
							},
						)}
						onclick={() => (modeDialogGameMode = 'classic')}
					>
						{#if modeDialogGameMode === 'classic'}
							<div
								transition:fly={{ y: -10, duration: 300, easing: quadInOut }}
								class="indicator-item badge badge-primary pointer-events-none p-0"
							>
								<CheckIcon class="size-5" />
							</div>
						{/if}

						<figure class="p-2 pb-0 sm:p-0">
							<ClassicMode selected={modeDialogGameMode === 'classic'} />
						</figure>
						<span class="mb-1 mt-2 text-lg font-semibold sm:text-xl">Classic</span>
						<span class="text-sm sm:text-sm">Vertical, horizontal, and diagonal lines</span>
					</button>
				</div>

				<div class="indicator flex w-auto">
					<button
						aria-label="Maze mode"
						class={cn(
							'cursor-pointer h-auto w-full flex flex-col items-center rounded-xl border-2 p-2 outline-hidden duration-300 transition-all sm:p-4',
							{
								'btn-outline border-primary': modeDialogGameMode === 'maze',
							},
						)}
						onclick={() => (modeDialogGameMode = 'maze')}
					>
						{#if modeDialogGameMode === 'maze'}
							<div
								transition:fly={{ y: -10, duration: 300, easing: quadInOut }}
								class="indicator-item badge badge-primary pointer-events-none p-0"
							>
								<CheckIcon class="size-5" />
							</div>
						{/if}

						<figure class="p-2 pb-0 sm:p-0">
							<MazeMode selected={modeDialogGameMode === 'maze'} />
						</figure>
						<span class="mb-1 mt-2 text-lg font-semibold sm:text-xl">Maze</span>
						<span class="text-sm sm:text-sm">Vertical and horizontal patterns</span>
					</button>
				</div>
			</div>

			<form method="dialog" class="modal-action">
				<button class="btn-ghost btn">Cancel</button>

				<button
					class="btn-primary btn"
					onclick={() => context.createGame({ mode: modeDialogGameMode })}
				>
					Save and play a new game
				</button>
			</form>
		</div>

		<form method="dialog" class="modal-backdrop">
			<button>close</button>
		</form>
	</dialog>

	<section
		class="max-h-screen-no-navbar flex-center size-full touch-pinch-zoom select-none p-4 pb-10 md:pb-4"
	>
		<div class="flex h-full max-w-2xl grow flex-col gap-2">
			<!-- Game top nav -->
			<div class="flex shrink-0 items-center gap-4">
				<div class="flex w-full grow items-center justify-between">
					<div class="flex items-center gap-2">
						<Dropdown bind:open={isDifficultyDropdownOpen}>
							<DropdownButton class="btn-sm">
								{difficultyToLabel[context.settingsManager.settings.difficulty]}
							</DropdownButton>

							<DropdownContent menu>
								{#each wordSearchDifficulties as difficulty}
									<DropdownItem>
										<button
											class:menu-active={context.settingsManager.settings.difficulty === difficulty}
											onclick={() => context.createGame({ difficulty })}
										>
											{difficultyToLabel[difficulty]}
										</button>
									</DropdownItem>
								{/each}
							</DropdownContent>
						</Dropdown>

						<Dropdown bind:open={isLanguageDropdownOpen}>
							<DropdownButton class="btn-sm whitespace-nowrap uppercase">
								{languageToFlag[context.settingsManager.settings.language]}&nbsp;
								{context.settingsManager.settings.language}
							</DropdownButton>

							<DropdownContent menu>
								{#each wordSearchLanguages as language}
									<DropdownItem>
										<button
											class="uppercase"
											class:menu-active={context.settingsManager.settings.language === language}
											onclick={() => context.createGame({ language })}
										>
											{languageToFlag[language]}&nbsp;
											{language}
										</button>
									</DropdownItem>
								{/each}
							</DropdownContent>
						</Dropdown>

						<button class="btn btn-sm" onclick={() => modeDialog.showModal()}>
							{context.settingsManager.settings.mode === 'maze' ? 'Maze' : 'Classic'}
						</button>

						<button class="btn btn-sm" onclick={() => (isInfoModalOpen = true)}>
							<InfoSolidIcon class="size-5" />
						</button>

						<MoreGamesButton class="btn-sm" />
					</div>
				</div>
			</div>

			<!-- Words -->
			{#key game?.id}
				{#if shouldShowAllWords}
					<Backdrop onclose={() => (shouldShowAllWords = false)} />
				{/if}

				<div
					class="flex h-[72px] shrink-0 items-start justify-center gap-2 md:h-[88px]"
					class:z-10={!shouldShowAllWords}
					class:z-30={shouldShowAllWords}
				>
					<div
						class="relative flex w-full grow basis-0 flex-wrap justify-center gap-2 overflow-hidden border border-y-base-300 bg-base-100 p-2 pr-10 transition-all md:pr-14"
						class:max-h-[72px]={!shouldShowAllWords}
						class:border-transparent={!shouldShowAllWords}
						class:md:max-h-[88px]={!shouldShowAllWords}
						class:rounded-lg={shouldShowAllWords}
						class:border-base-300={shouldShowAllWords}
						class:max-h-screen={shouldShowAllWords}
						class:shadow-lg={shouldShowAllWords}
						class:scale-105={shouldShowAllWords}
						class:translate-y-2={shouldShowAllWords}
					>
						<!-- Word preview -->
						<div class="pointer-events-none absolute inset-0 z-10">
							<div
								class="absolute inset-0 bg-base-100 transition-opacity"
								class:opacity-0={!game?.currentSelection.word}
								class:opacity-80={game?.currentSelection.word}
							></div>
							<span
								class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 whitespace-nowrap badge badge-outline badge-lg px-3 py-1 font-bold bg-base-100"
								class:opacity-0={!game?.currentSelection.word}
							>
								{game?.currentSelection.word}
							</span>
						</div>

						<button
							class="btn btn-xs absolute right-2 top-2 self-center transition-all md:btn-sm colorblind:btn-primary"
							aria-label={shouldShowAllWords ? 'Collapse words' : 'Show words'}
							onclick={() => {
								shouldShowAllWords = !shouldShowAllWords;
							}}
						>
							{shouldShowAllWords ? '-' : '+'}
						</button>

						{#if game && isReady}
							{#each game.sortedWords as word, _ (word)}
								{@const isFound = game.foundWords.some((foundWord) => foundWord.word === word)}
								<button
									animate:flip={{ duration: 400, easing: quintInOut }}
									class="btn-ghost btn no-animation btn-xs md:btn-sm dark:text-white {!isFound
										? wordToButtonColor[word]
										: ''}"
									class:line-through={isFound}
									onclick={() => {
										if (isFound) {
											return;
										}

										wordToHint = word;
										shouldShowAllWords = false;
										hintsUsed += 1;
									}}
								>
									{word}
								</button>
							{/each}
						{/if}
						{#if !isReady}
							<div class="skeleton w-20 h-8"></div>
							<div class="skeleton w-20 h-8"></div>
							<div class="skeleton w-20 h-8"></div>
						{/if}
					</div>
				</div>
			{/key}

			<!-- Game -->
			<!-- svelte-ignore a11y_no_static_element_interactions -->
			<div
				class="relative flex grow items-center justify-center lg:items-start"
				bind:this={gameSectionElement}
			>
				<div
					class="relative z-0 flex cursor-pointer flex-wrap"
					onmousedown={onMouseDown}
					onmousemove={onMouseMove}
					onmouseup={handleEndSelection}
					ontouchstart={onTouchStart}
					ontouchmove={onTouchMove}
					ontouchend={handleEndSelection}
				>
					{#if game && isReady}
						<svg
							class="pointer-events-none absolute inset-0"
							viewBox="0 0 {game.gridSize.columns} {game.gridSize.rows}"
						>
							{#each game.foundWords as foundWord, index}
								<path
									d={getPathData(foundWord.positions)}
									class="{colors[
										index % colors.length
									]} opacity-60 dark:opacity-40 colorblind:opacity-90 dark:colorblind:opacity-90"
									stroke="currentColor"
									stroke-width="0.8"
									stroke-linecap="round"
									stroke-linejoin="round"
									fill="none"
								/>
							{/each}

							{#if game.currentSelection.positions.length > 0}
								{@const currentColor = colors[game.foundWords.length % colors.length]}
								<path
									d={getPathData(game.currentSelection.positions)}
									class="{currentColor} opacity-60 dark:opacity-40 colorblind:opacity-90 dark:colorblind:opacity-90"
									stroke="currentColor"
									stroke-width="0.8"
									stroke-linecap="round"
									stroke-linejoin="round"
									fill="none"
								/>
							{/if}
						</svg>

						{#each game.grid ?? [] as row, rowIndex (`${rowIndex} - ${game.id ?? 1}`)}
							{#each row as letter, columnIndex (`${columnIndex} - ${letter}`)}
								{@const shouldHint =
									game &&
									wordToHint &&
									game.wordHints[wordToHint].startX === columnIndex &&
									game.wordHints[wordToHint].startY === rowIndex}
								<div
									class="flex-center z-10 flex grow select-none rounded-xl text-center transition aspect-square before:content-[attr(data-letter)]"
									style="width: {100 / (game.gridSize.columns ?? 6)}%;"
									class:text-2xl={game.difficulty === 'medium' || game.difficulty === 'hard'}
									class:md:text-3xl={game.difficulty === 'medium'}
									class:text-4xl={game.difficulty === 'easy'}
									class:bg-yellow-400={shouldHint}
									class:dark:bg-yellow-700={shouldHint}
									data-row-index={rowIndex}
									data-column-index={columnIndex}
									data-letter={letter}
								></div>
							{/each}
						{/each}
					{:else}
						{#each Array.from({ length: 6 }).fill(0) as _i}
							{#each Array.from({ length: 4 }).fill(0) as _i}
								<div
									class="items-center justify-center z-10 flex grow select-none rounded-xl text-center transition aspect-square before:content-[attr(data-letter)] before:invisible skeleton m-1"
									style="width: calc({100 / 6}% - 8px)"
									class:text-xs={context.settingsManager.settings.difficulty === 'medium' ||
										context.settingsManager.settings.difficulty === 'hard'}
									class:md:text-3xl={context.settingsManager.settings.difficulty === 'medium'}
									class:text-4xl={context.settingsManager.settings.difficulty === 'easy'}
									data-letter="A"
								></div>
							{/each}
						{/each}
					{/if}
				</div>
			</div>
		</div>
	</section>
</GameLayout>
