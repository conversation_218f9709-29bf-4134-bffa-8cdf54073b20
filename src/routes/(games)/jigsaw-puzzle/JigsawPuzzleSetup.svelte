<script lang="ts">
	import { onDestroy, onMount, untrack } from 'svelte';
	import { ghibliDarkWallpapers, ghibliLightWallpapers } from '$lib/data/wallpapers';
	import type { Wallpaper } from '$lib/data/wallpapers';
	import { cn } from '$lib/util/cn';
	import { fade, fly } from 'svelte/transition';
	import { wait } from '$lib/functions/wait';
	import ChevronLeftIcon from '$lib/components/Icons/ChevronLeftIcon.svelte';
	import JigsawFloatingBar from './JigsawFloatingBar.svelte';
	import PlayIcon from '$lib/components/Icons/PlayIcon.svelte';
	import WallpaperAttributionLinks from '../../[[tab]]/Chill/WallpaperAttributionLinks.svelte';
	import { gridSizeByPieceAmount, piecesAmount, type PieceAmount } from './game/JigsawPuzzleGame';

	interface Props {
		pieceAmount: PieceAmount;
		onStart: (image: Wallpaper) => void;
	}

	let { pieceAmount = $bindable(), onStart }: Props = $props();

	type JigsawImages = {
		images: Wallpaper[];
		category: string;
	};

	let container = $state<HTMLDivElement>();

	const jigsawImages: JigsawImages[] = [
		// {
		// 	category: 'ghibli',
		// 	images: [...ghibliLightWallpapers, ...ghibliDarkWallpapers],
		// },
		{
			category: 'Ghibli light',
			images: ghibliLightWallpapers,
		},
		{
			category: 'Ghibli dark',
			images: ghibliDarkWallpapers,
		},
		{
			category: 'Ghibli light 2',
			images: ghibliLightWallpapers,
		},
		{
			category: 'Ghibli dark 2',
			images: ghibliDarkWallpapers,
		},
		{
			category: 'Ghibli light 3',
			images: ghibliLightWallpapers,
		},
		{
			category: 'Ghibli dark 3',
			images: ghibliDarkWallpapers,
		},
		{
			category: 'Ghibli light 4',
			images: ghibliLightWallpapers,
		},
		{
			category: 'Ghibli dark 4',
			images: ghibliDarkWallpapers,
		},
		{
			category: 'Ghibli light 5',
			images: ghibliLightWallpapers,
		},
		{
			category: 'Ghibli dark 5',
			images: ghibliDarkWallpapers,
		},
	];

	let selectedCategory = $state<string>();
	let selectedImage = $state<Wallpaper>();
	let alreadyLoadedCategories: Record<string, boolean> = $state({});
	let loadedImages: Record<string, boolean> = $state({});

	let JigsawPuzzlePreview = $state<typeof import('./JigsawPuzzlePreview.svelte').default>();

	onMount(async () => {
		JigsawPuzzlePreview = (await import('./JigsawPuzzlePreview.svelte')).default;
	});

	onDestroy(() => {});

	const rotationByIndex: Record<number, number> = {
		0: 0,
		1: 1.8,
		2: -1.8,
	};

	async function selectCategory(category: string) {
		if (selectedCategory) {
			return;
		}

		container?.scrollTo({
			behavior: 'smooth',
			left: 0,
			top: 0,
		});

		selectedCategory = category;
		alreadyLoadedCategories[category] = true;

		if (rows < debouncedRows) {
			await wait(400);
		}

		debouncedRows = rows;
	}

	async function unselectCategory() {
		container?.scrollTo({
			behavior: 'smooth',
			left: 0,
			top: 0,
		});

		selectedCategory = undefined;

		if (rows < debouncedRows) {
			await wait(600);
		}

		debouncedRows = rows;
	}

	let canShowPreview = $state(false);
	let selectedImageOffset = $state({ x: 0, y: 0 });

	async function selectImage(image: Wallpaper, imageElement: HTMLImageElement) {
		container!.style.overflow = 'hidden';
		selectedImage = image;

		// Distance between image and viewport center
		const rect = imageElement.getBoundingClientRect();
		const dx = rect.left + rect.width / 2 - window.innerWidth / 2;
		const dy = rect.top + rect.height / 2 - window.innerHeight / 2;

		selectedImageOffset = {
			x: -dx,
			y: -dy,
		};

		await wait(500);

		if (selectedImage.url === image.url) {
			canShowPreview = true;
		}
	}

	async function unselectImage() {
		selectedImage = undefined;
		container!.style.overflow = 'auto';
		canShowPreview = false;
	}

	function selectAmount(amount: PieceAmount) {
		pieceAmount = amount;
	}

	let selectedJigsawImages = $derived(
		jigsawImages.find((img) => img.category === selectedCategory),
	);

	let cols = $state(3);
	let rows = $derived.by(() => {
		if (selectedJigsawImages) {
			return Math.ceil(selectedJigsawImages.images.length / cols);
		}

		return Math.ceil(jigsawImages.length / cols);
	});
	let debouncedRows = $state(untrack(() => rows));

	const zIndexes = {
		category: 99999,
		selectedImage: 999999,
	};
</script>

<div class="flex flex-col size-full max-h-screen overflow-scroll pt-24" bind:this={container}>
	<div>
		{#if selectedImage?.attribution}
			<div
				in:fly={{ y: 20, duration: 300, delay: 300 }}
				out:fly={{ y: 20, duration: 300 }}
				class="absolute bottom-24 left-1/2 -translate-x-1/2 z-10"
			>
				<WallpaperAttributionLinks class="mb-2" attribution={selectedImage.attribution} />
			</div>
		{/if}

		{#if selectedImage}
			<JigsawFloatingBar>
				<button
					class="btn btn-ghost btn-circle"
					onclick={() => {
						unselectImage();
					}}
				>
					<ChevronLeftIcon class="size-6" />
				</button>

				{#each piecesAmount as amount}
					<button
						class={cn('btn btn-circle', {
							'btn-secondary': pieceAmount === amount,
							'btn-ghost': pieceAmount !== amount,
							'hidden md:block': amount > 200,
						})}
						onclick={() => {
							selectAmount(amount);
						}}
					>
						{amount}
					</button>
				{/each}

				<button
					class="btn btn-circle btn-primary"
					onclick={() => {
						if (selectedImage) {
							onStart(selectedImage);
						}
					}}
				>
					<PlayIcon variant="play" class="size-6" />
				</button>
			</JigsawFloatingBar>
		{:else if selectedCategory}
			<JigsawFloatingBar>
				<button
					class="btn btn-ghost btn-circle"
					onclick={() => {
						unselectCategory();
					}}
				>
					<ChevronLeftIcon class="size-6" />
				</button>
				Pick an image
			</JigsawFloatingBar>
		{:else}
			<JigsawFloatingBar>Pick a category</JigsawFloatingBar>
		{/if}
	</div>

	<div class="grow px-4">
		<div class="relative z-0">
			<!-- Placeholders to fix layout shift and bottom padding -->
			<div class="flex flex-col gap-4 invisible">
				{#each { length: debouncedRows + 1 }}
					<div
						class="aspect-video last-of-type:h-32"
						style="width: calc(100% / 3 - {16 * (3 - 2.325)}px);"
					></div>
				{/each}
			</div>

			{#each jigsawImages as { images, category }, categoryIndex}
				{#each images as image, imageIndex (`${category}-${image.url}`)}
					{@const isCategorySelected = selectedCategory === category}
					{@const index = isCategorySelected ? imageIndex : categoryIndex}
					{@const rotation = isCategorySelected ? 0 : (rotationByIndex[imageIndex] ?? 0)}
					{@const isImageSelected = isCategorySelected && selectedImage?.url === image.url}

					<button
						class={cn('absolute top-0 left-0 duration-500 cursor-pointer aspect-video', {
							'opacity-0 pointer-events-none':
								(selectedCategory && selectedCategory !== category) ||
								(selectedImage && selectedImage.url !== image.url),
							'cursor-default': selectedImage,
						})}
						disabled={!!selectedImage}
						style={`
									transform: translate(
										calc(${100 * (index % 3)}% + ${16 * (index % 3)}px + ${isImageSelected ? selectedImageOffset.x : 0}px),
										calc(${100 * Math.floor(index / 3)}% + ${16 * Math.floor(index / 3)}px + ${isImageSelected ? selectedImageOffset.y : 0}px)
										) rotate(${rotation}deg) scale(${isImageSelected ? 2.5 : 1});
									width: calc(100% / 3 - ${16 * (3 - 2.325)}px);
									z-index: ${isImageSelected ? zIndexes.selectedImage : (category === selectedCategory ? zIndexes.category : 0) + images.length - imageIndex};
								`}
						onclick={(event) => {
							if (selectedCategory) {
								selectImage(image, event.target as HTMLImageElement);
							} else {
								selectCategory(category);
							}
						}}
						aria-label={selectedCategory
							? `Select ${image.attribution?.work.name} image`
							: `Select ${category} category`}
					>
						{#if isCategorySelected || imageIndex < 3 || alreadyLoadedCategories[category]}
							<img
								class={cn('bg-base-content aspect-video transition-all duration-500', {
									'[clip-path:inset(0%_0%_0%_0%_round_var(--radius-box,_1rem))]': !isImageSelected,
									'[clip-path:inset(0%_0%_0%_0%_round_0))]': isImageSelected,
								})}
								src={image.url}
								alt={image.url}
								loading="lazy"
								onload={() => {
									loadedImages[image.url] = true;
								}}
							/>

							{#if !loadedImages[image.url]}
								<div transition:fade class="bg-base-300 absolute inset-0"></div>
							{/if}

							{#if isImageSelected && JigsawPuzzlePreview && canShowPreview}
								{@const { rows, columns: cols } = gridSizeByPieceAmount[pieceAmount]}
								<div class="absolute inset-0 bg-base-100/80" transition:fade={{ duration: 400 }}>
									<JigsawPuzzlePreview src={image.url} {rows} {cols} noOutTransition />
								</div>
							{/if}
						{/if}

						{#if imageIndex === 0 && !selectedCategory}
							<div
								class="absolute bottom-0 left-1/2 -translate-x-1/2 translate-y-1/5 flex items-center rounded-full px-4 py-2 z-50 glass text-nowrap"
								in:fly={{ y: -20, duration: 300, delay: 400 + 40 * categoryIndex }}
								out:fly={{ y: -20, duration: 300 }}
							>
								<span class=" text-lg font-medium">
									{category}
								</span>
							</div>
						{/if}
					</button>
				{/each}
			{/each}
		</div>
	</div>
</div>
