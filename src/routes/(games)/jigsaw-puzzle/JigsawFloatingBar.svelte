<script lang="ts">
	import { cn } from '$lib/util/cn';
	import type { Snippet } from 'svelte';
	import { fly } from 'svelte/transition';

	interface Props {
		children: Snippet;
		class?: string;
	}

	let { children, class: className }: Props = $props();
</script>

<div
	class={cn(
		'glass rounded-full px-6 py-3 gap-3 flex flex-row items-center h-16 absolute bottom-8 left-1/2 -translate-x-1/2 z-10 text-nowrap',
		className,
	)}
	in:fly={{ y: 20, duration: 300, delay: 300 }}
	out:fly={{ y: 20, duration: 300 }}
>
	{@render children()}
</div>
