<script lang="ts">
	import GameIsland from '$lib/components/GameIsland/GameIsland.svelte';
	import { GameContext } from '$lib/util/GameContext/GameContext.svelte';
	import { onDestroy, onMount } from 'svelte';
	import {
		gridSizeByPieceAmount,
		JigsawPuzzleGame,
		piecesAmount,
	} from './game/JigsawPuzzleGame.svelte';
	import JigsawPuzzleSetup from './JigsawPuzzleSetup.svelte';
	import GameLayout from '$lib/components/GameLayout/GameLayout.svelte';
	import type { PieceAmount } from './game/JigsawPuzzleGame.svelte';
	import { Stats } from '$lib/util/Stats.svelte';
	import type { Wallpaper } from '$lib/data/wallpapers';
	import { wait } from '$lib/functions/wait';
	import { theme } from '$lib/stores/theme.svelte';
	import { type Size } from '$lib/models/Size';

	let image = $state<Wallpaper>();
	let seed = $state(0);
	let container = $state<Size>({ width: 0, height: 0 });
	let gameIsland: GameIsland<any, any, any> | null = $state(null);
	let gameContainer: HTMLDivElement | null = $state(null);

	type Step = 'setup' | 'game';
	let step = $state<Step>('setup');

	type TransitionState = 'none' | 'in' | 'out';
	let transitionState = $state<TransitionState>('none');
	const transitionDuration = 1300;
	const transitionOutDelay = 500;

	const context = new GameContext({
		GameClass: JigsawPuzzleGame,
		gameKey: 'jigsaw-puzzle',
		settings: {
			defaultSettings: {
				pieces: piecesAmount[1] as PieceAmount,
			},
		},
		defaultGameProps(context) {
			return {
				seed: 0,
				image: { url: '' },
				container: { width: 0, height: 0 },
				size: gridSizeByPieceAmount[context.settingsManager.settings.pieces],
				timer: context.timer,
				target: gameContainer || document.body,
			};
		},
		formatted(context) {
			return {
				name: 'Jigsaw Puzzle',
			};
		},
		stats({ props, context }) {
			return {
				stats: new Stats({
					...props,
					gameVariant: 'default',
					liveStats: {},
					initialPinnedStats: ['time'],
				}),
				canUpdateWithGameLost: () => false,
				visibleStats: [],
			};
		},
		onDispose(context) {
			context.game?.dispose();
		},
		onWillCreateGame({ previousGame }) {
			previousGame?.dispose();
		},
	});

	async function onStart(startProps: { image: Wallpaper; seed: number; container: Size }) {
		image = startProps.image;
		seed = startProps.seed;
		container = startProps.container;

		transitionState = 'in';

		await wait(transitionDuration);

		context.createGame({
			size: gridSizeByPieceAmount[context.settingsManager.settings.pieces],
			image,
			seed,
			container,
		});

		step = 'game';

		await wait(transitionOutDelay);

		transitionState = 'out';

		await wait(transitionDuration);

		transitionState = 'none';

		context.timer.start();
	}

	async function onLeaveGame() {
		context.handleGameOver('lost');
		context.resetGameState();
		gameIsland?.changeVariant('live-stats');

		transitionState = 'in';

		await wait(transitionDuration);

		image = undefined;
		seed = 0;
		step = 'setup';

		await wait(transitionOutDelay);

		transitionState = 'out';

		await wait(transitionDuration);

		transitionState = 'none';
	}

	let JigsawPuzzlePreview = $state<typeof import('./JigsawPuzzlePreview.svelte').default>();
	let JigsawPuzzleGameRenderer =
		$state<typeof import('./JigsawPuzzleGameRenderer.svelte').default>();

	onMount(async () => {
		context.load();

		JigsawPuzzlePreview = (await import('./JigsawPuzzlePreview.svelte')).default;
		JigsawPuzzleGameRenderer = (await import('./JigsawPuzzleGameRenderer.svelte')).default;
	});

	onDestroy(() => {
		context.dispose();
	});
</script>

<GameLayout noPadding navbarStyle="on-top" mobileOrientation="landscape">
	{#snippet Island()}
		<GameIsland
			{context}
			bind:this={gameIsland}
			onLeaveGame={(url) => {
				if (!url) {
					// User left by interacting with the back button, change step to setup
					onLeaveGame();
				}
			}}
		/>
	{/snippet}

	<div class="size-full" bind:this={gameContainer}>
		{#if step === 'setup'}
			<JigsawPuzzleSetup bind:pieceAmount={context.settingsManager.settings.pieces} {onStart} />
		{:else if step === 'game' && context.game}
			<JigsawPuzzleGameRenderer
				game={context.game}
				onBack={() => {
					gameIsland?.changeVariant('prevent-leave');
				}}
			/>
		{/if}

		{#if JigsawPuzzlePreview && transitionState === 'in'}
			<JigsawPuzzlePreview
				objectFit="cover"
				rows={gridSizeByPieceAmount['240'].rows}
				cols={gridSizeByPieceAmount['240'].columns}
				src={theme.brightness === 'light' ? '/share-2-light.avif' : '/share-2.avif'}
				class="absolute inset-0 z-10"
				{transitionDuration}
			/>
		{/if}
	</div>
</GameLayout>

<!-- Prefetch loading image -->
{#if theme.loaded}
	<!-- svelte-ignore a11y_missing_attribute -->
	<img
		src={theme.brightness === 'light' ? '/share-2-light.avif' : '/share-2.avif'}
		class="hidden"
		loading="eager"
		fetchpriority="auto"
	/>
{/if}
