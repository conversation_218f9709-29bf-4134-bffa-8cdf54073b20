<script lang="ts">
	import GameIsland from '$lib/components/GameIsland/GameIsland.svelte';
	import { GameContext } from '$lib/util/GameContext/GameContext.svelte';
	import { onDestroy, onMount } from 'svelte';
	import { gridSizeByPieceAmount, JigsawPuzzleGame, piecesAmount } from './game/JigsawPuzzleGame';
	import JigsawPuzzleSetup from './JigsawPuzzleSetup.svelte';
	import GameLayout from '$lib/components/GameLayout/GameLayout.svelte';
	import type { PieceAmount } from './game/JigsawPuzzleGame';
	import { Stats } from '$lib/util/Stats.svelte';
	import type { Wallpaper } from '$lib/data/wallpapers';
	import { wait } from '$lib/functions/wait';
	import { theme } from '$lib/stores/theme.svelte';

	let image = $state<Wallpaper>();

	type Step = 'setup' | 'game';
	let step = $state<Step>('setup');

	type TransitionState = 'none' | 'in' | 'out';
	let transitionState = $state<TransitionState>('none');
	const transitionDuration = 1500;

	const context = new GameContext({
		GameClass: JigsawPuzzleGame,
		gameKey: 'jigsaw-puzzle',
		settings: {
			defaultSettings: {
				pieces: piecesAmount[1] as PieceAmount,
			},
		},
		defaultGameProps() {
			return {};
		},
		formatted(context) {
			return {
				name: 'Jigsaw Puzzle',
			};
		},
		stats({ props, context }) {
			return {
				stats: new Stats({
					...props,
					gameVariant: 'default',
					liveStats: {},
					initialPinnedStats: ['time'],
				}),
				canUpdateWithGameLost: () => false,
				visibleStats: [],
			};
		},
	});

	async function onStart(selectedImage: Wallpaper) {
		image = selectedImage;

		transitionState = 'in';

		await wait(transitionDuration / 2);

		step = 'game';
		transitionState = 'out';

		// todo: start game

		await wait(transitionDuration / 2);

		transitionState = 'none';
	}

	let JigsawPuzzlePreview = $state<typeof import('./JigsawPuzzlePreview.svelte').default>();

	onMount(async () => {
		context.load();

		JigsawPuzzlePreview = (await import('./JigsawPuzzlePreview.svelte')).default;
	});

	onDestroy(() => {
		context.dispose();
	});
</script>

<GameLayout noPadding navbarStyle="on-top" mobileOrientation="landscape">
	{#snippet Island()}
		<GameIsland {context} />
	{/snippet}

	{#if step === 'setup'}
		<JigsawPuzzleSetup bind:pieceAmount={context.settingsManager.settings.pieces} {onStart} />
	{:else if step === 'game'}
		<!-- Game -->
	{/if}

	{#if JigsawPuzzlePreview && transitionState !== 'none'}
		<JigsawPuzzlePreview
			rows={gridSizeByPieceAmount[piecesAmount[2]].rows}
			cols={gridSizeByPieceAmount[piecesAmount[2]].columns}
			src={theme.brightness === 'light' ? '/share-light.png' : '/share.png'}
			class="absolute inset-0 z-10"
		/>
	{/if}
</GameLayout>
