import type { Wallpaper } from '$lib/data/wallpapers';
import type { GridSize } from '$lib/models/GridSize';
import { untrack } from 'svelte';
import { getImageDimensions, type ImageDimensions } from '../util/getImageDimensions';
import type { Size } from '$lib/models/Size';
import { Camera2D } from '../Camera2D.svelte';
import type { Timer } from '$lib/util/Timer.svelte';

export const piecesAmount = [48, 96, 144, 240] as const;
export type PieceAmount = (typeof piecesAmount)[number];

export const gridSizeByPieceAmount: Record<PieceAmount, GridSize> = {
	48: {
		rows: 6,
		columns: 8,
	},
	96: {
		rows: 8,
		columns: 12,
	},
	144: {
		rows: 9,
		columns: 16,
	},
	240: {
		rows: 12,
		columns: 20,
	},
};

export interface JigsawPuzzleGameProps {
	size: GridSize;
	image: Wallpaper;
	container: Size;
	seed: number;
	timer: Timer;
	target: HTMLElement;
}

export class JigsawPuzzleGame {
	size: GridSize;
	image: Wallpaper;
	seed: number;
	container = $state() as Size;
	camera: Camera2D;
	timer = $state() as Timer;

	private _imageDimensions = $state<ImageDimensions | null>(null);
	private _imageElement = $state<HTMLImageElement | null>(null);
	private _abortController = new AbortController();
	private _cleanupEffect: () => void;

	constructor({ size, image, container, seed, timer, target }: JigsawPuzzleGameProps) {
		this.size = size;
		this.image = image;
		this.seed = seed;
		this.container = container;
		this.timer = timer;
		this.camera = new Camera2D({
			target,
			enabled: false,
		});

		// Set up zoom change callback to update boundaries
		this.camera.onZoomChange = () => {
			this.updateCameraBoundary();
		};

		if (image.url) {
			const img = document.createElement('img');

			img.onload = () => {
				untrack(() => {
					this._imageDimensions = getImageDimensions({
						image: img,
						container: this.container,
					});
					this._imageElement = img;

					if (this._imageDimensions) {
						console.log(this._imageDimensions);
						this.updateCameraBoundary();
					}
					this.camera.pos(this.centerX, this.centerY);
				});
			};

			img.src = image.url;
		}

		this._cleanupEffect = $effect.root(() => {
			$effect(() => {
				this.camera.enabled = this.enabled;
			});
		});

		this.addListeners();
	}

	get enabled() {
		return this.timer.running;
	}

	get centerX() {
		return ((1 - this.camera.zoom) * this.container.width) / 2;
	}

	get centerY() {
		return ((1 - this.camera.zoom) * this.container.height) / 2;
	}

	get imageElement() {
		return this._imageElement;
	}

	get imageDimensions() {
		return this._imageDimensions;
	}

	private updateCameraBoundary() {
		if (!this._imageDimensions) return;

		// Calculate the boundary to ensure at least half the image is always visible
		// The camera position represents the top-left corner of the viewport

		// Account for current zoom level - the effective viewport size changes with zoom
		const currentZoom = this.camera.zoom;
		const effectiveViewportWidth = this.container.width / currentZoom;
		const effectiveViewportHeight = this.container.height / currentZoom;

		// For X axis: camera can move from showing right half to showing left half
		// minX: when camera is at this position, the right edge of viewport shows the center of image
		// maxX: when camera is at this position, the left edge of viewport shows the center of image
		const halfImageWidth = this._imageDimensions.width / 2;
		const halfImageHeight = this._imageDimensions.height / 2;

		// Calculate boundary based on image position and effective viewport size
		const imageLeft = this._imageDimensions.x;
		const imageTop = this._imageDimensions.y;
		const imageCenterX = imageLeft + halfImageWidth;
		const imageCenterY = imageTop + halfImageHeight;

		// Camera boundary calculation with zoom-adjusted viewport:
		// minX: rightmost camera position (shows left half of image)
		const minX = imageCenterX - effectiveViewportWidth;
		// maxX: leftmost camera position (shows right half of image)
		const maxX = imageCenterX;

		// minY: bottommost camera position (shows top half of image)
		const minY = imageCenterY - effectiveViewportHeight;
		// maxY: topmost camera position (shows bottom half of image)
		const maxY = imageCenterY;

		this.camera.boundary = {
			x: minX,
			y: minY,
			width: maxX - minX,
			height: maxY - minY,
		};
	}

	private handleResize() {
		// Update camera boundary when container size changes
		this.updateCameraBoundary();
	}

	private addListeners() {
		window.addEventListener('resize', () => this.handleResize, {
			signal: this._abortController.signal,
		});
	}

	dispose() {
		this._cleanupEffect();
		this._abortController.abort();
		this.camera.dispose();
	}
}
