<script lang="ts">
	import { cn } from '$lib/util/cn';
	import { Grid } from '$lib/util/Grid.svelte';
	import Konva from 'konva';
	import type { Context, SceneContext } from 'konva/lib/Context';
	import type { Node, NodeConfig } from 'konva/lib/Node';
	import { onMount, tick, untrack } from 'svelte';
	import { Group, Image, Layer, Stage, Shape } from 'svelte-konva';
	import { type TransitionConfig } from 'svelte/transition';

	interface Props {
		src: string;
		rows: number;
		cols: number;
		class?: string;
		transitionDuration?: number;
		noOutTransition?: boolean;
		objectFit?: 'cover' | 'contain';
		seed?: number;
	}

	let {
		src,
		rows: propsRows,
		cols: propsCols,
		class: className,
		noOutTransition,
		transitionDuration = 600,
		objectFit = 'contain',
		seed = $bindable(0),
	}: Props = $props();
	let image = $state<HTMLImageElement>();
	let container = $state<HTMLDivElement>();
	let containerSize = $state({ width: 0, height: 0 });
	let stage = $state<Konva.Stage>();
	let rows = $state(untrack(() => propsRows));
	let cols = $state(untrack(() => propsCols));

	$effect(() => {
		// Track rows and columns change
		rows;
		cols;

		untrack(() => {
			seed = Math.random() * 100;
		});
	});

	onMount(() => {
		onResize();
	});

	$effect(() => {
		if (src) {
			const img = document.createElement('img');
			img.src = src;
			img.onload = () => {
				untrack(() => {
					image = img;
					imageDimensions = getDimensions();
				});
			};
		}
	});

	let animationStarted = $state(false);
	let piecesGrid = new Grid<Node<NodeConfig> | undefined>({
		rows: 0,
		columns: 0,
		reactive: false,
	});

	function storeAllPieces() {
		piecesGrid = new Grid<Node<NodeConfig> | undefined>(
			{
				rows,
				columns: cols,
				reactive: false,
			},
			(row, col) => {
				return stage!.findOne(`#${row}-${col}`);
			},
		);
	}

	function getStaggeredAnimation({
		fadeIn,
		onComplete,
	}: {
		fadeIn: boolean;
		onComplete?: () => void;
	}) {
		const distance = piecesGrid.rows + piecesGrid.columns - 1;
		const baseDelay = Math.max(0, transitionDuration / distance);

		const animation = new Konva.Animation((frame) => {
			for (let row = 0; row < piecesGrid.rows; row++) {
				for (let col = 0; col < piecesGrid.columns; col++) {
					// Calculate distance from top-left (0,0)
					const distance = row + col;

					// Delay based on distance from top-left
					const delay = distance * baseDelay;
					let progress = frame!.time - delay;

					if (progress < 0) {
						continue;
					}

					if (progress > 1) {
						progress = 1;
					}

					const piece = piecesGrid.grid[row][col];

					const visible = (fadeIn ? progress : 1 - progress) === 1;

					piece?.visible(visible);

					if (progress === 1 && row === piecesGrid.rows - 1 && col === piecesGrid.columns - 1) {
						animation.stop();
						onComplete?.();
					}
				}
			}
		});

		return animation;
	}

	let fadeInAnimation: ReturnType<typeof getStaggeredAnimation> | null = $state(null);
	let fadeOutAnimation: ReturnType<typeof getStaggeredAnimation> | null = $state(null);
	let isFirstAnimation = $state(true);

	$effect(() => {
		if (
			(isFirstAnimation || (propsRows !== rows && propsCols !== cols)) &&
			image &&
			!fadeInAnimation &&
			!fadeOutAnimation
		) {
			untrack(() => {
				startRevealAnimation();
			});
		}
	});

	async function startRevealAnimation() {
		animationStarted = true;

		if (fadeInAnimation) {
			fadeInAnimation.stop();
		}

		if (fadeOutAnimation) {
			fadeOutAnimation.stop();
		}

		if (isFirstAnimation) {
			storeAllPieces();
			fadeInAnimation = getStaggeredAnimation({
				fadeIn: true,
				onComplete() {
					fadeInAnimation = null;
					isFirstAnimation = false;
				},
			});
			fadeInAnimation.start();
			return;
		}

		fadeOutAnimation = getStaggeredAnimation({
			fadeIn: false,
			async onComplete() {
				fadeOutAnimation = null;
				rows = propsRows;
				cols = propsCols;

				await tick();

				storeAllPieces();

				fadeInAnimation = getStaggeredAnimation({
					fadeIn: true,
					onComplete() {
						fadeInAnimation = null;
						isFirstAnimation = false;
					},
				});
				fadeInAnimation.start();
			},
		});

		fadeOutAnimation.start();
	}

	// Function to draw a jigsaw piece shape
	function drawJigsawPiece(
		ctx: SceneContext | Context,
		x: number,
		y: number,
		width: number,
		height: number,
		row: number,
		col: number,
		rows: number,
		cols: number,
		seed: number = 0,
	) {
		// Predefined curve coordinates for smooth jigsaw piece edges (less curvy, bigger insets)
		const curvyCoords = [
			0, 0, 35, 8, 37, 3, 37, 3, 40, 0, 38, -3, 38, -3, 20, -20, 50, -20, 50, -20, 80, -20, 62, -3,
			62, -3, 60, 0, 63, 3, 63, 3, 65, 8, 100, 0,
		];

		// Calculate tile ratio based on piece dimensions
		const tileRatio = Math.min(width, height) / 100;

		// Determine tab directions for each edge
		// Edge pieces have no tabs (0), internal pieces have tabs (1 or -1)
		// Each edge between pieces has a unique ID, and adjacent pieces reference the same edge

		// Horizontal edges: identified by the row of the edge and column
		const topEdgeId = row * cols + col + seed;
		const bottomEdgeId = (row + 1) * cols + col + seed;

		// Vertical edges: identified by row and the column of the edge
		const leftEdgeId = row * cols + col + 100000 + seed;
		const rightEdgeId = row * cols + (col + 1) + 100000 + seed;

		// Generate tab directions - adjacent pieces get opposite values for shared edges
		const topTab = row === 0 ? 0 : getTabDirection(topEdgeId);
		const bottomTab = row === rows - 1 ? 0 : -getTabDirection(bottomEdgeId);
		const leftTab = col === 0 ? 0 : getTabDirection(leftEdgeId);
		const rightTab = col === cols - 1 ? 0 : -getTabDirection(rightEdgeId);

		ctx.beginPath();

		// Start at top-left corner with offset only for internal edges
		const offsetX = col === 0 ? 0 : -4 * tileRatio;
		const offsetY = row === 0 ? 0 : 4 * tileRatio;
		const topLeftEdge = { x: x + offsetX, y: y + offsetY };
		ctx.moveTo(topLeftEdge.x, topLeftEdge.y);

		// Top edge - scale curve to fit width
		const widthRatio = width / 100;
		for (let i = 0; i < curvyCoords.length / 6; i++) {
			const p1x = topLeftEdge.x + curvyCoords[i * 6 + 0] * widthRatio;
			const p1y = topLeftEdge.y + topTab * curvyCoords[i * 6 + 1] * tileRatio;
			const p2x = topLeftEdge.x + curvyCoords[i * 6 + 2] * widthRatio;
			const p2y = topLeftEdge.y + topTab * curvyCoords[i * 6 + 3] * tileRatio;
			const p3x = topLeftEdge.x + curvyCoords[i * 6 + 4] * widthRatio;
			const p3y = topLeftEdge.y + topTab * curvyCoords[i * 6 + 5] * tileRatio;

			ctx.bezierCurveTo(p1x, p1y, p2x, p2y, p3x, p3y);
		}

		// Right edge - scale curve to fit height
		const topRightEdge = { x: topLeftEdge.x + width, y: topLeftEdge.y };
		const heightRatio = height / 100;
		for (let i = 0; i < curvyCoords.length / 6; i++) {
			const p1x = topRightEdge.x - rightTab * curvyCoords[i * 6 + 1] * tileRatio;
			const p1y = topRightEdge.y + curvyCoords[i * 6 + 0] * heightRatio;
			const p2x = topRightEdge.x - rightTab * curvyCoords[i * 6 + 3] * tileRatio;
			const p2y = topRightEdge.y + curvyCoords[i * 6 + 2] * heightRatio;
			const p3x = topRightEdge.x - rightTab * curvyCoords[i * 6 + 5] * tileRatio;
			const p3y = topRightEdge.y + curvyCoords[i * 6 + 4] * heightRatio;

			ctx.bezierCurveTo(p1x, p1y, p2x, p2y, p3x, p3y);
		}

		// Bottom edge - scale curve to fit width
		const bottomRightEdge = { x: topRightEdge.x, y: topRightEdge.y + height };
		for (let i = 0; i < curvyCoords.length / 6; i++) {
			const p1x = bottomRightEdge.x - curvyCoords[i * 6 + 0] * widthRatio;
			const p1y = bottomRightEdge.y - bottomTab * curvyCoords[i * 6 + 1] * tileRatio;
			const p2x = bottomRightEdge.x - curvyCoords[i * 6 + 2] * widthRatio;
			const p2y = bottomRightEdge.y - bottomTab * curvyCoords[i * 6 + 3] * tileRatio;
			const p3x = bottomRightEdge.x - curvyCoords[i * 6 + 4] * widthRatio;
			const p3y = bottomRightEdge.y - bottomTab * curvyCoords[i * 6 + 5] * tileRatio;

			ctx.bezierCurveTo(p1x, p1y, p2x, p2y, p3x, p3y);
		}

		// Left edge - scale curve to fit height
		const bottomLeftEdge = { x: bottomRightEdge.x - width, y: bottomRightEdge.y };
		for (let i = 0; i < curvyCoords.length / 6; i++) {
			const p1x = bottomLeftEdge.x + leftTab * curvyCoords[i * 6 + 1] * tileRatio;
			const p1y = bottomLeftEdge.y - curvyCoords[i * 6 + 0] * heightRatio;
			const p2x = bottomLeftEdge.x + leftTab * curvyCoords[i * 6 + 3] * tileRatio;
			const p2y = bottomLeftEdge.y - curvyCoords[i * 6 + 2] * heightRatio;
			const p3x = bottomLeftEdge.x + leftTab * curvyCoords[i * 6 + 5] * tileRatio;
			const p3y = bottomLeftEdge.y - curvyCoords[i * 6 + 4] * heightRatio;

			ctx.bezierCurveTo(p1x, p1y, p2x, p2y, p3x, p3y);
		}

		ctx.closePath();
	}

	// Helper function to determine tab direction for consistent adjacent pieces
	function getTabDirection(seed: number): number {
		// Use a simple pseudo-random function based on the seed
		// This ensures the same seed always produces the same result
		const random = Math.sin(seed * 12.9898) * 43758.5453;
		return random - Math.floor(random) > 0.5 ? 1 : -1;
	}

	// Fit image like css background-contain or background-cover
	function getDimensions() {
		if (!image || !container) return { x: 0, y: 0, width: 0, height: 0 };

		const containerWidth = container.clientWidth;
		const containerHeight = container.clientHeight;
		const imageWidth = image.width;
		const imageHeight = image.height;

		// Calculate the scaling ratios
		const scaleX = containerWidth / imageWidth;
		const scaleY = containerHeight / imageHeight;

		// Use the appropriate scale based on objectFit prop
		const scale =
			objectFit === 'cover'
				? Math.max(scaleX, scaleY) // cover: image covers entire container, may be cropped
				: Math.min(scaleX, scaleY); // contain: entire image fits within container

		// Calculate the new dimensions
		const newWidth = imageWidth * scale;
		const newHeight = imageHeight * scale;

		// Calculate position to center the image
		const x = (containerWidth - newWidth) / 2;
		const y = (containerHeight - newHeight) / 2;

		return {
			x,
			y,
			width: newWidth,
			height: newHeight,
		};
	}

	let imageDimensions = $state(getDimensions());

	// Update dimensions when image or container changes
	$effect(() => {
		if (image && container && containerSize.width > 0 && containerSize.height > 0) {
			imageDimensions = getDimensions();
		}
	});

	function onResize() {
		containerSize = { width: container?.clientWidth ?? 0, height: container?.clientHeight ?? 0 };
		imageDimensions = getDimensions();
	}

	function outTransition(node: HTMLElement): TransitionConfig {
		if (noOutTransition) {
			return {
				duration: 0,
				tick(t, u) {
					return;
				},
			};
		}

		return {
			duration: transitionDuration,
			tick(t, u) {
				if (fadeOutAnimation) {
					return;
				}

				fadeOutAnimation = getStaggeredAnimation({ fadeIn: false });
				fadeOutAnimation.start();
			},
		};
	}
</script>

<svelte:window onresize={onResize} />

<div class={cn('size-full', className)} bind:this={container} out:outTransition>
	{#if container}
		<Stage config={containerSize} bind:handle={stage}>
			<Layer
				config={{
					opacity: animationStarted ? 1 : 0,
					listening: false,
				}}
			>
				{#each { length: rows } as _, row}
					{#each { length: cols } as _, col}
						<!-- Outer group: container for both inner group and shape outline -->
						<Group
							config={{
								x: imageDimensions.x + (imageDimensions.width / cols) * col,
								y: imageDimensions.y + (imageDimensions.height / rows) * row,
								width: imageDimensions.width / cols,
								height: imageDimensions.height / rows,
								id: `${row}-${col}`,
								visible: false,
								listening: false,
							}}
						>
							<!-- Inner group: contains only the clipped image -->
							<Group
								config={{
									width: imageDimensions.width / cols,
									height: imageDimensions.height / rows,
									listening: false,
									clipFunc(ctx) {
										const x = 0;
										const y = 0;
										const width = imageDimensions.width / cols;
										const height = imageDimensions.height / rows;

										// Draw jigsaw piece shape for clipping
										drawJigsawPiece(ctx, x, y, width, height, row, col, rows, cols, seed);
									},
								}}
							>
								<Image
									config={{
										image,
										width: imageDimensions.width,
										height: imageDimensions.height,
										x: -(imageDimensions.width / cols) * col,
										y: -(imageDimensions.height / rows) * row,
										listening: false,
										perfectDrawEnabled: false,
									}}
								/>
							</Group>

							<!-- Shape outline: drawn outside the clipped group -->
							<Shape
								config={{
									stroke: 'black',
									strokeWidth: 0.5,
									listening: false,
									shadowForStrokeEnabled: false,
									perfectDrawEnabled: false,
									sceneFunc(ctx, shape) {
										const x = 0;
										const y = 0;
										const width = imageDimensions.width / cols;
										const height = imageDimensions.height / rows;

										// Draw the jigsaw piece shape outline
										drawJigsawPiece(ctx, x, y, width, height, row, col, rows, cols, seed);
										ctx.fillStrokeShape(shape);
									},
									fill: 'transparent',
								}}
							/>
						</Group>
					{/each}
				{/each}
			</Layer>
		</Stage>
	{/if}
</div>
