<script lang="ts">
	import { cn } from '$lib/util/cn';
	import { Grid } from '$lib/util/Grid.svelte';
	import Konva from 'konva';
	import type { Context, SceneContext } from 'konva/lib/Context';
	import type { Node, NodeConfig } from 'konva/lib/Node';
	import { onMount, tick, untrack } from 'svelte';
	import { Group, Image, Layer, Stage, Shape } from 'svelte-konva';
	import { type TransitionConfig } from 'svelte/transition';

	interface Props {
		src: string;
		rows: number;
		cols: number;
		class?: string;
		noOutTransition?: boolean;
	}

	let {
		src,
		rows: propsRows,
		cols: propsCols,
		class: className,
		noOutTransition,
	}: Props = $props();
	let image = $state<HTMLImageElement>();
	let container = $state<HTMLDivElement>();
	let containerSize = $state({ width: 0, height: 0 });
	let stage = $state<Konva.Stage>();
	let rows = $state(untrack(() => propsRows));
	let cols = $state(untrack(() => propsCols));

	onMount(() => {
		onResize();
	});

	$effect(() => {
		if (src) {
			const img = document.createElement('img');
			img.src = src;
			img.onload = () => {
				untrack(() => {
					image = img;
				});
			};
		}
	});

	let animationStarted = $state(false);
	let piecesGrid = new Grid<Node<NodeConfig> | undefined>({
		rows: 0,
		columns: 0,
		reactive: false,
	});

	function storeAllPieces() {
		piecesGrid = new Grid<Node<NodeConfig> | undefined>(
			{
				rows,
				columns: cols,
				reactive: false,
			},
			(row, col) => {
				return stage!.findOne(`#${row}-${col}`);
			},
		);
	}

	function square(progress: number) {
		if (progress > 0.5) {
			return 1;
		}

		return 0;
	}

	function getStaggeredFadeAnimation({
		fadeIn,
		onComplete,
	}: {
		fadeIn: boolean;
		onComplete?: () => void;
	}) {
		const baseDelay = 35;
		const duration = 140;

		const animation = new Konva.Animation((frame) => {
			for (let row = 0; row < piecesGrid.rows; row++) {
				for (let col = 0; col < piecesGrid.columns; col++) {
					// Calculate distance from top-left (0,0)
					const distance = row + col;

					// Delay based on distance from top-left
					const delay = distance * baseDelay;
					let progress = (frame!.time - delay) / duration;

					if (progress < 0) {
						continue;
					}

					if (progress > 1) {
						progress = 1;
					}

					const piece = piecesGrid.grid[row][col];
					const visible = (fadeIn ? square(progress) : square(1 - progress)) === 1;

					piece?.visible(visible);

					if (progress === 1 && row === rows - 1 && col === cols - 1) {
						animation.stop();
						onComplete?.();
					}
				}
			}
		});

		return animation;
	}

	let fadeInAnimation: ReturnType<typeof getStaggeredFadeAnimation> | null = $state(null);
	let fadeOutAnimation: ReturnType<typeof getStaggeredFadeAnimation> | null = $state(null);
	let isFirstAnimation = $state(true);

	$effect(() => {
		if (
			(isFirstAnimation || (propsRows !== rows && propsCols !== cols)) &&
			image &&
			!fadeInAnimation &&
			!fadeOutAnimation
		) {
			untrack(() => {
				startRevealAnimation();
			});
		}
	});

	async function startRevealAnimation() {
		animationStarted = true;

		if (fadeInAnimation) {
			fadeInAnimation.stop();
		}

		if (fadeOutAnimation) {
			fadeOutAnimation.stop();
		}

		if (isFirstAnimation) {
			storeAllPieces();
			fadeInAnimation = getStaggeredFadeAnimation({
				fadeIn: true,
				onComplete() {
					fadeInAnimation = null;
					isFirstAnimation = false;
				},
			});
			fadeInAnimation.start();
			return;
		}

		fadeOutAnimation = getStaggeredFadeAnimation({
			fadeIn: false,
			async onComplete() {
				fadeOutAnimation = null;
				rows = propsRows;
				cols = propsCols;

				await tick();

				storeAllPieces();

				fadeInAnimation = getStaggeredFadeAnimation({
					fadeIn: true,
					onComplete() {
						fadeInAnimation = null;
						isFirstAnimation = false;
					},
				});
				fadeInAnimation.start();
			},
		});

		fadeOutAnimation.start();
	}

	// Function to draw a jigsaw piece shape
	function drawJigsawPiece(
		ctx: SceneContext | Context,
		x: number,
		y: number,
		width: number,
		height: number,
		row: number,
		col: number,
		rows: number,
		cols: number,
	) {
		// Create deterministic but consistent tab shapes
		const tabSize = Math.min(width, height) * 0.2;

		// For each edge, we need to ensure that adjacent pieces use the same seed
		// but with opposite directions

		// Create consistent tab shapes for each edge
		// We'll use the position of the edge itself as the seed
		// This ensures the same edge between two pieces has the same shape
		const topEdgeSeed = row * cols + col;
		const rightEdgeSeed = row * cols + col + 1;
		const bottomEdgeSeed = (row + 1) * cols + col;
		const leftEdgeSeed = row * cols + col;

		// Create variations based on consistent seeds
		const topTabVariation = Math.sin(topEdgeSeed * 0.3) * 0.3 + 0.7;
		const rightTabVariation = Math.sin(rightEdgeSeed * 0.3) * 0.3 + 0.7;
		const bottomTabVariation = Math.sin(bottomEdgeSeed * 0.3) * 0.3 + 0.7;
		const leftTabVariation = Math.sin(leftEdgeSeed * 0.3) * 0.3 + 0.7;

		// Calculate tab dimensions
		const topTabWidth = width * 0.4 * topTabVariation;
		const rightTabHeight = height * 0.4 * rightTabVariation;
		const bottomTabWidth = width * 0.4 * bottomTabVariation;
		const leftTabHeight = height * 0.4 * leftTabVariation;

		// Determine tab directions consistently
		// For horizontal edges (top/bottom), use row number to determine direction
		// For vertical edges (left/right), use column number to determine direction
		// This ensures adjacent pieces have complementary shapes
		const topTabDirection = row % 2 === 0 ? -1 : 1;
		const rightTabDirection = col % 2 === 0 ? 1 : -1;
		const bottomTabDirection = row % 2 === 0 ? 1 : -1; // Opposite of top
		const leftTabDirection = col % 2 === 0 ? -1 : 1; // Opposite of right

		ctx.beginPath();

		// Start at top-left corner
		ctx.moveTo(x, y);

		// Top edge
		if (row > 0) {
			ctx.lineTo(x + width * 0.3, y);
			ctx.bezierCurveTo(
				x + width * 0.3 + topTabWidth * 0.25,
				y + topTabDirection * tabSize * topTabVariation,
				x + width * 0.7 - topTabWidth * 0.25,
				y + topTabDirection * tabSize * topTabVariation,
				x + width * 0.7,
				y,
			);
		}
		ctx.lineTo(x + width, y);

		// Right edge
		if (col < cols - 1) {
			ctx.lineTo(x + width, y + height * 0.3);
			ctx.bezierCurveTo(
				x + width + rightTabDirection * tabSize * rightTabVariation,
				y + height * 0.3 + rightTabHeight * 0.25,
				x + width + rightTabDirection * tabSize * rightTabVariation,
				y + height * 0.7 - rightTabHeight * 0.25,
				x + width,
				y + height * 0.7,
			);
		}
		ctx.lineTo(x + width, y + height);

		// Bottom edge
		if (row < rows - 1) {
			ctx.lineTo(x + width * 0.7, y + height);
			ctx.bezierCurveTo(
				x + width * 0.7 - bottomTabWidth * 0.25,
				y + height + bottomTabDirection * tabSize * bottomTabVariation,
				x + width * 0.3 + bottomTabWidth * 0.25,
				y + height + bottomTabDirection * tabSize * bottomTabVariation,
				x + width * 0.3,
				y + height,
			);
		}
		ctx.lineTo(x, y + height);

		// Left edge
		if (col > 0) {
			ctx.lineTo(x, y + height * 0.7);
			ctx.bezierCurveTo(
				x + leftTabDirection * tabSize * leftTabVariation,
				y + height * 0.7 - leftTabHeight * 0.25,
				x + leftTabDirection * tabSize * leftTabVariation,
				y + height * 0.3 + leftTabHeight * 0.25,
				x,
				y + height * 0.3,
			);
		}
		ctx.lineTo(x, y);

		ctx.closePath();
	}

	// Crop image like css background-cover
	function getDimensions() {
		if (!image || !container) return { x: 0, y: 0, width: 0, height: 0 };

		const containerWidth = container.clientWidth;
		const containerHeight = container.clientHeight;
		const imageWidth = image.width;
		const imageHeight = image.height;

		// Calculate the scaling ratios
		const scaleX = containerWidth / imageWidth;
		const scaleY = containerHeight / imageHeight;

		// Use the larger scale to ensure the image covers the container
		const scale = Math.max(scaleX, scaleY);

		// Calculate the new dimensions
		const newWidth = imageWidth * scale;
		const newHeight = imageHeight * scale;

		// Calculate position to center the image
		const x = (containerWidth - newWidth) / 2;
		const y = (containerHeight - newHeight) / 2;

		console.log({ x, y, newWidth, newHeight });

		return {
			x,
			y,
			width: newWidth,
			height: newHeight,
		};
	}

	let imageDimensions = $state(getDimensions());

	function onResize() {
		containerSize = { width: container?.clientWidth ?? 0, height: container?.clientHeight ?? 0 };
		imageDimensions = getDimensions();
	}

	function outTransition(node: HTMLElement): TransitionConfig {
		if (noOutTransition) {
			return {
				duration: 0,
				tick(t, u) {
					return;
				},
			};
		}

		return {
			duration: 1000,
			tick(t, u) {
				if (fadeOutAnimation) {
					return;
				}

				fadeOutAnimation = getStaggeredFadeAnimation({ fadeIn: false });
				fadeOutAnimation.start();
			},
		};
	}
</script>

<svelte:window onresize={onResize} />

<div class={cn('size-full', className)} bind:this={container} out:outTransition>
	{#if container}
		<Stage config={containerSize} bind:handle={stage}>
			<Layer
				config={{
					opacity: animationStarted ? 1 : 0,
					listening: false,
				}}
			>
				{#each { length: rows } as _, row}
					{#each { length: cols } as _, col}
						<Group
							config={{
								x: (containerSize.width / cols) * col,
								y: (containerSize.height / rows) * row,
								width: containerSize.width / cols,
								height: containerSize.height / rows,
								id: `${row}-${col}`,
								visible: false,
								// opacity: 0,
								listening: false,
								clipFunc(ctx) {
									const x = 0;
									const y = 0;
									const width = containerSize.width / cols;
									const height = containerSize.height / rows;

									// Draw jigsaw piece shape
									drawJigsawPiece(ctx, x, y, width, height, row, col, rows, cols);
								},
							}}
						>
							<Image
								config={{
									image,
									...containerSize,
									x: -(containerSize.width / cols) * col,
									y: -(containerSize.height / rows) * row,
									listening: false,
									perfectDrawEnabled: false,
								}}
							/>

							<Shape
								config={{
									stroke: 'black',
									strokeWidth: 1,
									listening: false,
									shadowForStrokeEnabled: false,
									perfectDrawEnabled: false,
									sceneFunc(ctx, shape) {
										const x = 0;
										const y = 0;
										const width = containerSize.width / cols;
										const height = containerSize.height / rows;

										// Draw the same jigsaw piece shape for the stroke
										drawJigsawPiece(ctx, x, y, width, height, row, col, rows, cols);
										ctx.fillStrokeShape(shape);
									},
									fill: 'transparent',
								}}
							/>
						</Group>
					{/each}
				{/each}
			</Layer>
		</Stage>
	{/if}
</div>
