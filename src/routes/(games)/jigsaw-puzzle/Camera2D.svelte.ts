import type { Point2D } from '$lib/models/Point2D';
import type { Size } from '$lib/models/Size';

interface Camera2DProps {
	target: HTMLElement;
	enabled?: boolean;
	minZoom?: number;
	maxZoom?: number;
	boundary?: Size & Point2D;
}

export class Camera2D {
	target: HTMLElement;

	// Callback for when zoom changes - can be set by external code
	onZoomChange?: (newZoom: number, oldZoom: number) => void;

	private _zoom = $state(1);
	private _minZoom: number;
	private _maxZoom: number;
	private _boundary?: Size & Point2D;
	private _isPinching = false;
	private _lastPinchDistance = 0;
	private _pinchCenter = { x: 0, y: 0 };
	private _isMiddleMouseDragging = false;
	private _lastMousePos = { x: 0, y: 0 };
	private _enabled = $state(true);
	private abortController = new AbortController();

	constructor({ target, enabled = true, minZoom = 0.25, maxZoom = 2.0, boundary }: Camera2DProps) {
		this.target = target;
		this._enabled = enabled;
		this._minZoom = minZoom;
		this._maxZoom = maxZoom;
		this._boundary = boundary;

		// Ensure initial zoom is within bounds
		this._zoom = Math.max(minZoom, Math.min(maxZoom, this._zoom));

		this.initListeners();
	}

	private _pos: Point2D = $state({
		x: 0,
		y: 0,
	});

	get boundaryCenterX() {
		if (!this.boundary) {
			return 0;
		}

		return ((1 - this.zoom) * (Math.abs(this.boundary.x) + this.boundary.width)) / 2;
	}

	get boundaryCenterY() {
		if (!this.boundary) {
			return 0;
		}

		return ((1 - this.zoom) * (Math.abs(this.boundary.y) + this.boundary.height)) / 2;
	}

	get x() {
		return this._pos.x;
	}

	set x(value: number) {
		this._pos.x = this.constrainX(value);
	}

	get y() {
		return this._pos.y;
	}

	set y(value: number) {
		this._pos.y = this.constrainY(value);
	}

	get enabled() {
		return this._enabled;
	}

	set enabled(value: boolean) {
		this._enabled = value;
	}

	get zoom() {
		return this._zoom;
	}

	set zoom(value: number) {
		// Constrain zoom value within bounds
		const newZoom = Math.max(this._minZoom, Math.min(this._maxZoom, value));
		const oldZoom = this._zoom;

		if (oldZoom !== newZoom) {
			// Calculate the center point of the current view in world coordinates
			const viewCenterX = this.x + this.target.clientWidth / 2 / oldZoom;
			const viewCenterY = this.y + this.target.clientHeight / 2 / oldZoom;

			// Update zoom
			this._zoom = newZoom;

			// Calculate new camera position to maintain the same center point
			const newX = viewCenterX - this.target.clientWidth / 2 / newZoom;
			const newY = viewCenterY - this.target.clientHeight / 2 / newZoom;

			// Apply the new position (constraints will be applied automatically)
			this.pos(newX, newY);

			// Trigger zoom change callback
			if (this.onZoomChange) {
				this.onZoomChange(newZoom, oldZoom);
			}
		} else {
			this._zoom = newZoom;
		}
	}

	get minZoom() {
		return this._minZoom;
	}

	get maxZoom() {
		return this._maxZoom;
	}

	get boundary() {
		return this._boundary;
	}

	set boundary(value: (Size & Point2D) | undefined) {
		this._boundary = value;

		// Re-apply constraints to current position if boundary is set
		if (value) {
			this.pos(this.x, this.y);
		}
	}

	pos(x: number, y: number) {
		this.x = x;
		this.y = y;
	}

	private constrainX(value: number): number {
		if (!this._boundary) return value;

		// When zoom > 1 (zoomed in), we need MORE movement range to explore
		// When zoom < 1 (zoomed out), we need LESS movement range
		const scaledWidth = this._boundary.width * this._zoom;

		const minX = this._boundary.x;
		const maxX = this._boundary.x + scaledWidth;

		return Math.max(minX, Math.min(maxX, value));
	}

	private constrainY(value: number): number {
		if (!this._boundary) return value;

		// When zoom > 1 (zoomed in), we need MORE movement range to explore
		// When zoom < 1 (zoomed out), we need LESS movement range
		const scaledHeight = this._boundary.height * this._zoom;

		const minY = this._boundary.y;
		const maxY = this._boundary.y + scaledHeight;

		return Math.max(minY, Math.min(maxY, value));
	}

	moveBy(x: number, y: number) {
		this.x += x;
		this.y += y;
	}

	zoomBy(zoom: number) {
		this.zoom = this.zoom + zoom;
	}

	private onWheel(event: WheelEvent) {
		if (!this.enabled) {
			return;
		}

		if (event.cancelable) {
			event.preventDefault();
		}

		// Detect trackpad pinch-to-zoom gestures
		if (event.ctrlKey && Math.abs(event.deltaY) > 0) {
			this.handleTrackpadPinch(event);
			return;
		}

		if (event.metaKey || event.altKey) {
			// Get pointer position relative to the target element
			const rect = this.target.getBoundingClientRect();
			const pointer = {
				x: event.clientX - rect.left,
				y: event.clientY - rect.top,
			};

			// Store old zoom value
			const oldZoom = this.zoom;

			// Calculate the point in world coordinates before zoom
			const mousePointTo = {
				x: (pointer.x - this.x) / oldZoom,
				y: (pointer.y - this.y) / oldZoom,
			};

			// Calculate zoom delta proportional to wheel delta
			const zoomSensitivity = 0.001;
			const zoomDelta = -event.deltaY * zoomSensitivity;

			// Calculate new zoom level
			const newZoom = oldZoom + zoomDelta;

			// Update zoom (setter will handle limits)
			this.zoom = newZoom;

			// Only update position if zoom actually changed
			if (this.zoom !== oldZoom) {
				// Calculate new position to keep the mouse point fixed
				const newPos = {
					x: pointer.x - mousePointTo.x * this.zoom,
					y: pointer.y - mousePointTo.y * this.zoom,
				};

				// Update position
				this.pos(newPos.x, newPos.y);
			}

			return;
		}

		this.moveBy(-event.deltaX, -event.deltaY);
	}

	private handleTrackpadPinch(event: WheelEvent) {
		// Get pointer position relative to the target element
		const rect = this.target.getBoundingClientRect();
		const pointer = {
			x: event.clientX - rect.left,
			y: event.clientY - rect.top,
		};

		// Store old zoom value
		const oldZoom = this.zoom;

		// Calculate the point in world coordinates before zoom
		const mousePointTo = {
			x: (pointer.x - this.x) / oldZoom,
			y: (pointer.y - this.y) / oldZoom,
		};

		// Trackpad pinch gestures have ctrlKey = true and use deltaY for zoom
		// Negative deltaY = zoom in, positive deltaY = zoom out
		const zoomSensitivity = 0.01;
		const zoomDelta = -event.deltaY * zoomSensitivity;

		// Calculate new zoom level
		const newZoom = oldZoom + zoomDelta;

		// Update zoom (setter will handle limits)
		this.zoom = newZoom;

		// Only update position if zoom actually changed
		if (this.zoom !== oldZoom) {
			// Calculate new position to keep the pointer position fixed
			const newPos = {
				x: pointer.x - mousePointTo.x * this.zoom,
				y: pointer.y - mousePointTo.y * this.zoom,
			};

			// Update position
			this.pos(newPos.x, newPos.y);
		}
	}

	private onMouseDown(event: MouseEvent) {
		if (!this.enabled) {
			return;
		}

		// Check for middle mouse button (button 1)
		if (event.button === 1) {
			if (event.cancelable) {
				event.preventDefault();
			}

			this._isMiddleMouseDragging = true;
			this._lastMousePos = {
				x: event.clientX,
				y: event.clientY,
			};

			// Change cursor to indicate dragging mode
			this.target.style.cursor = 'grabbing';
		}
	}

	private onMouseMove(event: MouseEvent) {
		if (!this.enabled || !this._isMiddleMouseDragging) {
			return;
		}

		if (event.cancelable) {
			event.preventDefault();
		}

		// Calculate mouse movement delta
		const deltaX = event.clientX - this._lastMousePos.x;
		const deltaY = event.clientY - this._lastMousePos.y;

		// Move camera by the delta
		this.moveBy(deltaX, deltaY);

		// Update last mouse position
		this._lastMousePos = {
			x: event.clientX,
			y: event.clientY,
		};
	}

	private onMouseUp(event: MouseEvent) {
		if (!this.enabled) {
			return;
		}

		// Check for middle mouse button release
		if (event.button === 1 && this._isMiddleMouseDragging) {
			this._isMiddleMouseDragging = false;

			// Reset cursor
			this.target.style.cursor = '';
		}
	}

	private onTouchStart(event: TouchEvent) {
		if (!this.enabled) {
			return;
		}

		if (event.touches.length === 2) {
			this._isPinching = true;

			// Calculate initial distance between two touches
			const touch1 = event.touches[0];
			const touch2 = event.touches[1];
			this._lastPinchDistance = Math.hypot(
				touch2.clientX - touch1.clientX,
				touch2.clientY - touch1.clientY,
			);

			// Calculate center point between the two touches
			const rect = this.target.getBoundingClientRect();
			this._pinchCenter = {
				x: (touch1.clientX + touch2.clientX) / 2 - rect.left,
				y: (touch1.clientY + touch2.clientY) / 2 - rect.top,
			};
		}
	}

	private onTouchMove(event: TouchEvent) {
		if (!this.enabled) {
			return;
		}

		if (event.cancelable) {
			event.preventDefault();
		}

		if (this._isPinching && event.touches.length === 2) {
			const touch1 = event.touches[0];
			const touch2 = event.touches[1];

			// Calculate current distance between touches
			const currentDistance = Math.hypot(
				touch2.clientX - touch1.clientX,
				touch2.clientY - touch1.clientY,
			);

			// Calculate zoom factor based on distance change
			const zoomFactor = currentDistance / this._lastPinchDistance;

			// Store old zoom value
			const oldZoom = this.zoom;

			// Calculate the point in world coordinates before zoom (using pinch center)
			const mousePointTo = {
				x: (this._pinchCenter.x - this.x) / oldZoom,
				y: (this._pinchCenter.y - this.y) / oldZoom,
			};

			// Apply zoom
			const newZoom = oldZoom * zoomFactor;
			this.zoom = newZoom;

			// Only update position if zoom actually changed
			if (this.zoom !== oldZoom) {
				// Calculate new position to keep the pinch center fixed
				const newPos = {
					x: this._pinchCenter.x - mousePointTo.x * this.zoom,
					y: this._pinchCenter.y - mousePointTo.y * this.zoom,
				};

				// Update position
				this.pos(newPos.x, newPos.y);
			}

			// Update last distance for next frame
			this._lastPinchDistance = currentDistance;
		}
	}

	private onTouchEnd(event: TouchEvent) {
		if (!this.enabled) {
			return;
		}

		// Reset pinching state when touches end
		if (event.touches.length < 2) {
			this._isPinching = false;
			this._lastPinchDistance = 0;
		}
	}

	private initListeners() {
		this.target.addEventListener('wheel', (e) => this.onWheel(e), {
			signal: this.abortController.signal,
		});
		this.target.addEventListener('touchstart', (e) => this.onTouchStart(e), {
			signal: this.abortController.signal,
		});
		this.target.addEventListener('touchmove', (e) => this.onTouchMove(e), {
			signal: this.abortController.signal,
		});
		this.target.addEventListener('touchend', (e) => this.onTouchEnd(e), {
			signal: this.abortController.signal,
		});
		this.target.addEventListener('mousedown', (e) => this.onMouseDown(e), {
			signal: this.abortController.signal,
		});
		this.target.addEventListener('mousemove', (e) => this.onMouseMove(e), {
			signal: this.abortController.signal,
		});
		this.target.addEventListener('mouseup', (e) => this.onMouseUp(e), {
			signal: this.abortController.signal,
		});
	}

	dispose() {
		this.abortController.abort();
	}
}
