// interface Props {}

import type { Point2D } from '$lib/models/Point2D';

interface Camera2DProps {
	target: HTMLElement;
	enabled?: boolean;
}

export class Camera2D {
	zoom = $state(0.8);
	target: HTMLElement;
	private _isPinching = false;
	private _enabled = $state(true);
	private abortController = new AbortController();

	constructor({ target, enabled = true }: Camera2DProps) {
		this.target = target;
		this._enabled = enabled;
		this.initListeners();
	}

	private _pos: Point2D = $state({
		x: 0,
		y: 0,
	});

	get x() {
		return this._pos.x;
	}

	set x(value: number) {
		this._pos.x = value;
	}

	get y() {
		return this._pos.y;
	}

	set y(value: number) {
		this._pos.y = value;
	}

	get enabled() {
		return this._enabled;
	}

	set enabled(value: boolean) {
		this._enabled = value;
	}

	pos(x: number, y: number) {
		this.x = x;
		this.y = y;
	}

	moveBy(x: number, y: number) {
		this.x += x;
		this.y += y;
	}

	zoomBy(zoom: number) {
		this.zoom += zoom;
	}

	private onWheel(event: WheelEvent) {
		if (!this.enabled) {
			return;
		}

		if (event.cancelable) {
			event.preventDefault();
		}

    if (event.)
		this.moveBy(-event.deltaX, -event.deltaY);
	}

	private onTouchStart(event: TouchEvent) {
		if (!this.enabled) {
			return;
		}

		// if (event.cancelable) {
		// 	event.preventDefault();
		// }

		// if (event.touches.length === 2) {
		// 	this._isPinching = true;
		// }
	}

	private onTouchMove(event: TouchEvent) {
		if (!this.enabled) {
			return;
		}

		// if (event.cancelable) {
		// 	event.preventDefault();
		// }

		// if (this._isPinching) {
		// 	var dist = Math.hypot(
		// 		event.touches[0].pageX - event.touches[1].pageX,
		// 		event.touches[0].pageY - event.touches[1].pageY,
		// 	);
		// 	this.zoom = dist / 100;
		// }
	}

	private onTouchEnd(event: TouchEvent) {
		if (!this.enabled) {
			return;
		}

		// if (event.cancelable) {
		// 	event.preventDefault();
		// }

		// this._isPinching = false;
	}

	private initListeners() {
		this.target.addEventListener('wheel', (e) => this.onWheel(e), {
			signal: this.abortController.signal,
		});
		this.target.addEventListener('touchstart', (e) => this.onTouchStart(e), {
			signal: this.abortController.signal,
		});
		this.target.addEventListener('touchmove', (e) => this.onTouchMove(e), {
			signal: this.abortController.signal,
		});
		this.target.addEventListener('touchend', (e) => this.onTouchEnd(e), {
			signal: this.abortController.signal,
		});
	}

	dispose() {
		this.abortController.abort();
	}
}
