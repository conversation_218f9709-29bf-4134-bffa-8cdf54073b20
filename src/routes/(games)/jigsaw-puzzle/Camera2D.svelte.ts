// interface Props {}

import type { Point2D } from '$lib/models/Point2D';

interface Camera2DProps {
	target: HTMLElement;
	enabled?: boolean;
}

export class Camera2D {
	zoom = $state(0.8);
	target: HTMLElement;
	private _isPinching = false;
	private _lastPinchDistance = 0;
	private _pinchCenter = { x: 0, y: 0 };
	private _isTrackpadPinching = false;
	private _lastTrackpadZoom = 0;
	private _isMiddleMouseDragging = false;
	private _lastMousePos = { x: 0, y: 0 };
	private _enabled = $state(true);
	private abortController = new AbortController();

	constructor({ target, enabled = true }: Camera2DProps) {
		this.target = target;
		this._enabled = enabled;
		this.initListeners();
	}

	private _pos: Point2D = $state({
		x: 0,
		y: 0,
	});

	get x() {
		return this._pos.x;
	}

	set x(value: number) {
		this._pos.x = value;
	}

	get y() {
		return this._pos.y;
	}

	set y(value: number) {
		this._pos.y = value;
	}

	get enabled() {
		return this._enabled;
	}

	set enabled(value: boolean) {
		this._enabled = value;
	}

	pos(x: number, y: number) {
		this.x = x;
		this.y = y;
	}

	moveBy(x: number, y: number) {
		this.x += x;
		this.y += y;
	}

	zoomBy(zoom: number) {
		this.zoom += zoom;
	}

	private onWheel(event: WheelEvent) {
		if (!this.enabled) {
			return;
		}

		if (event.cancelable) {
			event.preventDefault();
		}

		// Detect trackpad pinch-to-zoom gestures
		if (event.ctrlKey && Math.abs(event.deltaY) > 0) {
			this.handleTrackpadPinch(event);
			return;
		}

		if (event.metaKey || event.altKey) {
			// Get pointer position relative to the target element
			const rect = this.target.getBoundingClientRect();
			const pointer = {
				x: event.clientX - rect.left,
				y: event.clientY - rect.top,
			};

			// Store old zoom value
			const oldZoom = this.zoom;

			// Calculate the point in world coordinates before zoom
			const mousePointTo = {
				x: (pointer.x - this.x) / oldZoom,
				y: (pointer.y - this.y) / oldZoom,
			};

			// Calculate zoom delta proportional to wheel delta
			const zoomSensitivity = 0.001;
			const zoomDelta = -event.deltaY * zoomSensitivity;

			// Calculate new zoom level with limits
			const newZoom = Math.max(0.1, Math.min(5.0, oldZoom + zoomDelta));

			// Update zoom
			this.zoom = newZoom;

			// Calculate new position to keep the mouse point fixed
			const newPos = {
				x: pointer.x - mousePointTo.x * newZoom,
				y: pointer.y - mousePointTo.y * newZoom,
			};

			// Update position
			this.pos(newPos.x, newPos.y);

			return;
		}

		this.moveBy(-event.deltaX, -event.deltaY);
	}

	private handleTrackpadPinch(event: WheelEvent) {
		// Get pointer position relative to the target element
		const rect = this.target.getBoundingClientRect();
		const pointer = {
			x: event.clientX - rect.left,
			y: event.clientY - rect.top,
		};

		// Store old zoom value
		const oldZoom = this.zoom;

		// Calculate the point in world coordinates before zoom
		const mousePointTo = {
			x: (pointer.x - this.x) / oldZoom,
			y: (pointer.y - this.y) / oldZoom,
		};

		// Trackpad pinch gestures have ctrlKey = true and use deltaY for zoom
		// Negative deltaY = zoom in, positive deltaY = zoom out
		const zoomSensitivity = 0.01;
		const zoomDelta = -event.deltaY * zoomSensitivity;

		// Calculate new zoom level
		const newZoom = Math.max(0.1, Math.min(5.0, oldZoom + zoomDelta));

		// Update zoom
		this.zoom = newZoom;

		// Calculate new position to keep the pointer position fixed
		const newPos = {
			x: pointer.x - mousePointTo.x * newZoom,
			y: pointer.y - mousePointTo.y * newZoom,
		};

		// Update position
		this.pos(newPos.x, newPos.y);
	}

	private onMouseDown(event: MouseEvent) {
		if (!this.enabled) {
			return;
		}

		// Check for middle mouse button (button 1)
		if (event.button === 1) {
			if (event.cancelable) {
				event.preventDefault();
			}

			this._isMiddleMouseDragging = true;
			this._lastMousePos = {
				x: event.clientX,
				y: event.clientY,
			};

			// Change cursor to indicate dragging mode
			this.target.style.cursor = 'grabbing';
		}
	}

	private onMouseMove(event: MouseEvent) {
		if (!this.enabled || !this._isMiddleMouseDragging) {
			return;
		}

		if (event.cancelable) {
			event.preventDefault();
		}

		// Calculate mouse movement delta
		const deltaX = event.clientX - this._lastMousePos.x;
		const deltaY = event.clientY - this._lastMousePos.y;

		// Move camera by the delta
		this.moveBy(deltaX, deltaY);

		// Update last mouse position
		this._lastMousePos = {
			x: event.clientX,
			y: event.clientY,
		};
	}

	private onMouseUp(event: MouseEvent) {
		if (!this.enabled) {
			return;
		}

		// Check for middle mouse button release
		if (event.button === 1 && this._isMiddleMouseDragging) {
			this._isMiddleMouseDragging = false;

			// Reset cursor
			this.target.style.cursor = '';
		}
	}

	private onTouchStart(event: TouchEvent) {
		if (!this.enabled) {
			return;
		}

		if (event.touches.length === 2) {
			this._isPinching = true;

			// Calculate initial distance between two touches
			const touch1 = event.touches[0];
			const touch2 = event.touches[1];
			this._lastPinchDistance = Math.hypot(
				touch2.clientX - touch1.clientX,
				touch2.clientY - touch1.clientY,
			);

			// Calculate center point between the two touches
			const rect = this.target.getBoundingClientRect();
			this._pinchCenter = {
				x: (touch1.clientX + touch2.clientX) / 2 - rect.left,
				y: (touch1.clientY + touch2.clientY) / 2 - rect.top,
			};
		}
	}

	private onTouchMove(event: TouchEvent) {
		if (!this.enabled) {
			return;
		}

		if (event.cancelable) {
			event.preventDefault();
		}

		if (this._isPinching && event.touches.length === 2) {
			const touch1 = event.touches[0];
			const touch2 = event.touches[1];

			// Calculate current distance between touches
			const currentDistance = Math.hypot(
				touch2.clientX - touch1.clientX,
				touch2.clientY - touch1.clientY,
			);

			// Calculate zoom factor based on distance change
			const zoomFactor = currentDistance / this._lastPinchDistance;

			// Store old zoom value
			const oldZoom = this.zoom;

			// Calculate the point in world coordinates before zoom (using pinch center)
			const mousePointTo = {
				x: (this._pinchCenter.x - this.x) / oldZoom,
				y: (this._pinchCenter.y - this.y) / oldZoom,
			};

			// Apply zoom
			const newZoom = oldZoom * zoomFactor;
			this.zoom = newZoom;

			// Calculate new position to keep the pinch center fixed
			const newPos = {
				x: this._pinchCenter.x - mousePointTo.x * newZoom,
				y: this._pinchCenter.y - mousePointTo.y * newZoom,
			};

			// Update position
			this.pos(newPos.x, newPos.y);

			// Update last distance for next frame
			this._lastPinchDistance = currentDistance;
		}
	}

	private onTouchEnd(event: TouchEvent) {
		if (!this.enabled) {
			return;
		}

		// Reset pinching state when touches end
		if (event.touches.length < 2) {
			this._isPinching = false;
			this._lastPinchDistance = 0;
		}
	}

	private initListeners() {
		this.target.addEventListener('wheel', (e) => this.onWheel(e), {
			signal: this.abortController.signal,
		});
		this.target.addEventListener('touchstart', (e) => this.onTouchStart(e), {
			signal: this.abortController.signal,
		});
		this.target.addEventListener('touchmove', (e) => this.onTouchMove(e), {
			signal: this.abortController.signal,
		});
		this.target.addEventListener('touchend', (e) => this.onTouchEnd(e), {
			signal: this.abortController.signal,
		});
	}

	dispose() {
		this.abortController.abort();
	}
}
