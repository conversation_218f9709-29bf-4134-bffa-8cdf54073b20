<script lang="ts">
	import CenterIcon from '$lib/components/Icons/CenterIcon.svelte';
	import ChevronLeftIcon from '$lib/components/Icons/ChevronLeftIcon.svelte';
	import ImageIcon from '$lib/components/Icons/ImageIcon.svelte';
	import LightBulbIcon from '$lib/components/Icons/LightBulbIcon.svelte';
	import { Image, Layer, Stage, Group, Rect } from 'svelte-konva';
	import type { JigsawPuzzleGame } from './game/JigsawPuzzleGame.svelte';
	import JigsawFloatingBar from './JigsawFloatingBar.svelte';
	import Konva from 'konva';
	import { onMount } from 'svelte';
	import { stages } from 'konva/lib/Stage';
	import { Util } from 'konva/lib/Util';
	import { theme } from '$lib/stores/theme.svelte';
	import { siteSounds } from '$lib/stores/siteSounds.svelte';

	interface Props {
		game: JigsawPuzzleGame;
		onBack: () => void;
	}

	let { game, onBack }: Props = $props();

	let container = $state<HTMLDivElement>();
	let stage = $state<Konva.Stage>();
	let canShowImage = $state(false);
	let rectColor = $state('white');
	let imageRef = $state<Konva.Image>();
	let imageOpacityTween = $derived.by(() => {
		if (imageRef) {
			return new Konva.Tween({
				duration: 0.3,
				node: imageRef,
				opacity: 0.8,
				easing: Konva.Easings.EaseInOut,
			});
		}
	});

	function onResize() {
		game.container = { width: container?.clientWidth ?? 0, height: container?.clientHeight ?? 0 };
	}

	function updateRectColor() {
		const style = getComputedStyle(document.documentElement);
		rectColor = style.getPropertyValue('--color-base-300');
	}

	$effect(() => {
		// Track theme change
		theme.value;

		updateRectColor();
	});

	onMount(() => {
		if (import.meta.env.DEV) {
			(window as any).Konva.stages = stages;
			(window as any).Konva.Util = Util;
		}

		onResize();
	});
</script>

<svelte:window onresize={onResize} />

<div class="size-full" bind:this={container}>
	{#if container && game}
		<Stage
			config={{
				width: game.container.width,
				height: game.container.height,
				scale: {
					x: game.camera.zoom,
					y: game.camera.zoom,
				},
				x: game.camera.x,
				y: game.camera.y,
			}}
			bind:handle={stage}
		>
			<!-- Background Image -->
			<Layer
				config={{
					listening: false,
				}}
			>
				{#if game.imageElement && game.imageDimensions}
					<Group>
						<Rect
							config={{
								width: game.imageDimensions.width,
								height: game.imageDimensions.height,
								x: (game.container.width - game.imageDimensions.width) / 2,
								y: (game.container.height - game.imageDimensions.height) / 2,
								fill: rectColor,
							}}
						/>

						<Image
							bind:handle={imageRef}
							config={{
								image: game.imageElement,
								width: game.imageDimensions.width,
								height: game.imageDimensions.height,
								x: (game.container.width - game.imageDimensions.width) / 2,
								y: (game.container.height - game.imageDimensions.height) / 2,
								perfectDrawEnabled: false,
								opacity: 0,
							}}
						/>
					</Group>
				{/if}
			</Layer>

			<!-- Game -->
			<Layer>
				{#if game.imageElement && game.imageDimensions}{/if}
			</Layer>
		</Stage>
	{/if}
</div>

<JigsawFloatingBar>
	<button
		class="btn btn-ghost btn-circle"
		onclick={onBack}
		aria-label="Back to jigsaw puzzle selection"
	>
		<ChevronLeftIcon class="size-6" />
	</button>

	<button
		class="btn btn-ghost btn-circle"
		disabled={!game.enabled}
		onclick={() => {
			// unselectImage();
		}}
		aria-label="Show hint"
	>
		<LightBulbIcon class="size-6" />
	</button>

	<button
		class="btn btn-ghost btn-circle"
		class:btn-active={canShowImage}
		disabled={!game.enabled}
		onclick={() => {
			canShowImage = !canShowImage;

			if (canShowImage) {
				imageOpacityTween?.play();
				siteSounds.toggleOn.play();
			} else {
				imageOpacityTween?.reverse();
				siteSounds.toggleOff.play();
			}
		}}
		aria-label="Show current puzzle image"
	>
		<ImageIcon class="size-6" />
	</button>

	<button
		class="btn btn-ghost btn-circle"
		disabled={!game.enabled}
		onclick={() => {
			// unselectImage();
		}}
		aria-label="Centralize"
	>
		<!-- TODO: Change this icon -->
		<CenterIcon class="size-6" />
	</button>
</JigsawFloatingBar>
