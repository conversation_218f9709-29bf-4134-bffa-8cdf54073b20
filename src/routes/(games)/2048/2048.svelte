<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import { quintInOut } from 'svelte/easing';
	import { _2048Game } from './_2048Game.svelte';
	import { heroTranslate } from '$lib/functions/heroTranslate';
	import { _2048SoundResources } from './_2048SoundResources';
	import GameLayout from '$lib/components/GameLayout/GameLayout.svelte';
	import { cn } from '$lib/util/cn';
	import GameIsland from '$lib/components/GameIsland/GameIsland.svelte';
	import { Stats } from '$lib/util/Stats.svelte';
	import InstructionsIsland2048 from './InstructionsIsland2048.svelte';
	import { GameContext } from '$lib/util/GameContext/GameContext.svelte';
	import NewGameButton from '$lib/components/NewGameButton.svelte';
	import InfoModal2048 from './InfoModal2048.svelte';
	import InfoSolidIcon from '$lib/components/Icons/InfoSolidIcon.svelte';
	import { Leaderboard } from '$lib/util/Leaderboard.svelte';
	import MoreGamesButton from '$lib/components/MoreGamesButton/MoreGamesButton.svelte';

	let isInfoModalOpen = $state(false);
	let gameElement = $state<HTMLElement>();
	const slideDuration = 100;
	let context = new GameContext({
		gameKey: '2048',
		GameClass: _2048Game,
		settings: {
			defaultSettings: {},
		},
		sounds: {
			resources: _2048SoundResources,
			lifecycle: {
				createGame: _2048SoundResources.replay,
				win: _2048SoundResources.gameWin,
			},
		},
		defaultGameProps(context) {
			return {
				targetElement: gameElement!,
				audios: context.sounds,
				slideDuration,
				timer: context.timer,
				onStart,
				onWin,
				onLose,
			};
		},
		formatted() {
			return {
				name: '2048',
			};
		},
		stats({ props, context }) {
			return {
				stats: new Stats({
					...props,
					gameVariant: 'default',
					liveStats: {
						score: {
							name: 'Score',
							unit: 'plain',
							value() {
								return context.game?.score ?? 0;
							},
							metrics: {
								total: {
									key: 'totalScore',
									name: 'Total Score',
								},
								average: {
									key: 'averageScore',
									name: 'Average Score',
								},
								max: {
									key: 'bestScore',
									name: 'Best Score',
									useAsBest: true,
								},
								min: {
									key: 'worstScore',
									name: 'Worst Score',
								},
							},
						},
					},
					initialPinnedStats: ['time', 'score'] as any,
				}),
				visibleStats: ['bestScore', 'averageScore', 'wonGames', 'totalGames'],
				canUpdateWithGameLost(game) {
					return !game.isWon && game.score > 0;
				},
			};
		},
		leaderboard(context) {
			return {
				leaderboard: new Leaderboard({
					game: context.gameKey,
					order: 'higher-first',
					firstAvailableDate: new Date('2025/05/08'),
				}),
				sendScoreOn: ['won', 'lost'],
				getScore(game) {
					return {
						score: game.score,
					};
				},
			};
		},
		onWillCreateGame({ previousGame }) {
			previousGame?.dispose();
		},
	});

	let game = $derived(context.game);

	const [send, receive] = heroTranslate({
		fallback: () => {
			return {
				duration: 300,
				easing: quintInOut,
				css: (t) => `
					scale: ${t}
				`,
			};
		},
		duration: slideDuration,
	});

	function onStart() {
		context.timer.start();
	}

	function onWin() {
		context.addConfetti();
		context.sounds.gameWin.play();
	}

	function onLose() {
		context.handleGameOver(context.game?.isWon ? 'won' : 'lost', {
			handleConfetti: false,
			handleSound: false,
			updateStats: true,
		});
	}

	onMount(() => {
		context.load();
	});

	onDestroy(() => {
		context.dispose();
	});
</script>

<InfoModal2048 bind:isOpen={isInfoModalOpen} />

<GameLayout>
	{#snippet Island()}
		<GameIsland {context} gameOver={!!context.game?.isLost} gameOverIslandDelay={1000}>
			{#snippet InstructionsIsland()}
				<InstructionsIsland2048 />
			{/snippet}
		</GameIsland>
	{/snippet}

	<section class="max-h-screen-no-navbar flex-center size-full select-none" bind:this={gameElement}>
		<div class="mx-auto w-full max-w-md">
			<div class="relative w-full gap-1 aspect-square bg-game-2048-bg">
				<div class="absolute -top-10 left-0 flex items-center gap-2">
					<NewGameButton
						buttonClass="btn-sm"
						variant="long"
						onPlayNewGame={() => {
							context.createGame();
						}}
					/>

					<button class="btn btn-sm" onclick={() => (isInfoModalOpen = true)}>
						<InfoSolidIcon class="size-5" />
					</button>

					<MoreGamesButton class="btn-sm" />
				</div>

				<!-- Game -->
				<div class="relative size-full overflow-hidden">
					{#each game?.board ?? [] as row, rowIndex (`${rowIndex} - ${game?.id ?? 1}`)}
						{#each row as items, columnIndex (`${columnIndex}`)}
							{#if items !== _2048Game.emptyItem}
								{#each items as item (`${item?.id}`)}
									<div
										in:send|global={{ key: `${item.id}` }}
										out:receive|global={{ key: `${item.id}` }}
										class="absolute flex aspect-square"
										class:bounce={item.justMerged}
										style="
										width: {100 / (game?.size ?? 3)}%;
										top: {(100 * rowIndex) / (game?.size ?? 1)}%;
										left: {(100 * columnIndex) / (game?.size ?? 1)}%;
									"
									>
										<div class="w-full grow border-2 border-transparent">
											<div
												class={cn(
													'flex-center size-full text-center font-bold transition-colors duration-100',
													{
														// Size
														'text-4xl': item.value <= 64,
														'text-3xl': item.value > 64 && item.value < 1024,
														'text-2xl': item.value >= 1024 && item.value < 16_384,
														'text-xl': item.value >= 16_384 && item.value < 131_072,
														// Number color
														'text-game-2048-text-below-8': item.value < 8,
														'text-game-2048-text-above-8': item.value >= 8,
														// Block color
														'bg-game-2048-2': item.value === 2,
														'bg-game-2048-4': item.value === 4,
														'bg-game-2048-8': item.value === 8,
														'bg-game-2048-16': item.value === 16,
														'bg-game-2048-32': item.value === 32,
														'bg-game-2048-64': item.value === 64,
														'bg-game-2048-128': item.value >= 128 && item.value < 512,
														'bg-game-2048-512': item.value >= 512 && item.value < 4096,
														'bg-game-2048-4096': item.value >= 4096 && item.value < 16_384,
														'bg-game-2048-16384': item.value >= 16_384,
													},
												)}
											>
												{item.value ?? ''}
											</div>
										</div>
									</div>
								{/each}
							{/if}
						{/each}
					{/each}
				</div>
			</div>
		</div>
	</section>
</GameLayout>

<style>
	@keyframes bounceAnimation {
		from {
			scale: 1;
		}

		25% {
			scale: 0.8;
		}

		75% {
			scale: 1.1;
		}

		to {
			scale: 1;
		}
	}

	.bounce {
		animation: bounceAnimation 150ms ease-in-out;
	}
</style>
