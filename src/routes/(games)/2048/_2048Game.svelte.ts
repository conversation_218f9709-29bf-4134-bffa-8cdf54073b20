import { get2DGrid } from '$lib/functions/get2DGrid';
import { getGridItemsAround } from '$lib/functions/getGridItemsAround';
import { DirectionListener, type Direction } from '$lib/util/DirectionListener';
import { Timer } from '$lib/util/Timer.svelte';

type Item = {
	id: number;
	value: number;
	justMerged?: boolean;
};
type EmptyItem = null;
type _2048Row = (EmptyItem | Item[])[];
type _2048Board = _2048Row[];

const createItem = (value: number): Item => ({
	value,
	id: Math.floor(Math.random() * 1e9),
});

interface Props {
	targetElement: HTMLElement;
	slideDuration: number;
	timer: Timer;
	onStart: () => void;
	onWin: () => void;
	onLose: () => void;
}

export class _2048Game {
	id: number = Math.floor(Math.random() * 1e9);
	board: _2048Board = $state([]);
	timer: Timer;
	readonly size = 4;
	private _score = $state(0);
	private slideDuration: number;
	private directionListener: DirectionListener;
	private _isLost = $state(false);
	private _isWon = $state(false);
	private scheduleMergeTimeoutId = -1;
	private _onStart: () => void;
	private _onWin: () => void;
	private _onLose: () => void;

	static emptyItem: EmptyItem = null;

	constructor({ onLose, onStart, onWin, slideDuration, targetElement, timer }: Props) {
		this.timer = timer;
		this._onStart = onStart;
		this._onWin = onWin;
		this._onLose = onLose;
		this.slideDuration = slideDuration;
		this.directionListener = new DirectionListener(targetElement, this.handleDirectionChange, {
			emit: 'once',
			throttle: slideDuration * 2,
		});
		this.initBoard();
		// this.debugInitCloseToWinBoard();
		this.addListeners();
	}

	start() {
		if (!this.timer.started) {
			this.timer.start();
			this._onStart();
		}
	}

	get score() {
		return this._score;
	}

	set score(score: number) {
		this._score = score;
	}

	get isWon() {
		return this._isWon;
	}

	get isLost() {
		return this._isLost;
	}

	set isLost(isLost: boolean) {
		this._isLost = isLost;

		if (isLost) {
			this._onLose();
		}
	}

	set isWon(isWon: boolean) {
		const currentIsWon = this._isWon;
		this._isWon = isWon;

		if (currentIsWon === false && isWon) {
			this._onWin();
		}
	}

	moveItems(direction: Direction) {
		if (this.isLost || (this.timer.paused && this.timer.started)) {
			return false;
		}

		this.start();
		const currentBoard = this.board;
		this.groupItemsToBeMerged(direction);
		this.translateItems(direction);
		this.scheduleItemsMerge();

		if (_2048Game.hasDiff(currentBoard, this.board)) {
			this.spawnItem();
		}
	}

	private scheduleItemsMerge() {
		clearTimeout(this.scheduleMergeTimeoutId);

		this.scheduleMergeTimeoutId = setTimeout(() => {
			this.mergeItemsImmediatelyAndProceed();
		}, this.slideDuration / 2) as unknown as number;
	}

	private mergeItemsImmediatelyAndProceed() {
		clearTimeout(this.scheduleMergeTimeoutId);
		this.mergePendingItems();
		this.checkIfGameIsOver();
		this.scheduleMergeTimeoutId = -1;
	}

	private checkIfGameIsOver() {
		this.checkIfGameIsWon();
		this.checkIfGameIsLost();
	}

	private checkIfGameIsWon() {
		if (!this.isWon) {
			this.isWon = this.board.some((row) =>
				row.some((items) => items?.some((item) => item.value >= 2048)),
			);
		}
	}

	private checkIfGameIsLost() {
		this.isLost = this.board.every((row, rowIndex) =>
			row.every((items, columnIndex) => {
				if (items === _2048Game.emptyItem) {
					return false;
				}

				const itemsAround = getGridItemsAround(
					{ row: rowIndex, column: columnIndex },
					{
						row: this.size,
						column: this.size,
					},
					{
						diagonal: false,
					},
				);

				return itemsAround.every((itemAround) => {
					const itemAroundOnBoard = this.board[itemAround.row][itemAround.column];

					if (itemAroundOnBoard === _2048Game.emptyItem) {
						return false;
					}

					return items[0].value !== itemAroundOnBoard[0].value;
				});
			}),
		);
	}

	private spawnItem(value?: number) {
		const hasEmptyItem = this.board.some((row) => row.some((item) => item === _2048Game.emptyItem));

		if (!hasEmptyItem) {
			return;
		}

		const { row, column } = this.getRandomPosition();

		if (this.board[row][column] === _2048Game.emptyItem) {
			this.board[row][column] = [createItem(value ?? this.getRandomItemSpawnValue())];
		} else {
			this.spawnItem(value);
		}
	}

	private getRandomItemSpawnValue = () => (Math.random() < 0.9 ? 2 : 4);

	private getRandomPosition = () => ({
		row: Math.floor(Math.random() * this.size),
		column: Math.floor(Math.random() * this.size),
	});

	private debugInitCloseToWinBoard() {
		this.board = get2DGrid(this.size);
		this.spawnItem(2);
		this.spawnItem(4);
		this.spawnItem(8);
		this.spawnItem(16);
		this.spawnItem(32);
		this.spawnItem(64);
		this.spawnItem(128);
		this.spawnItem(256);
		this.spawnItem(512);
		this.spawnItem(1024);
		this.spawnItem(1024);
		this.spawnItem(1024);
	}

	private initBoard = () => {
		this.board = get2DGrid(this.size);
		this.spawnItem();
		this.spawnItem();
	};

	private groupItemsOnRowFromStartToEnd(row: _2048Row) {
		const denseRow = this.getDense(row) as Item[][];
		const newItems: Item[][] = [];

		for (let i = 0; i < denseRow.length; i += 1) {
			const current = denseRow[i];
			const next = denseRow[i + 1];

			if (next) {
				if (current[0].value === next[0].value) {
					newItems.push([next[0], current[0]]);
					i += 1;
					continue;
				}
			}
			newItems.push(current);
		}

		const newRow = [...newItems, ...Array(row.length - newItems.length).fill(_2048Game.emptyItem)];

		return newRow;
	}

	private groupItemsToBeMerged = (direction: Direction) => {
		if (this.scheduleMergeTimeoutId !== -1) {
			this.mergeItemsImmediatelyAndProceed();
		}

		if (direction === 'right') {
			this.board = this.board.map((row) => {
				return this.groupItemsOnRowFromStartToEnd([...row].reverse()).reverse();
			});
		} else if (direction === 'left') {
			this.board = this.board.map((row) => this.groupItemsOnRowFromStartToEnd(row));
		} else if (direction === 'up') {
			const newBoard: _2048Board = get2DGrid(this.board.length);

			for (let columnIndex = 0; columnIndex < this.board.length; columnIndex += 1) {
				const column = this.getColumn(columnIndex);
				const groupedColumn = this.groupItemsOnRowFromStartToEnd(column);
				groupedColumn.forEach((item, rowIndex) => {
					newBoard[rowIndex][columnIndex] = item;
				});
			}

			this.board = newBoard;
		} else if (direction === 'down') {
			const newBoard: _2048Board = get2DGrid(this.board.length);

			for (let columnIndex = 0; columnIndex < this.board.length; columnIndex += 1) {
				const column = this.getColumn(columnIndex).reverse();
				const groupedColumn = this.groupItemsOnRowFromStartToEnd(column).reverse();
				groupedColumn.forEach((item, rowIndex) => {
					newBoard[rowIndex][columnIndex] = item;
				});
			}

			this.board = newBoard;
		}
	};

	private mergePendingItems = () => {
		this.board = this.board.map((row) =>
			row.map((items) => {
				if (items === _2048Game.emptyItem) {
					return items;
				}

				return [
					items.reduce((previous, current) => {
						const newValue = previous.value + current.value;

						if (previous.value !== 0) {
							this.score += newValue;
						}

						return {
							id: current.id,
							value: newValue,
							justMerged: items.length > 1,
						};
					}, createItem(0)),
				];
			}),
		);
	};

	private getDense = (row: _2048Board[0]) => row.filter((item) => item !== _2048Game.emptyItem);

	private getColumn = (columnIndex: number) =>
		Array(this.board.length)
			.fill(null)
			.map((_, rowIndex) => this.board[rowIndex][columnIndex]);

	private translateItems = (direction: Direction) => {
		if (direction === 'right') {
			this.board = this.board.map((row) => {
				const denseRow = this.getDense(row);
				const newRow = [
					...Array(row.length - denseRow.length).fill(_2048Game.emptyItem),
					...denseRow,
				];

				return newRow;
			});
		} else if (direction === 'left') {
			this.board = this.board.map((row) => {
				const denseRow = this.getDense(row);
				const newRow = [
					...denseRow,
					...Array(row.length - denseRow.length).fill(_2048Game.emptyItem),
				];

				return newRow;
			});
		} else if (direction === 'down') {
			const newBoard = this.cloneBoard();

			for (let columnIndex = 0; columnIndex < newBoard.length; columnIndex += 1) {
				const column = this.getColumn(columnIndex);
				const denseColumn = this.getDense(column);
				const newColumn = [
					...Array(newBoard.length - denseColumn.length).fill(_2048Game.emptyItem),
					...denseColumn,
				];

				for (let rowIndex = 0; rowIndex < newBoard.length; rowIndex += 1) {
					newBoard[rowIndex][columnIndex] = newColumn[rowIndex];
				}

				this.board = newBoard;
			}
		} else if (direction === 'up') {
			const newBoard = this.cloneBoard();

			for (let columnIndex = 0; columnIndex < newBoard.length; columnIndex += 1) {
				const column = this.getColumn(columnIndex);
				const denseColumn = this.getDense(column);
				const newColumn = [
					...denseColumn,
					...Array(newBoard.length - denseColumn.length).fill(_2048Game.emptyItem),
				];

				for (let rowIndex = 0; rowIndex < newBoard.length; rowIndex += 1) {
					newBoard[rowIndex][columnIndex] = newColumn[rowIndex];
				}

				this.board = newBoard;
			}
		}
	};

	private cloneBoard(): _2048Board {
		return this.board.map((row) => row.map((items) => items?.map((item) => item) ?? null));
	}

	private handleDirectionChange = async (direction: Direction) => {
		this.moveItems(direction);
	};

	private addListeners() {
		this.directionListener.listen();
	}

	static hasDiff(board1: _2048Board, board2: _2048Board): boolean {
		return board1.some((row, rowIndex) =>
			row.some((items, itemsIndex) => {
				const board2Items = board2[rowIndex][itemsIndex];
				const itemsIds = items?.map((item) => item.id) ?? [];
				const items2Ids = board2Items?.map((item) => item.id) ?? [];

				return (
					(items === null && board2Items !== null) ||
					(items !== null && board2Items === null) ||
					itemsIds.length !== items2Ids.length ||
					!itemsIds.every((id) => items2Ids.includes(id)) ||
					!items2Ids.every((id) => itemsIds.includes(id))
				);
			}),
		);
	}

	dispose() {
		this.directionListener.dispose();
	}
}
