<script lang="ts">
	import type { Card } from '$lib/models/card-game';
	import { getContext } from 'svelte';
	import { cardStacksKey } from './cardStacksKey';
	import type { CardStacksContextType } from './CardStacksContextType';

	interface Props {
		onclick?: (event: MouseEvent) => void;
		style?: string;
		card: Card;
		stackPosition?: number;
		class?: string;
	}

	let {
		onclick,
		style = '',
		card = $bindable(),
		stackPosition = 0,
		class: classFromProps = '',
	}: Props = $props();

	const flipAnimationDuration = 300;

	let shouldHideCard: boolean = $state(card.face === 'down');
	let lastCardFace = $state(card.face);

	$effect(() => {
		if (lastCardFace === 'down' && card.face === 'up') {
			shouldHideCard = false;
		} else if (lastCardFace === 'up' && card.face === 'down') {
			setTimeout(() => {
				shouldHideCard = card.face === 'down';
			}, flipAnimationDuration);
		}

		lastCardFace = card.face;
	});

	const { send, receive } = getContext<CardStacksContextType>(cardStacksKey);

	// Handle clicks
	let pointerDownX = 0;
	let pointerDownY = 0;
	const pointerErrorMargin = 5;

	function onPointerDown(event: MouseEvent) {
		pointerDownX = Math.floor(event.clientX);
		pointerDownY = Math.floor(event.clientY);
	}

	function handleClick(event: MouseEvent) {
		if (
			Math.abs(event.clientX - pointerDownX) <= pointerErrorMargin &&
			Math.abs(event.clientY - pointerDownY) <= pointerErrorMargin
		) {
			onclick?.(event);
		}
	}
</script>

<!-- svelte-ignore a11y_click_events_have_key_events -->
<!-- svelte-ignore a11y_no_static_element_interactions -->
<div
	onpointerdown={onPointerDown}
	onclick={handleClick}
	in:send|global={{ key: `${card.suit}-${card.value}` }}
	out:receive|global={{ key: `${card.suit}-${card.value}` }}
	class="scene w-full cursor-pointer select-none {classFromProps ?? ''}"
	style="--stack-position:{stackPosition}; {style}"
>
	<div class="game-card" class:show-front={card.face === 'up'}>
		<div class="back">
			<picture class="h-auto w-full aspect-39/53">
				<source
					media="(max-width: 767px)"
					srcset="https://static.lofiandgames.com/images/cards/mobile/card-back.svg"
				/>
				<source
					media="(min-width: 768px)"
					srcset="https://static.lofiandgames.com/images/cards/desktop/card-back.svg"
				/>
				<img
					draggable="false"
					src="https://static.lofiandgames.com/images/cards/mobile/card-back.svg"
					alt="face down card"
					class="h-auto w-full aspect-39/53"
				/>
			</picture>
		</div>

		{#if !shouldHideCard}
			<div class="front absolute inset-0">
				<picture class="h-auto w-full aspect-39/53">
					<source
						media="(max-width: 767px)"
						srcset="https://static.lofiandgames.com/images/cards/mobile/{card.value}-{card.suit}.svg"
					/>
					<source
						media="(min-width: 768px)"
						srcset="https://static.lofiandgames.com/images/cards/desktop/{card.value}-{card.suit}.svg"
					/>
					<img
						draggable="false"
						src="https://static.lofiandgames.com/images/cards/mobile/{card.value}-{card.suit}.svg"
						alt="{card.value} of {card.suit} card"
						class="h-auto w-full aspect-39/53"
					/>
				</picture>
			</div>
		{/if}
	</div>
</div>

<style>
	.scene {
		filter: drop-shadow(0 0 4px rgb(0 0 0 / 0.3));
		perspective: 1000px;
	}

	.game-card {
		position: relative;
		transition: transform 300ms ease-in-out;
		transform-style: preserve-3d;
	}

	.back,
	.front {
		/** Keep -webkit- prefix so it works on old Safari versions */
		-webkit-perspective: 0;
		-webkit-backface-visibility: hidden;
		-webkit-transform: translate3d(0, 0, 0);

		transform: translate3d(0, 0, 0);
		perspective: 0;
		backface-visibility: hidden;
	}

	/** Fix card shrinking on Firefox */
	@supports (-moz-appearance: none) {
		.back img {
			transform: rotateY(0);
		}
	}

	.front {
		transform: rotateY(180deg);
	}

	.show-front {
		transform: rotateY(180deg);
	}
</style>
