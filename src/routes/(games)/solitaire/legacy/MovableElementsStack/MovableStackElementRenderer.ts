import { getTranslateXY } from './getTranslateXY';

export class MovableStackElementRenderer {
	isMoving = false;
	initialZIndex: string;
	initialTranslate: ReturnType<typeof getTranslateXY> = {
		translateX: 0,
		translateY: 0,
	};
	element: HTMLElement;
	canMove = false;
	timeoutId: number = -1;

	constructor(element: HTMLElement) {
		this.element = element;
		this.initialZIndex = window.getComputedStyle(element).zIndex;
		this.element.style.willChange = 'transform';
		this.element.style.perspective = '1000px';
		this.canMove = true;
	}

	translateBy(x: number, y: number) {
		if (this.canMove) {
			this.element.style.transform = `translate3d(${this.initialTranslate.translateX + x}px, ${
				this.initialTranslate.translateY + y
			}px, 0px)`;
		}
	}

	goToInitialPosition() {
		this.canMove = false;
		this.isMoving = false;
		this.element.style.transition = 'transform 300ms ease-in-out';
		this.element.style.removeProperty('transform');

		this.timeoutId = setTimeout(() => {
			this.element.style.zIndex = this.initialZIndex;
			this.canMove = true;
		}, 300) as unknown as number;
	}

	startMovement() {
		if (this.canMove) {
			clearTimeout(this.timeoutId);
			this.timeoutId = -1;
			this.initialTranslate = getTranslateXY(this.element);
			this.isMoving = true;
			this.element.style.zIndex = '999';
			this.element.style.transition = 'none';
		}
	}

	refresh() {
		this.initialZIndex = window.getComputedStyle(this.element).zIndex;
		this.element.style.removeProperty('transform');
		this.isMoving = false;
		this.canMove = true;
	}

	dispose() {
		clearTimeout(this.timeoutId);
		this.element.style.zIndex = this.initialZIndex;
	}
}
