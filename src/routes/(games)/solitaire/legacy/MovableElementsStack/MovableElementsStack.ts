import { MovableStackElement } from './MovableStackElement';

export type MovableElementsStackOptions = {
	canMove?: (index: number) => boolean;
	onDrop?: (index: number, siblingsAmount: number, stackName: string) => void;
	canMoveWithNextSiblings?: (index: number) => boolean;
	getChildNodes?: (element: HTMLElement) => HTMLCollection | null | undefined;
};

export class MovableElementsStack<Name extends string> {
	movableStackElements: MovableStackElement[];
	element: HTMLElement;
	options: MovableElementsStackOptions;
	name: Name;

	static allStacks: MovableElementsStack<string>[] = [];

	constructor(name: Name, element: HTMLElement, options: MovableElementsStackOptions) {
		this.name = name;
		this.element = element;
		this.options = options;
		const children = options.getChildNodes?.(element) || element.children;

		this.movableStackElements = Array.from(children).map((child, index) => {
			return new MovableStackElement(child as HTMLElement, {
				canMove: () => options.canMove?.(index) ?? false,
				canMoveWithNextSiblings: () => options.canMoveWithNextSiblings?.(index) ?? false,
				onDrop: (event, siblingsAmount) => {
					const target = event.target as HTMLElement;
					const targetBounds = target.getBoundingClientRect();
					const stack = MovableElementsStack.allStacks
						.filter((stack) => stack.name !== this.name)
						.map((stack) => {
							const containerBounds = stack.element.getBoundingClientRect();

							const intersects =
								targetBounds.left < containerBounds.right &&
								targetBounds.right > containerBounds.left &&
								targetBounds.top < containerBounds.bottom &&
								targetBounds.bottom > containerBounds.top;

							if (intersects) {
								const x1 = Math.max(targetBounds.left, containerBounds.left);
								const x2 = Math.min(targetBounds.right, containerBounds.right);
								const y1 = Math.max(targetBounds.top, containerBounds.top);
								const y2 = Math.min(targetBounds.bottom, containerBounds.bottom);

								const area = (x2 - x1) * (y2 - y1);

								return {
									stack,
									area,
								};
							}

							return {
								stack,
								area: 0,
							};
						})
						.sort((a, b) => b.area - a.area)
						.filter((intersection) => intersection.area > 0)[0]?.stack;

					if (stack) {
						return options.onDrop?.(index, siblingsAmount, stack.name) ?? false;
					}
				},
			});
		});

		MovableElementsStack.allStacks.push(this);
	}

	dispose() {
		this.movableStackElements.forEach((element) => element.dispose());
		this.movableStackElements = [];

		MovableElementsStack.allStacks = MovableElementsStack.allStacks.filter(
			(stack) => stack !== this,
		);
	}
}
