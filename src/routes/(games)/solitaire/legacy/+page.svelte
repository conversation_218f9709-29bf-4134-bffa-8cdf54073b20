<script lang="ts">
	import Solitaire from './SolitaireLegacy.svelte';
	import { MetaTags } from 'svelte-meta-tags';
	import PageTransition from '$lib/components/PageTransition.svelte';
	import Footer from '$lib/components/Footer.svelte';
	import GameSoundAttributions from '$lib/components/Attributions/GameSoundAttributions.svelte';
	import { solitaireSoundResources as sounds } from './solitaireSoundResources';
	import { fly } from 'svelte/transition';
	import { onMount } from 'svelte';
	import { page } from '$app/state';
	import InfoSolidIcon from '$lib/components/Icons/InfoSolidIcon.svelte';

	let showBetaAlert: boolean | null = $state(null);

	function hideBetaAlert() {
		showBetaAlert = false;
		sessionStorage.setItem('solitaire-v2-beta-alert-dismissed', new Date().toISOString());
	}

	onMount(() => {
		if (showBetaAlert === null) {
			showBetaAlert = sessionStorage.getItem('solitaire-v2-beta-alert-dismissed') === null;
		}
	});

	$effect.pre(() => {
		if (page.url.searchParams.get('no-alert') !== null) {
			showBetaAlert = false;
		}
	});
</script>

<MetaTags
	title="Play Solitaire (Legacy) Online for Free"
	titleTemplate="%s | Lofi and Games"
	description="Play solitaire online for free. Beautiful solitaire game, delightful gaming experience, no download nor registration is required."
	canonical="https://www.lofiandgames.com/solitaire"
	openGraph={{
		url: 'https://www.lofiandgames.com/solitaire',
		images: [
			{
				url: 'https://www.lofiandgames.com/share-solitaire.png',
				width: 1200,
				height: 630,
				alt: 'Solitaire Game',
			},
		],
		siteName: 'Lofi and Games',
		type: 'game',
	}}
	twitter={{
		cardType: 'summary_large_image',
		title: 'Play Solitaire on Lofi and Games',
		image: 'https://www.lofiandgames.com/share-solitaire.png',
		site: 'https://www.lofiandgames.com/solitaire',
	}}
/>

<PageTransition>
	<Solitaire />

	{#if showBetaAlert}
		<div in:fly={{ y: 20, delay: 300 }} out:fly={{ y: 20 }} class="fixed p-4 bottom-0 w-full">
			<div
				role="alert"
				class="alert shadow-lg w-full mx-auto max-w-[400px] sm:max-w-[700px] flex-col sm:flex-row flex justify-between"
			>
				<InfoSolidIcon class="size-8" />

				<div class="flex flex-col gap-1 text-center items-center sm:items-start grow">
					<h3 class="text-lg font-bold">New Solitaire Version!</h3>
					<div class="text-sm">Enjoy smoother animations and improved gameplay</div>
				</div>

				<div class="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
					<button class="btn btn-sm shrink-0" onclick={hideBetaAlert}> Maybe later </button>
					<a href="/solitaire" class="btn btn-sm btn-primary">Play Now</a>
				</div>
			</div>
		</div>
	{/if}

	<article class="mx-auto">
		<h1 class="mt-16">Solitaire</h1>

		<p>
			Solitaire, or Klondike, is a classic card game that is enjoyed by people of all ages. The game
			is played with a standard deck of 52 cards, and the goal is to move all of the cards from the
			tableau to the foundation piles in order of suit and in ascending order.
		</p>

		<h2>How to Play Solitaire: The Game Rules</h2>

		<p>
			The game begins with 28 cards dealt face-down to the tableau in 7 columns of increasing
			length, with the first column having 1 card and the last column having 7 cards. The remaining
			cards form a stock pile, which can be used to fill any empty spots in the tableau. The
			foundation piles are located at the top of the screen and are initially empty.
		</p>

		<p>
			The player can move cards from one tableau pile to another if they are in descending order and
			in alternating colors. For example, a red 7 can be placed on a black 8. Cards can also be
			moved from the stock pile to the tableau, but only one card at a time.
		</p>

		<p>
			The foundation piles must be built in suit and in ascending order, starting with the Ace and
			ending with the King.
		</p>

		<p>Once all of the cards have been moved to the foundation piles, the game is won.</p>

		<h2>Useful Solitare Game Tips</h2>

		<ul>
			<li>
				Start by moving the Aces to the foundation piles as soon as possible. This will free up
				space in the tableau and make it easier to move other cards around.
			</li>

			<li>
				Keep an eye out for cards that can be moved to the foundation piles. If you spot a card that
				can be moved, do it as soon as possible to avoid blocking other cards.
			</li>

			<li>
				Try to keep the tableau as organized as possible. This will make it easier to spot cards
				that can be moved to the foundation piles.
			</li>

			<li>
				Don't be afraid to use the stock pile. If you can't make any moves in the tableau, use the
				stock pile to fill in any empty spots.
			</li>

			<li>
				If you get stuck, don't give up! Take a break and come back to the game later with a fresh
				perspective.
			</li>
		</ul>

		<h2>Conclusion</h2>

		<p>
			Overall, Solitaire is a fun and challenging game that can be enjoyed by people of all ages.
			With a little patience and strategy, you'll be able to master the game in no time.
		</p>

		<h2>Sound Effects Credits</h2>

		<p>
			The sound effects used on the Solitaire game come from multiple parties. The credits and
			respective licenses are listed below:
		</p>

		<GameSoundAttributions {sounds} />
	</article>

	<Footer />
</PageTransition>
