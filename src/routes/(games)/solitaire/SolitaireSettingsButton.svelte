<script lang="ts">
	import DropdownItem from '$lib/components/Dropdown/DropdownItem.svelte';
	import SettingsIcon from '$lib/components/Icons/SettingsIcon.svelte';
	import Toggle from '$lib/components/Toggle/Toggle.svelte';
	import Dialog from '$lib/components/Dialog.svelte';
	import DropdownContent from '$lib/components/Dropdown/DropdownContent.svelte';
	import type { SolitaireApp } from './game/SolitaireApp.svelte';
	import Collapse from '$lib/components/Collapse/Collapse.svelte';
	import CollapseContent from '$lib/components/Collapse/CollapseContent.svelte';

	interface Props {
		game?: SolitaireApp;
		disabled?: boolean;
	}

	let { game, disabled = false }: Props = $props();
	let shouldShowNextGameWinnableText = $state(false);
	let isOpen = $state(false);

	$effect(() => {
		if (!isOpen) {
			shouldShowNextGameWinnableText = false;
		}
	});
</script>

<button
	class="btn btn-xs flex flex-nowrap md:btn-sm"
	onclick={() => (isOpen = true)}
	aria-label="Show settings"
>
	<SettingsIcon class="size-5" />
</button>

<Dialog bind:isOpen modalBoxClass="sm:w-96! px-0" centered={false}>
	<div class="h-6"></div>

	<DropdownContent class="w-auto p-0 shadow-none *:px-6">
		{#if game?.gameManager?.gameSettings}
			{@const settings = game.gameManager.gameSettings.settings}

			<Collapse open={shouldShowNextGameWinnableText}>
				<DropdownItem>
					<Toggle
						name="toggle winnable games only"
						{disabled}
						bind:checked={settings.winnableGamesOnly}
						onchange={() => {
							shouldShowNextGameWinnableText = settings.winnableGamesOnly;
						}}
					>
						Winnable Games Only
					</Toggle>
				</DropdownItem>

				<CollapseContent asSettings>
					<label class="label flex flex-col items-start gap-4">
						<DropdownItem class="text-sm text-base-content">
							Current game may not be winnable. Start a winnable one now?
						</DropdownItem>

						<button
							class="btn btn-sm btn-primary w-full"
							onclick={() => {
								game?.gameManager?.throttledStartNewGame({ draw: settings.drawAmount });
								isOpen = false;
							}}
						>
							Start a winnable game now
						</button>
					</label>
				</CollapseContent>
			</Collapse>

			<DropdownItem>
				<Toggle
					name="toggle automatic finish"
					{disabled}
					bind:checked={settings.shouldFinishAutomatically}
				>
					Automatic Finish
				</Toggle>
			</DropdownItem>

			<DropdownItem>
				<Toggle name="toggle deck on right" {disabled} bind:checked={settings.alignDeckOnRight}>
					Deck on Right
				</Toggle>
			</DropdownItem>

			<DropdownItem>
				<div
					class={game?.gameManager?.drawAmount === 3 ? 'tooltip' : undefined}
					data-tip={game?.gameManager?.drawAmount === 3 ? 'Unavailable on Draw 3 mode' : undefined}
				>
					<Toggle
						name="toggle align cards on waste pile"
						disabled={disabled || game?.gameManager?.drawAmount === 3}
						bind:checked={settings.alignWasteCards}
					>
						Align Cards on Waste Pile
					</Toggle>
				</div>
			</DropdownItem>

			<Collapse open={settings.enableAnimations}>
				<DropdownItem>
					<Toggle name="toggle animations" {disabled} bind:checked={settings.enableAnimations}>
						Animations
					</Toggle>
				</DropdownItem>

				<CollapseContent asSettings>
					<label class="label py-2 flex items-center justify-between gap-4">
						<DropdownItem class="text-sm text-base-content">Speed</DropdownItem>

						<div class="join grid grid-cols-2">
							<input
								class="join-item btn btn-sm"
								type="radio"
								name="animations-speed"
								aria-label="Normal"
								value="normal"
								bind:group={settings.animationsSpeed}
							/>
							<input
								class="join-item btn btn-sm"
								type="radio"
								name="animations-speed"
								aria-label="Fast"
								value="fast"
								bind:group={settings.animationsSpeed}
							/>
						</div>
					</label>

					<DropdownItem>
						<Toggle name="toggle bouncy cards" {disabled} bind:checked={settings.bouncyCards}>
							Bouncy Cards
						</Toggle>
					</DropdownItem>
				</CollapseContent>
			</Collapse>

			<Collapse open={settings.enableAutoPlayHint}>
				<DropdownItem>
					<Toggle
						name="toggle auto-play hint"
						{disabled}
						bind:checked={settings.enableAutoPlayHint}
					>
						Auto-play Hint
					</Toggle>
				</DropdownItem>

				<CollapseContent asSettings class="gap-0">
					<DropdownItem>
						<Toggle
							name="toggle auto-play hint from deck"
							{disabled}
							bind:checked={settings.autoPlayHintFromDeck}>From deck to waste pile</Toggle
						>
					</DropdownItem>
					<DropdownItem>
						<Toggle
							name="toggle auto-play hint from waste to deck"
							{disabled}
							bind:checked={settings.autoPlayHintFromWasteToDeck}>From waste pile to deck</Toggle
						>
					</DropdownItem>
					<DropdownItem>
						<Toggle
							name="toggle auto-play hint from waste to foundation"
							{disabled}
							bind:checked={settings.autoPlayHintFromWasteToFoundation}
						>
							From waste pile to foundation
						</Toggle>
					</DropdownItem>
					<DropdownItem>
						<Toggle
							name="toggle auto-play hint from waste to tableau"
							{disabled}
							bind:checked={settings.autoPlayHintFromWasteToTableau}
						>
							From waste pile to tableau
						</Toggle>
					</DropdownItem>
					<DropdownItem>
						<Toggle
							name="toggle auto-play hint from tableau to foundation"
							{disabled}
							bind:checked={settings.autoPlayHintFromTableauToFoundation}
						>
							From tableau to foundation
						</Toggle>
					</DropdownItem>
					<DropdownItem>
						<Toggle
							name="toggle auto-play hint from tableau to tableau"
							{disabled}
							bind:checked={settings.autoPlayHintFromTableauToTableau}
						>
							From tableau to tableau
						</Toggle>
					</DropdownItem>
				</CollapseContent>
			</Collapse>
		{/if}
	</DropdownContent>
</Dialog>
