import { Application, isWebGLSupported, isWebGPUSupported, Text } from 'pixi.js';
import { AnimationSystem } from '../systems/AnimationSystem';
import { CardGameAssetsManager } from '../assets/CardGameAssetsManager';
import { GameManager } from '../entities/GameManager.svelte';
import { solitaireBreakpoints } from '../entities/SolitaireGameLayout';
import { getElementSize } from '$lib/functions/getElementSize';
import throttle from 'lodash/throttle';

interface SolitaireAppOptions {
	canvas: HTMLCanvasElement;
	showFPS: boolean;
}

export class SolitaireApp {
	private canvas: HTMLCanvasElement;
	private app?: Application;
	private animationSystem = new AnimationSystem();
	private assetsManager = new CardGameAssetsManager();
	gameManager: GameManager | null = $state(null);
	private throttledResize: () => void;
	private fps = new Text();
	private _isLoading = $state(true);
	error?: 'webgl-not-supported' | 'device-error' = $state();

	constructor({ canvas, showFPS }: SolitaireAppOptions) {
		this.canvas = canvas;
		this.throttledResize = throttle(this.resize, 300, { leading: false, trailing: true });
		this.init();
		this.fps.visible = showFPS;
	}

	private async init() {
		this.initListeners();
		this._isLoading = true;

		try {
			if (isWebGLSupported(true) || (await isWebGPUSupported())) {
				this.error = undefined;
			} else {
				this.error = 'webgl-not-supported';
			}
		} catch (error: any) {
			this.error = 'webgl-not-supported';
			console.error(error);
		}

		if (this.error) {
			this._isLoading = false;
			return;
		}

		try {
			this.app = new Application();

			const isOnChromebook = /CrOS/.test(navigator.userAgent);

			await this.app.init({
				canvas: this.canvas,
				resizeTo: this.canvas.parentElement!,
				backgroundColor: getComputedStyle(this.canvas).backgroundColor,
				resolution: window.devicePixelRatio,
				autoDensity: true,
				antialias: false,
				roundPixels: false,
				preference: isOnChromebook ? 'webgl' : 'webgpu',
				failIfMajorPerformanceCaveat: false,
			});

			this.app.stage.sortableChildren = true;
		} catch (error: any) {
			this.error = 'device-error';
			this._isLoading = false;
			console.error(error);
			return;
		}

		if (import.meta.env.DEV) {
			// eslint-disable-next-line @typescript-eslint/no-explicit-any
			(globalThis as any).__PIXI_APP__ = this.app;
		}

		try {
			await this.assetsManager.loadAndUse(
				this.app.screen.width >= solitaireBreakpoints.tablet ? 'desktop' : 'mobile',
			);
		} catch (error: any) {
			this.error = 'device-error';
			this._isLoading = false;
			console.error(error);
			return;
		}

		this._isLoading = false;

		this.gameManager = new GameManager({
			app: this.app,
			assetsManager: this.assetsManager,
			animationSystem: this.animationSystem,
		});

		this.app.stage.addChild(this.gameManager.view);

		let minFps = Infinity;
		this.app.stage.addChild(this.fps);
		let lastFPSUpdate = 0;
		let lastMinFPSReset = 0;

		this.app.ticker.add(() => {
			const now = performance.now();

			if (this.fps.visible) {
				if (now - lastFPSUpdate > 50) {
					const newFps = Math.floor(this.app?.ticker.FPS ?? 0);
					if (now - lastMinFPSReset > 2000) {
						minFps = newFps;
						lastMinFPSReset = now;
					} else if (minFps > newFps) {
						minFps = newFps;
					}
					this.fps.text = `${newFps} - ${minFps}`;
					lastFPSUpdate = now;
				}
			}

			this.animationSystem.update(now);
		});

		this.resize();
	}

	get isLoading() {
		return this._isLoading;
	}

	private resize = async () => {
		if (!this.app?.renderer) {
			return;
		}
		const canvas = this.canvas;
		const parent = canvas.parentElement!;
		const { width, height } = getElementSize(parent);

		this.canvas.style.width = `${width}px`;
		this.canvas.style.height = `${height}px`;

		// Update renderer  and navigation screens dimensions
		this.app?.renderer.resize(width, height);

		const currentSize = this.assetsManager.size;

		await this.assetsManager.loadAndUse(
			this.app?.screen.width >= solitaireBreakpoints.tablet ? 'desktop' : 'mobile',
		);

		// Check again because the app can be destroyed at this moment
		if (!this.app?.canvas || !this.app?.renderer) {
			return;
		}

		this.gameManager?.resize({
			reloadTextures: currentSize !== this.assetsManager.size,
		});

		this.fps.x = 100;
		this.fps.y = height - 50;
	};

	onKeyDown = (event: KeyboardEvent) => {
		if (['INPUT', 'TEXTAREA', 'SELECT'].includes((event.target as HTMLElement).tagName)) {
			return;
		}

		const key = event.key.toLocaleLowerCase();

		if (key === 'f' && event.shiftKey) {
			this.fps.visible = !this.fps.visible;
		}
	};

	private onWebGLContextLost = (event: Event) => {
		event.preventDefault();
		this.dispose();
		this.error = 'device-error';
	};

	private onWebGLContextRestored = (event: Event) => {
		this.init();
	};

	private initListeners() {
		this.canvas.addEventListener('webglcontextlost', this.onWebGLContextLost);
		this.canvas.addEventListener('webglcontextrestored', this.onWebGLContextRestored);
		window.addEventListener('resize', this.throttledResize);
		window.addEventListener('orientationchange', this.throttledResize);
		window.addEventListener('keydown', this.onKeyDown);
	}

	async dispose() {
		this.canvas.removeEventListener('webglcontextlost', this.onWebGLContextLost);
		this.canvas.removeEventListener('webglcontextrestored', this.onWebGLContextRestored);
		window.removeEventListener('resize', this.throttledResize);
		window.removeEventListener('orientationchange', this.throttledResize);
		window.removeEventListener('keydown', this.onKeyDown);
		this.gameManager?.dispose();
		this.gameManager = null;
		this.animationSystem.reset();
		try {
			this.app?.destroy(true);
		} catch (error) {
			// Ignore
			console.error(error);
		}
		await this.assetsManager.unload();
	}
}
