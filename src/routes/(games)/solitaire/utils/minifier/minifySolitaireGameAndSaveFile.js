// @ts-nocheck

/**
 * Utility file to convert saved solitaire games as JSON to
 * a minified text version. DO NOT CHANGE ANY VALUES!
 * Change only the import statement below and the amountOfFiles const.
 * After minifying, delete the last empty line of each file
 */

import games from './solitaire-turn-1.json' assert { type: "json" };
import fs from 'fs';

const amountOfFiles = 6;
const suits = ['club', 'diamond', 'heart', 'spade'];
const values = [2, 3, 4, 5, 6, 7, 8, 9, 10, 'J', 'Q', 'K', 'A'];

const characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';

export const cardToTextMap = new Map();

suits.forEach((suit, i) => {
	values.forEach((value, j) => {
		const charIndex = j + i * values.length;
		const char = characters[charIndex];

		cardToTextMap.set(`${suit}-${value}`, char);
	});
});

export function minifySolitaireGame(game) {
	const cards = [
		...game.deck,
		...game.stack1,
		...game.stack2,
		...game.stack3,
		...game.stack4,
		...game.stack5,
		...game.stack6,
		...game.stack7,
	]

	return cards
		.map(card => cardToTextMap.get(`${card.suit}-${card.value}`))
		.join('');
}

const maxConvertedGamesPerFile = games.length / amountOfFiles;
const files = Array.from({ length: amountOfFiles }).map((_, i) => fs.openSync(`out-${i}.txt`, 'wx'))

games.forEach((game, i) => {
	const result = minifySolitaireGame(game.game);
	const outFile = files[Math.floor(i / maxConvertedGamesPerFile)]
	fs.writeFileSync(outFile, result + "\n");
})