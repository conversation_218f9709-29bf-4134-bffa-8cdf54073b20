import type { CardSuit, CardValue } from '$lib/models/card-game';
import type { SolitaireGame } from '../../model/SolitaireGame';

const suits: CardSuit[] = ['club', 'diamond', 'heart', 'spade'];
const values: CardValue[] = [2, 3, 4, 5, 6, 7, 8, 9, 10, 'J', 'Q', 'K', 'A'];

const characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';

export const cardToTextMap = new Map();

suits.forEach((suit, i) => {
	values.forEach((value, j) => {
		const charIndex = j + i * values.length;
		const char = characters[charIndex];

		cardToTextMap.set(`${suit}-${value}`, char);
	});
});

export function minifySolitaireGame(game: SolitaireGame) {
	const cards = [
		...game.deck,
		...game.stack1,
		...game.stack2,
		...game.stack3,
		...game.stack4,
		...game.stack5,
		...game.stack6,
		...game.stack7,
	];

	return cards.map((card) => cardToTextMap.get(`${card.suit}-${card.value}`)).join('');
}
