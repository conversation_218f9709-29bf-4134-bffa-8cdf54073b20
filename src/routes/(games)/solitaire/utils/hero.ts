import { Container } from 'pixi.js';
import { Easing, Tween } from '@tweenjs/tween.js';

export interface HeroParams {
	target: Container;
	duration?: number;
	easingFunction?: Parameters<Tween['easing']>[0];
	updateToFinalPosition: (target: Container) => void;
}

export function hero({
	target,
	duration = 300,
	updateToFinalPosition,
	easingFunction = Easing.Quadratic.InOut,
}: HeroParams): Tween {
	const initialGlobalPosition = target.getGlobalPosition();

	updateToFinalPosition(target);

	// Calculate the initial position in local space
	const initialLocalPosition = target.parent.toLocal(
		initialGlobalPosition,
		undefined,
		undefined,
		true,
	);

	// Use the current local position as the final position
	const finalLocalPosition = target.position.clone();

	const tween = new Tween({
		x: initialLocalPosition.x,
		y: initialLocalPosition.y,
	})
		.to(
			{
				x: finalLocalPosition.x,
				y: finalLocalPosition.y,
			},
			duration,
		)
		.easing(easingFunction)
		.onUpdate(({ x, y }) => {
			target.position.set(x, y);
		});

	return tween;
}
