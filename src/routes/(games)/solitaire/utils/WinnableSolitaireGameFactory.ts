import { getRandomItemAt } from '$lib/functions/getRandomItemAt';
import { SolitaireGame } from '../model/SolitaireGame';
import { unminifySolitaireGame } from './minifier/unminifySolitaireGame';

export class WinnableSolitaireGameFactory {
	private draw1MinifiedGames: string[] = [];
	private draw3MinifiedGames: string[] = [];

	async build(draw: 1 | 3): Promise<SolitaireGame> {
		if (draw === 3 && this.draw3MinifiedGames.length > 0) {
			return unminifySolitaireGame(getRandomItemAt(this.draw3MinifiedGames), 3);
		}

		if (draw === 1 && this.draw1MinifiedGames.length > 0) {
			return unminifySolitaireGame(getRandomItemAt(this.draw1MinifiedGames), 1);
		}

		let allGamesText: string | undefined;

		if (draw === 3) {
			allGamesText = await fetch(
				'https://static.lofiandgames.com/generated-games/solitaire/draw-3.txt',
			).then((res) => res.text());
		} else {
			const part = getRandomItemAt([0, 1, 2, 3, 4, 5]);

			switch (part) {
				case 0: {
					allGamesText = await fetch(
						'https://static.lofiandgames.com/generated-games/solitaire/draw-1-pt-0.txt',
					).then((res) => res.text());
					break;
				}
				case 1: {
					allGamesText = await fetch(
						'https://static.lofiandgames.com/generated-games/solitaire/draw-1-pt-1.txt',
					).then((res) => res.text());
					break;
				}
				case 2: {
					allGamesText = await fetch(
						'https://static.lofiandgames.com/generated-games/solitaire/draw-1-pt-2-v2.txt',
					).then((res) => res.text());
					break;
				}
				case 3: {
					allGamesText = await fetch(
						'https://static.lofiandgames.com/generated-games/solitaire/draw-1-pt-3.txt',
					).then((res) => res.text());
					break;
				}
				case 4: {
					allGamesText = await fetch(
						'https://static.lofiandgames.com/generated-games/solitaire/draw-1-pt-4.txt',
					).then((res) => res.text());
					break;
				}
				case 5: {
					allGamesText = await fetch(
						'https://static.lofiandgames.com/generated-games/solitaire/draw-1-pt-5.txt',
					).then((res) => res.text());
					break;
				}
			}
		}

		if (!allGamesText) {
			throw new Error('Failed to get winnable solitaire game');
		}

		let allGames = allGamesText.split('\n').map((line) => line.replace('\r', ''));

		if (draw === 3) {
			this.draw3MinifiedGames = allGames;
		} else {
			this.draw1MinifiedGames = allGames;
		}

		return unminifySolitaireGame(getRandomItemAt(allGames), draw);
	}
}
