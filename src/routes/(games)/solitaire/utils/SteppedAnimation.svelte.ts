import { type Ticker, type Application } from 'pixi.js';

type SteppedAnimationStrategy = 'ticker' | 'timeout';

interface Params {
	app: Application;
	frameDuration: number;
	strategy: SteppedAnimationStrategy;
	onComplete: () => void;
	/** @returns true if there is a next step */
	onNext: () => boolean;
}

export class SteppedAnimation {
	app: Application;
	frameDuration = 15;
	private strategy: SteppedAnimationStrategy;
	private timeSinceLastRender = $state(-1);
	private _timeoutId = -1;
	private _isFinished = $state(false);
	private onNext: () => boolean;
	private onComplete: () => void;

	constructor({ app, frameDuration, strategy, onNext, onComplete }: Params) {
		this.app = app;
		this.strategy = strategy;
		this.frameDuration = frameDuration;
		this.onNext = onNext;
		this.onComplete = onComplete;
	}

	isPlaying() {
		return this.timeSinceLastRender !== -1;
	}

	isFinished() {
		return this._isFinished;
	}

	start() {
		if (this.isPlaying()) {
			return;
		}
		this.timeSinceLastRender = this.frameDuration;
		this._isFinished = false;

		if (this.strategy === 'ticker') {
			this.app.ticker.add(this.animateTicker);
		} else {
			this.animateTimeout();
		}
	}

	stop() {
		clearTimeout(this._timeoutId);
		this._timeoutId = -1;

		try {
			this.app.ticker.remove(this.animateTicker);
		} catch {
			// Ignore
		}
		this.timeSinceLastRender = -1;
	}

	private animateTicker = (ticker: Ticker) => {
		this.timeSinceLastRender += ticker.deltaMS;

		if (this.timeSinceLastRender > 0 && this.timeSinceLastRender < this.frameDuration) {
			return;
		}

		this.timeSinceLastRender = 0;

		this.handleAnimation();
	};

	private animateTimeout = () => {
		this._timeoutId = setTimeout(() => {
			if (this.handleAnimation()) {
				this.animateTimeout();
			}
		}, this.frameDuration) as unknown as number;
	};

	private handleAnimation(): boolean {
		if (!this.onNext()) {
			this.stop();
			this._isFinished = true;
			this.onComplete();
			return false;
		}

		return true;
	}

	dispose() {
		this._isFinished = false;
		this.stop();
	}
}
