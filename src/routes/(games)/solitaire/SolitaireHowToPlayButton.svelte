<script>
	import Dialog from '$lib/components/Dialog.svelte';
	import { isMacLike } from '$lib/functions/isMacLike';
	import { onMount } from 'svelte';

	let isOpen = $state(false);
	let isMac = $state(false);

	onMount(() => {
		isMac = isMacLike();
	});
</script>

<button class="btn btn-xs md:btn-sm" onclick={() => (isOpen = true)}
	>How <span class="hidden sm:inline">to play</span>
</button>

<Dialog bind:isOpen modalBoxClass="sm:max-w-2xl">
	<article>
		<h2>How to Play Solitaire</h2>

		<h3>Setup</h3>

		<p>
			<strong>Tableau:</strong> Seven piles of cards, where the top card of each pile is face-up, while
			the other cards are face-down. The first pile has one card, the second has two cards, and so on
			until the seventh pile has seven cards.
		</p>
		<p>
			<strong>Stock:</strong> The leftover cards after setting up the tableau form the stock pile. You'll
			draw from these cards to reveal new playable cards.
		</p>
		<p>
			<strong>Waste:</strong> The pile you'll place the cards after drawing from the stock pile.
		</p>
		<p>
			<strong>Foundations:</strong> Four empty piles where you will build up cards by suit, from Ace
			to King.
		</p>

		<figure>
			<picture>
				<source
					media="(max-width: 767px)"
					srcset="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-1-sm.avif"
				/>
				<source
					media="(min-width: 768px)"
					srcset="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-1.avif"
				/>
				<source
					media="(max-width: 767px)"
					srcset="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-1-sm.png"
				/>
				<source
					media="(min-width: 768px)"
					srcset="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-1.png"
				/>
				<img
					draggable="false"
					src="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-1.png"
					alt="Solitaire setup"
					loading="lazy"
					class="rounded-md"
				/>
			</picture>
			<figcaption class="text-center">Solitaire Setup</figcaption>
		</figure>

		<h3>Gameplay</h3>
		<p>
			<strong>Stock & Waste:</strong> Click the stock pile to draw new cards. The revealed cards are
			moved to the waste pile.
		</p>

		<figure>
			<picture>
				<source
					media="(max-width: 767px)"
					srcset="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-2-sm.avif"
				/>
				<source
					media="(min-width: 768px)"
					srcset="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-2.avif"
				/>
				<source
					media="(max-width: 767px)"
					srcset="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-2-sm.png"
				/>
				<source
					media="(min-width: 768px)"
					srcset="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-2.png"
				/>
				<img
					draggable="false"
					src="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-2.png"
					alt="Clicking on the Stock pile to reveal the Ace of Diamonds"
					loading="lazy"
					class="rounded-md"
				/>
			</picture>
			<figcaption class="text-center">
				Clicking on the Stock pile to reveal the Ace of Diamonds
			</figcaption>
		</figure>

		<p>
			<strong>Move cards to the foundation:</strong> Drag cards from the tableau or waste to the foundation
			piles, building them by suit from Ace to King.
		</p>

		<figure>
			<picture>
				<source
					media="(max-width: 767px)"
					srcset="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-3-sm.avif"
				/>
				<source
					media="(min-width: 768px)"
					srcset="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-3.avif"
				/>
				<source
					media="(max-width: 767px)"
					srcset="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-3-sm.png"
				/>
				<source
					media="(min-width: 768px)"
					srcset="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-3.png"
				/>
				<img
					draggable="false"
					src="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-3.png"
					alt="Clicking on the Ace of Diamonds to move it to a foundation pile"
					loading="lazy"
					class="rounded-md"
				/>
			</picture>
			<figcaption class="text-center">
				Clicking on the Ace of Diamonds to move it to a foundation pile
			</figcaption>
		</figure>

		<figure>
			<picture>
				<source
					media="(max-width: 767px)"
					srcset="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-4-sm.avif"
				/>
				<source
					media="(min-width: 768px)"
					srcset="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-4.avif"
				/>
				<source
					media="(max-width: 767px)"
					srcset="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-4-sm.png"
				/>
				<source
					media="(min-width: 768px)"
					srcset="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-4.png"
				/>
				<img
					draggable="false"
					src="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-4.png"
					alt="Moving the 2 of Diamonds on top of the Ace of Diamonds on the foundation pile"
					loading="lazy"
					class="rounded-md"
				/>
			</picture>
			<figcaption class="text-center">
				Moving the 2 of Diamonds on top of the Ace of Diamonds on the foundation pile
			</figcaption>
		</figure>

		<p>
			<strong>Move cards in the tableau:</strong> Drag cards to form descending sequences with alternating
			colors. Kings or stacks starting with Kings can be moved to empty columns. As you clear cards in
			the tableau, face-down cards will be revealed automatically.
		</p>

		<figure>
			<picture>
				<source
					media="(max-width: 767px)"
					srcset="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-5-sm.avif"
				/>
				<source
					media="(min-width: 768px)"
					srcset="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-5.avif"
				/>
				<source
					media="(max-width: 767px)"
					srcset="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-5-sm.png"
				/>
				<source
					media="(min-width: 768px)"
					srcset="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-5.png"
				/>
				<img
					draggable="false"
					src="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-5.png"
					alt="Moving the King of Spades to the empty column on the tableau"
					loading="lazy"
					class="rounded-md"
				/>
			</picture>
			<figcaption class="text-center">
				Moving the King of Spades to the empty column on the tableau
			</figcaption>
		</figure>

		<figure>
			<picture>
				<source
					media="(max-width: 767px)"
					srcset="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-6-sm.avif"
				/>
				<source
					media="(min-width: 768px)"
					srcset="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-6.avif"
				/>
				<source
					media="(max-width: 767px)"
					srcset="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-6-sm.png"
				/>
				<source
					media="(min-width: 768px)"
					srcset="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-6.png"
				/>
				<img
					draggable="false"
					src="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-6.png"
					alt="Moving the Queen of Hearts to the King of Spades on the tableau"
					loading="lazy"
					class="rounded-md"
				/>
			</picture>
			<figcaption class="text-center">
				Moving the Queen of Hearts to the King of Spades on the tableau
			</figcaption>
		</figure>

		<h3>Winning</h3>
		<p>Move all cards to the foundation piles to win the game!</p>

		<figure>
			<picture>
				<source
					media="(max-width: 767px)"
					srcset="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-7-sm.avif"
				/>
				<source
					media="(min-width: 768px)"
					srcset="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-7.avif"
				/>
				<source
					media="(max-width: 767px)"
					srcset="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-7-sm.png"
				/>
				<source
					media="(min-width: 768px)"
					srcset="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-7.png"
				/>
				<img
					draggable="false"
					src="https://static.lofiandgames.com/images/solitaire/tutorial/solitaire-7.png"
					alt="Moving the last card on the tableau, the King of Diamonds, to the top of the foundation"
					loading="lazy"
					class="rounded-md"
				/>
			</picture>
			<figcaption class="text-center">
				Moving the last card on the tableau, the King of Diamonds, to the top of the foundation
			</figcaption>
		</figure>

		<section class="hidden lg:block">
			<h3>Keyboard Shortcuts</h3>

			<div class="overflow-x-auto">
				<table class="table">
					<thead>
						<tr>
							<th>Action</th>
							<th>Keybinding</th>
						</tr>
					</thead>

					<tbody>
						<tr>
							<td>Draw Card</td>
							<td>
								<kbd class="kbd dark:text-base-content">D</kbd>
							</td>
						</tr>
						<tr>
							<td>Show Hint</td>
							<td>
								<kbd class="kbd dark:text-base-content">H</kbd>
							</td>
						</tr>
						<tr>
							<td>Undo</td>
							<td>
								<div class="kbd dark:text-base-content">{isMac ? '⌘' : 'Ctrl'}</div>
								+
								<div class="kbd dark:text-base-content">Z</div>
							</td>
						</tr>
						<tr>
							<td>Redo</td>
							<td>
								<kbd class="kbd dark:text-base-content">{isMac ? '⌘' : 'Ctrl'}</kbd>
								+
								<kbd class="kbd dark:text-base-content">Shift</kbd>
								+
								<kbd class="kbd dark:text-base-content">Z</kbd>
							</td>
						</tr>
						<tr>
							<td>Toggle Pause</td>
							<td>
								<kbd class="kbd dark:text-base-content">P</kbd>
							</td>
						</tr>
						<tr>
							<td>Replay Current Game</td>
							<td>
								<kbd class="kbd dark:text-base-content">Shift</kbd>
								+
								<kbd class="kbd dark:text-base-content">C</kbd>
							</td>
						</tr>
						<tr>
							<td>Play a New Game</td>
							<td>
								<kbd class="kbd dark:text-base-content">Shift</kbd>
								+
								<kbd class="kbd dark:text-base-content">N</kbd>
							</td>
						</tr>
						<tr>
							<td>Play a New Game Drawing 1 Card at a Time</td>
							<td>
								<kbd class="kbd dark:text-base-content">Shift</kbd>
								+
								<kbd class="kbd dark:text-base-content">1</kbd>
							</td>
						</tr>
						<tr>
							<td>Play a New Game Drawing 3 Cards at a Time</td>
							<td>
								<kbd class="kbd dark:text-base-content">Shift</kbd>
								+
								<kbd class="kbd dark:text-base-content">3</kbd>
							</td>
						</tr>
						<tr>
							<td>Play Daily Game</td>
							<td>
								<kbd class="kbd dark:text-base-content">Shift</kbd>
								+
								<kbd class="kbd dark:text-base-content">D</kbd>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
		</section>
	</article>
</Dialog>
