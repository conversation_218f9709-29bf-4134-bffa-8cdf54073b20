import { type Card, type CardSuit, type CardValue } from '$lib/models/card-game';
import {
	Assets,
	Spritesheet,
	Texture,
	type SpritesheetData,
	type SpritesheetFrameData,
} from 'pixi.js';

type GameCardDownVariant = 'default';
type GameCardUpVariant = 'default';
type GameCardSlotVariant = 'default';
type SizeVariant = 'mobile' | 'desktop';

export class CardGameAssetsManager {
	size: SizeVariant = 'desktop';
	private spritesheets: Record<SizeVariant, Spritesheet<typeof desktopAtlasData> | null> = {
		mobile: null,
		desktop: null,
	};

	get atlas() {
		return this.size === 'desktop' ? desktopAtlasData : mobileAtlasData;
	}

	get spritesheet() {
		return this.spritesheets[this.size];
	}

	async loadAndUse(size: SizeVariant) {
		this.size = size;
		const image = this.atlas.meta.image;

		if (this.spritesheets[size] !== null) {
			return;
		}

		await Assets.load(image);

		this.spritesheets[size] = new Spritesheet(Texture.from(image), this.atlas);

		await this.spritesheets[size].parse();
	}

	async unload() {
		const promises = [];
		if (this.spritesheets.desktop) {
			promises.push(Assets.unload(desktopAtlasData.meta.image));
		}
		if (this.spritesheets.mobile) {
			promises.push(Assets.unload(mobileAtlasData.meta.image));
		}

		return Promise.all(promises);
	}

	/** @todo Implement variant */
	upTexture(card: Pick<Card, 'value' | 'suit'>, _variant: GameCardUpVariant = 'default'): Texture {
		return this.spritesheet!.textures[`${card.suit}-${card.value}`];
	}

	/** @todo Implement variant */
	downTexture(_variant: GameCardDownVariant = 'default') {
		return this.spritesheet!.textures.back;
	}

	/** @todo Implement variant */
	slotTexture(_variant: GameCardSlotVariant = 'default') {
		return this.spritesheet!.textures.slot;
	}

	/** @todo Implement variant */
	deckTexture(_variant: GameCardSlotVariant = 'default') {
		return this.spritesheet!.textures.deck;
	}

	/** @todo Implement variant */
	foundationTexture(_variant: GameCardSlotVariant = 'default') {
		return this.spritesheet!.textures.foundation;
	}
}

interface GetAtlasDataParams {
	cardWidth: number;
	cardHeight: number;
	gap: number;
	image: string;
}

function getAtlasData({ cardWidth, cardHeight, gap, image }: GetAtlasDataParams) {
	const suits: CardSuit[] = ['diamond', 'heart', 'spade', 'club'];
	const values: CardValue[] = [2, 3, 4, 5, 6, 7, 8, 9, 10, 'A', 'J', 'Q', 'K'];

	const framesArray = suits.flatMap((suit, row) => {
		return values.map((value, column) => {
			return {
				name: `${suit}-${value}` as `${CardSuit}-${CardValue}`,
				value: {
					x: column * (cardWidth + gap),
					y: row * (cardHeight + gap),
					w: cardWidth,
					h: cardHeight,
				},
			};
		});
	});

	const cardFrontFrames = framesArray.reduce(
		(object, frame) => {
			object[frame.name] = {
				frame: frame.value,
				rotated: false,
				trimmed: false,
				spriteSourceSize: { x: 0, y: 0, w: frame.value.w, h: frame.value.h },
				sourceSize: { w: frame.value.w, h: frame.value.h },
			};

			return object;
		},
		{} as Record<`${CardSuit}-${CardValue}`, SpritesheetFrameData>,
	);

	return {
		frames: {
			...cardFrontFrames,
			slot: {
				frame: {
					x: 0,
					y: 4 * (cardHeight + gap),
					w: cardWidth,
					h: cardHeight,
				},
			},
			deck: {
				frame: {
					x: cardWidth + gap,
					y: 4 * (cardHeight + gap),
					w: cardWidth,
					h: cardHeight,
				},
			},
			foundation: {
				frame: {
					x: (cardWidth + gap) * 2,
					y: 4 * (cardHeight + gap),
					w: cardWidth,
					h: cardHeight,
				},
			},
			back: {
				frame: {
					x: (cardWidth + gap) * 3,
					y: 4 * (cardHeight + gap),
					w: cardWidth,
					h: cardHeight,
				},
			},
		},
		meta: {
			image,
			format: 'RGBA8888',
			size: { w: (cardWidth + gap) * 13, h: (cardHeight + gap) * 5 },
			scale: 1,
		},
	} satisfies SpritesheetData;
}

const desktopScale = 2;

const desktopAtlasData = getAtlasData({
	cardWidth: 135 * desktopScale,
	cardHeight: 184 * desktopScale,
	gap: 5 * desktopScale,
	image: 'https://static.lofiandgames.com/images/solitaire/cards/desktop/cards-2x.png',
});

const mobileScale = 3;

const mobileAtlasData = getAtlasData({
	cardWidth: 39 * mobileScale,
	cardHeight: 53 * mobileScale,
	gap: 5 * mobileScale,
	image: 'https://static.lofiandgames.com/images/solitaire/cards/mobile/cards-3x.png',
});
