import { Container, Graphics, type Application } from 'pixi.js';
import type { CardGameAssetsManager } from '../assets/CardGameAssetsManager';
import type { AnimationSystem } from '../systems/AnimationSystem';
import { SolitaireGameLayout } from './SolitaireGameLayout';
import { GameCard } from './GameCard';
import { getStandardDeck } from '$lib/functions/getStandardDeck';
import type { Card, CardSuit, CardValue } from '$lib/models/card-game';
import { getCardId } from '../utils/getCardId';
import {
	SolitaireGame,
	type CardHint,
	type GameStateChange,
	type SolitaireSpeed,
	type SolitaireStack,
} from '../model/SolitaireGame';
import { Undoable } from '$lib/util/Undoable/Undoable.svelte';
import { UndoableKeyboardListener } from '$lib/util/Undoable/UndoableKeyboardListener';
import { Easing, Tween } from '@tweenjs/tween.js';
import { shake } from '../utils/shake';
import { FinishAutomaticallyAnimation } from '../animations/FinishAutomaticallyAnimation';
import { WinningAnimation } from '../animations/WinningAnimation';
import { wait } from '$lib/functions/wait';
import { DistributeCardsAnimation } from '../animations/DistributeCardsAnimation';
import throttle from 'lodash/throttle';
import { solitaireSoundResources } from '../assets/solitaireSoundResources';
import { WinnableSolitaireGameFactory } from '../utils/WinnableSolitaireGameFactory';
import { Stats } from '$lib/util/Stats.svelte';
import { untrack } from 'svelte';
import { unminifySolitaireGame } from '../utils/minifier/unminifySolitaireGame';
import { minifySolitaireGame } from '../utils/minifier/minifySolitaireGame';
import { supabase } from '$lib/api/supabase';
import { GameContext } from '$lib/util/GameContext/GameContext.svelte';
import { Leaderboard } from '$lib/util/Leaderboard.svelte';

export type WinnableGameFetchState = 'idle' | 'loading' | 'error' | 'success';

type GameMode = 'random' | 'winnable' | 'debug' | 'daily';

interface GameManagerParams {
	app: Application;
	assetsManager: CardGameAssetsManager;
	animationSystem: AnimationSystem;
}

type Settings = {
	newGameThrottleDelay: number;
	cardActionThrottleDelay: number;
	undoAndRedoThrottleDelay: number;
	moveDuration: number;
	flipDuration: number;
	bouncyMoveDuration: number;
	bouncyFlipDuration: number;
	shakeDuration: number;
	hintGlowDuration: number;
	hintDuration: number;
	finishAutomaticallyTickDuration: number;
	distributeCardsTickDuration: number;
	couldNotFindAnyHintDuration: number;
};

const settingsMap: Record<SolitaireSpeed | 'off', Settings> = {
	normal: {
		newGameThrottleDelay: 1000,
		cardActionThrottleDelay: 250,
		undoAndRedoThrottleDelay: 150,
		moveDuration: 300,
		flipDuration: 250,
		bouncyMoveDuration: 420,
		bouncyFlipDuration: 300,
		shakeDuration: 200,
		hintGlowDuration: 400,
		hintDuration: 1200,
		finishAutomaticallyTickDuration: 60,
		distributeCardsTickDuration: 35,
		couldNotFindAnyHintDuration: 2000,
	},
	fast: {
		newGameThrottleDelay: 1000,
		cardActionThrottleDelay: 100,
		undoAndRedoThrottleDelay: 50,
		moveDuration: 100,
		flipDuration: 80,
		bouncyMoveDuration: 150,
		bouncyFlipDuration: 120,
		shakeDuration: 40,
		hintGlowDuration: 120,
		hintDuration: 600,
		couldNotFindAnyHintDuration: 1000,
		distributeCardsTickDuration: 5,
		finishAutomaticallyTickDuration: 10,
	},
	off: {
		newGameThrottleDelay: 1000,
		cardActionThrottleDelay: 0,
		undoAndRedoThrottleDelay: 0,
		moveDuration: 0,
		flipDuration: 0,
		bouncyMoveDuration: 0,
		bouncyFlipDuration: 0,
		shakeDuration: 0,
		hintGlowDuration: 0,
		hintDuration: 600,
		couldNotFindAnyHintDuration: 1000,
		distributeCardsTickDuration: 0,
		finishAutomaticallyTickDuration: 0,
	},
};

export class GameManager {
	view = new Container();
	private app: Application;
	private layout: SolitaireGameLayout;
	private game = $state() as SolitaireGame;
	private cards: Record<`${CardSuit}-${CardValue}`, GameCard<Card>>;
	private animationSystem: AnimationSystem;
	private assetsManager: CardGameAssetsManager;
	throttledDrawCards = this.drawCards;
	throttledOnCardTap = this.onCardTap;
	throttledOnUndo = this.onUndo;
	throttledOnRedo = this.onRedo;
	throttledStartNewGame = this.startNewGame;
	throttledRestartCurrentGame = this.restartCurrentGame;
	throttledOnCardsDrop = this.onCardsDrop;
	throttledShowHint = this.showHint;
	private history: Undoable<GameStateChange>;
	private undoableKeyboardListener: UndoableKeyboardListener;
	private hint: CardHint | null = $state(null);
	private hintStateChange: GameStateChange | null = $state(null);
	private hintBorder = new Graphics();
	private _settings: Settings = $state(settingsMap.normal);
	private finishAutomaticallyAnimation: FinishAutomaticallyAnimation;
	private distributeCardsAnimation: DistributeCardsAnimation;
	private winningAnimation: WinningAnimation | null = null;
	private _isFinishing = $state(false);
	private _isStarting = $state(false);
	private _mode = $state('random' as GameMode);
	private _couldNotFindAnyHint = $state(false);
	private couldNotFindAnyHintTimeout = -1;
	private _totalMoves = $state(0);
	private _winnableGameFetchState: WinnableGameFetchState = $state('idle');
	private winnableSolitaireGameFactory = new WinnableSolitaireGameFactory();
	private cleanUpSettingsListener: () => void;
	private _lastPlayedGames: string[] = $state([]);
	private _hintsPlayed = $state(0);
	context = new GameContext({
		gameKey: 'solitaire',
		sounds: {
			resources: solitaireSoundResources,
		},
		settings: {
			defaultSettings: {
				drawAmount: 1 as 1 | 3,
				shouldFinishAutomatically: true,
				winnableGamesOnly: false,
				alignDeckOnRight: false,
				enableAnimations: true,
				animationsSpeed: 'normal' as SolitaireSpeed,
				enableAutoPlayHint: false,
				autoPlayHintFromDeck: false,
				autoPlayHintFromWasteToDeck: false,
				autoPlayHintFromWasteToFoundation: false,
				autoPlayHintFromWasteToTableau: false,
				autoPlayHintFromTableauToFoundation: false,
				autoPlayHintFromTableauToTableau: false,
				alignWasteCards: false,
				bouncyCards: false,
			},
		},
		formatted: () => {
			return {
				name: 'Solitaire',
				variant: `Draw ${this.drawAmount ?? 1}`,
				leaderboardVariant: `Draw ${this.drawAmount ?? 1}`,
			};
		},
		stats: ({ props }) => {
			return {
				stats: new Stats({
					...props,
					gameVariant: `${this.drawAmount}`,
					liveStats: {
						moves: {
							name: 'Moves',
							unit: 'plain',
							value: () => this.moves,
							metrics: {
								total: {
									key: 'totalMoves',
									name: 'Total Moves',
								},
								average: {
									key: 'averageMoves',
									name: 'Average Moves',
								},
								min: {
									key: 'fewestMoves',
									name: 'Fewest Moves',
									useAsBest: true,
								},
								max: {
									key: 'maxMoves',
									name: 'Max Moves',
								},
							},
						},
						totalMoves: {
							name: 'Total Moves',
							description: 'Includes undos, redos, and automatic finish moves',
							unit: 'plain',
							value: () => this.totalMoves,
							metrics: {
								total: {
									key: 'totalTotalMoves',
									name: 'Total Total Moves',
								},
								average: {
									key: 'averageTotalMoves',
									name: 'Average Total Moves',
								},
								min: {
									key: 'fewestTotalMoves',
									name: 'Fewest Total Moves',
									useAsBest: true,
								},
								max: {
									key: 'maxTotalMoves',
									name: 'Max Total Moves',
								},
							},
						},
					},
					initialPinnedStats: ['time', 'moves'],
				}),
				canUpdateWithGameLost: () => {
					return this.totalMoves >= 10 && !this.isWon;
				},
				visibleStats: [
					'bestTime',
					'averageTime',
					'fewestMoves',
					'averageMoves',
					'fewestTotalMoves',
					'averageTotalMoves',
					'wonGames',
					'totalGames',
				],
			};
		},
		dailyGame: () => {
			return {
				type: 'fetch',
				firstAvailableGameDate: new Date('2025/01/11'),
				async fetchGame(date) {
					const games = (
						await supabase
							.rpc('daily_solitaire', {
								target_date: `${date.getFullYear()}/${date.getMonth() + 1}/${date.getDate()}`,
							})
							.select('game,draw')
							.throwOnError()
					).data;

					if (!games || games.length === 0) {
						throw new Error('Game not found');
					}

					return games[0];
				},
				onPlay: ({ game, draw }) => {
					const instance = unminifySolitaireGame(game, draw);

					this.startNewGame({
						draw,
						dailyGame: instance,
						mode: 'daily',
					});
				},
				onError: () => {
					this.startNewGame({
						draw: this.gameSettings.settings.drawAmount,
						animated: false,
					});
				},
			};
		},
		leaderboard: (context) => {
			return {
				leaderboard: new Leaderboard({
					game: context.gameKey,
					firstAvailableDate: new Date('2025/05/08'),
					hasMoves: true,
					gameVariant: `${this.drawAmount}`,
					order: 'lower-first',
				}),
				sendScoreOn: ['won'],
				getScore: () => {
					return {
						score: this.score,
						moves: this.moves,
					};
				},
			};
		},
		isGameReady: () => {
			return this.winnableGameFetchState !== 'loading';
		},
	});

	constructor({ app, assetsManager, animationSystem }: GameManagerParams) {
		this.app = app;
		this.animationSystem = animationSystem;
		this.assetsManager = assetsManager;

		this.cards = getStandardDeck()
			.map(
				(card) =>
					({
						key: getCardId(card),
						value: new GameCard<Card>({
							id: getCardId(card),
							face: 'down',
							upTexture: assetsManager.upTexture({
								suit: card.suit,
								value: card.value,
							}),
							downTexture: assetsManager.downTexture(),
							meta: card,
						}),
					}) as const,
			)
			.reduce(
				(acc, current) => {
					acc[current.key] = current.value;
					return acc;
				},
				{} as typeof this.cards,
			);

		this.game = new SolitaireGame(1, true);
		this.history = new Undoable<GameStateChange>({
			game: this.game,
			changedStacks: [],
			movingCards: [],
		});
		this.undoableKeyboardListener = new UndoableKeyboardListener(
			() => this.throttledOnUndo(),
			() => this.throttledOnRedo(),
		);
		this.layout = new SolitaireGameLayout({
			app,
			assetsManager,
			animationSystem,
			cards: this.cards,
			callbacks: {
				onDeckTap: () => this.throttledDrawCards(),
				onCardTap: (card, index, stack) => this.throttledOnCardTap(card, index, stack),
				onCardsDrop: (cardIndex, from, to) => this.throttledOnCardsDrop(cardIndex, from, to),
				onDragStart: () => this.canHandleUserEvents,
			},
		});
		this.distributeCardsAnimation = new DistributeCardsAnimation({
			app,
			layout: this.layout,
			tickDuration: this.settings.distributeCardsTickDuration,
			onComplete: async (game) => {
				this.game = game;
				this.history.reset({
					game: this.game,
					changedStacks: [],
					movingCards: [],
				});
				this._lastPlayedGames.push(`${game.amountOfCardsToDraw}-${minifySolitaireGame(game)}`);
				this._isStarting = false;

				await wait(this.moveDuration);
				this.sounds.deckShuffle.stop();
			},
		});
		this.finishAutomaticallyAnimation = new FinishAutomaticallyAnimation({
			app,
			layout: this.layout,
			tickDuration: this.settings.finishAutomaticallyTickDuration,
			onMoveCard: () => {
				this._totalMoves += 1;
			},
			onComplete: async (game) => {
				this.sounds.deckShuffle.loop(false);

				await wait(this.moveDuration);

				if (!this._isFinishing) {
					return;
				}

				this.game = game;

				this.sounds.deckShuffle.stop();
				this.sounds.applause.stopAndPlay();

				this.winningAnimation?.dispose();

				this.winningAnimation = new WinningAnimation({
					app: this.app,
					cards: this.cards,
					onComplete: () => {
						this.sounds.applause.stop();
						this._isFinishing = false;
					},
				});

				this.winningAnimation.start(this.game);

				this.context.handleGameOver('won', { handleConfetti: false });
			},
		});

		this.view.addChild(this.layout.view);
		this.addListeners();

		this.cleanUpSettingsListener = $effect.root(() => {
			$effect(() => {
				this.syncSettings();
			});
		});

		this.context.load();

		if (
			!this.context.dailyGame!.isFetching &&
			!this.context.dailyGame!.dateOfLastSuccessfullyPlayedGame
		) {
			this.startNewGame({
				draw: this.gameSettings.settings.drawAmount,
				animated: false,
			});
		}
	}

	get lastPlayedGames() {
		return this._lastPlayedGames;
	}

	get sounds() {
		return this.context.sounds;
	}

	get timer() {
		return this.context.timer;
	}

	get isFinishing() {
		return this._isFinishing;
	}

	get isStarting() {
		return this._isStarting;
	}

	get mode() {
		return this._mode;
	}

	get gameSettings() {
		return this.context.settingsManager;
	}

	private syncSettings() {
		if (!this.gameSettings.settings.enableAnimations) {
			this.settings = settingsMap.off;
		} else {
			this.settings = settingsMap[this.gameSettings.settings.animationsSpeed];
		}
		this.layout.settings = {
			alignDeckOnRight: this.gameSettings.settings.alignDeckOnRight,
			alignNextCards:
				untrack(() => this.drawAmount) === 1 ? this.gameSettings.settings.alignWasteCards : false,
			bouncyCards: this.gameSettings.settings.bouncyCards,
		};
		untrack(() => this.game).shouldFinishAutomatically =
			this.gameSettings.settings.shouldFinishAutomatically;
	}

	get winnableGameFetchState() {
		return this._winnableGameFetchState;
	}

	set winnableGameFetchState(newWinnableGameFetchState) {
		this._winnableGameFetchState = newWinnableGameFetchState;
	}

	private get moveDuration() {
		if (this.gameSettings.settings.bouncyCards) {
			return this.settings.bouncyMoveDuration;
		}

		return this.settings.moveDuration;
	}

	private get flipDuration() {
		if (this.gameSettings.settings.bouncyCards) {
			return this.settings.bouncyFlipDuration;
		}

		return this.settings.flipDuration;
	}

	get settings(): Settings {
		return this._settings;
	}

	set settings(newSettings: Settings) {
		this._settings = newSettings;
		this.layout.config.flipDuration = this.flipDuration;
		this.layout.config.moveDuration = this.moveDuration;
		this.finishAutomaticallyAnimation.tickDuration = newSettings.finishAutomaticallyTickDuration;
		this.distributeCardsAnimation.tickDuration = newSettings.distributeCardsTickDuration;
		this.throttledDrawCards = throttle(this.drawCards, this.settings.cardActionThrottleDelay, {
			leading: true,
			trailing: false,
		});
		this.throttledOnCardTap = throttle(this.onCardTap, this.settings.cardActionThrottleDelay, {
			leading: true,
			trailing: false,
		});
		this.throttledOnCardsDrop = throttle(this.onCardsDrop, this.settings.cardActionThrottleDelay, {
			leading: true,
			trailing: false,
		});
		this.throttledOnUndo = throttle(this.onUndo, this.settings.undoAndRedoThrottleDelay, {
			leading: true,
			trailing: false,
		});
		this.throttledOnRedo = throttle(this.onRedo, this.settings.undoAndRedoThrottleDelay, {
			leading: true,
			trailing: false,
		});
		this.throttledStartNewGame = throttle(this.startNewGame, this.settings.newGameThrottleDelay, {
			leading: true,
			trailing: false,
		});
		this.throttledRestartCurrentGame = throttle(
			this.restartCurrentGame,
			this.settings.newGameThrottleDelay,
			{
				leading: true,
				trailing: false,
			},
		);
		this.throttledShowHint = throttle(this.showHint, this.settings.cardActionThrottleDelay, {
			leading: true,
			trailing: false,
		});
	}

	resize({ reloadTextures }: { reloadTextures: boolean }) {
		if (reloadTextures) {
			Object.values(this.cards).forEach((card) => {
				card.upSprite.texture = this.assetsManager.upTexture(card.meta!);
				card.downSprite.texture = this.assetsManager.downTexture();
			});
		}
		this.layout.handleResize({ reloadTextures, game: this.game });
	}

	/** UI methods */

	get totalMoves(): number {
		return this._totalMoves;
	}

	get moves(): number {
		return this.history.timeline.past.length;
	}

	/** Smaller is better */
	get score(): number {
		return this.totalMoves + Math.floor(this.timer.elapsedTime / 1000) + this._hintsPlayed * 10;
	}

	get drawAmount(): 1 | 3 {
		return this.game.amountOfCardsToDraw as 1 | 3;
	}

	get couldNotFindAnyHint() {
		return this._couldNotFindAnyHint;
	}

	set couldNotFindAnyHint(couldNotFindAnyHint: boolean) {
		this._couldNotFindAnyHint = couldNotFindAnyHint;
	}

	canUndo(): boolean {
		if (!this.canHandleUserEvents) {
			return false;
		}
		return this.history.canUndo();
	}

	canRedo(): boolean {
		if (!this.canHandleUserEvents) {
			return false;
		}

		return this.history.canRedo();
	}

	isWon = $derived.by(() => {
		return !this._isStarting && this.distributeCardsAnimation.isFinished() && this.game.isWon();
	});

	canHandleUserEvents = $derived.by(() => {
		return (
			!this.game.isWon() &&
			!this._isFinishing &&
			!this._isStarting &&
			(this.timer.running || !this.timer.started)
		);
	});

	private showHint() {
		if (!this.canHandleUserEvents) {
			return;
		}

		if (this.hint) {
			return;
		}

		clearTimeout(this.couldNotFindAnyHintTimeout);
		this.couldNotFindAnyHint = false;
		this.hint = this.game.getCardHint();

		if (this.hint) {
			this.hintStateChange = this.game.moveFromHint(this.hint);

			if (this.hintStateChange) {
				const {
					enableAutoPlayHint,
					autoPlayHintFromDeck,
					autoPlayHintFromWasteToDeck,
					autoPlayHintFromTableauToFoundation,
					autoPlayHintFromWasteToFoundation,
					autoPlayHintFromWasteToTableau,
					autoPlayHintFromTableauToTableau,
				} = this.gameSettings.settings;

				if (enableAutoPlayHint) {
					const hasTableau = this.hintStateChange.changedStacks.some((changedStack) =>
						(
							[
								'stack1',
								'stack2',
								'stack3',
								'stack4',
								'stack5',
								'stack6',
								'stack7',
							] as SolitaireStack[]
						).includes(changedStack),
					);
					const hasFoundation = this.hintStateChange.changedStacks.some((changedStack) =>
						(['slot1', 'slot2', 'slot3', 'slot4'] as SolitaireStack[]).includes(changedStack),
					);
					const hasDeck = this.hintStateChange.changedStacks.includes('deck');
					const hasWaste = this.hintStateChange.changedStacks.includes('next-cards');

					if (autoPlayHintFromDeck && this.hint.from === 'deck') {
						return this.playHint();
					}

					if (
						autoPlayHintFromWasteToDeck &&
						this.hint.from === 'next-cards' &&
						this.hint.to === 'deck'
					) {
						return this.playHint();
					}

					if (autoPlayHintFromTableauToFoundation && hasTableau && hasFoundation) {
						return this.playHint();
					}

					if (
						autoPlayHintFromWasteToFoundation &&
						this.hint.from === 'next-cards' &&
						hasFoundation
					) {
						return this.playHint();
					}

					if (autoPlayHintFromWasteToTableau && this.hint.from === 'next-cards' && hasTableau) {
						return this.playHint();
					}

					if (
						autoPlayHintFromTableauToTableau &&
						hasTableau &&
						!hasWaste &&
						!hasDeck &&
						!hasFoundation
					) {
						return this.playHint();
					}
				}

				// Animate glow
				this.animationSystem.removeById('clear-hint-glow');
				this.animationSystem.add({
					id: 'hint-glow',
					animation: new Tween({
						alpha: 0,
						distance: 0,
						outerStrength: 0,
						innerStrength: 0,
					})
						.to({ alpha: 1, distance: 15, outerStrength: 4, innerStrength: 1 })
						.onUpdate(({ alpha }) => {
							this.hintBorder.alpha = alpha;
						})
						.easing(Easing.Quadratic.InOut)
						.duration(this.settings.hintGlowDuration),
				});

				this.layout.sync({
					...this.hintStateChange,
					keepCardListeners: true,
				});

				const firstCard = this.cards[getCardId(this.hintStateChange.movingCards[0])];
				const lastCard =
					this.cards[
						getCardId(this.hintStateChange.movingCards[this.hintStateChange.movingCards.length - 1])
					];

				this.hintBorder.removeFromParent();

				const height =
					lastCard.view.getGlobalPosition().y -
					firstCard.view.getGlobalPosition().y +
					lastCard.view.height;

				this.hintBorder
					.clear()
					.roundRect(
						-lastCard.view.width / 2,
						-height + lastCard.view.height / 2,
						lastCard.view.width,
						height,
						8,
					)
					.stroke({
						color: '#fde047',
						width: 4,
					});

				lastCard.view.addChild(this.hintBorder);

				this.scheduleClearHint();
			}
		} else {
			this.couldNotFindAnyHint = true;
			this.couldNotFindAnyHintTimeout = setTimeout(() => {
				this.couldNotFindAnyHint = false;
				this.couldNotFindAnyHintTimeout = -1;
			}, this.settings.couldNotFindAnyHintDuration) as unknown as number;
		}
	}

	/** End UI methods */

	private clearHint(sync = false): GameStateChange | null {
		if (this.hint && this.hintStateChange) {
			const stateChange = this.hintStateChange;

			// Animate glow
			this.animationSystem.add({
				id: 'clear-hint-glow',
				completeOnClear: true,
				animation: new Tween({
					alpha: this.hintBorder.alpha,
				})
					.to({ alpha: 0, distance: 0, outerStrength: 0, innerStrength: 0 })
					.onUpdate(({ alpha }) => {
						this.hintBorder.alpha = alpha;
					})
					.easing(Easing.Quadratic.InOut)
					.duration(this.settings.hintGlowDuration)
					.onComplete(() => {
						this.hintBorder.removeFromParent();
					}),
			});

			if (sync) {
				this.layout.sync({
					...this.hintStateChange,
					game: this.game,
				});
			}
			this.hint = null;
			this.hintStateChange = null;

			return stateChange;
		}

		this.hint = null;
		this.hintStateChange = null;

		return null;
	}

	private scheduleClearHint(duration = this.settings.hintDuration) {
		if (this.hint) {
			const hintToBeCleared = this.hint;

			setTimeout(() => {
				if (hintToBeCleared === this.hint) {
					this.clearHint(true);
				}
			}, duration);
		}
	}

	private async checkGameWin() {
		if (this.game.canFinishAutomatically() || this.isWon) {
			this.sounds.gameWin.play();

			this.timer.stop();
			this._isFinishing = true;

			if (this.mode === 'random') {
				void supabase
					.from('solitaire_winnable_game_candidates')
					.insert({
						game: minifySolitaireGame(this.history.timeline.past[0].game),
						draw: this.drawAmount,
					})
					.then();
			}

			await this.context.addConfetti();

			// Prevent shuffle sound from playing when there is nothing to shuffle
			if (!this.game.isWon()) {
				this.sounds.deckShuffle.loop(true);
				this.sounds.deckShuffle.stopAndPlay();
			}

			this.finishAutomaticallyAnimation.start(this.game);
		}
	}

	private sync(...stateChanges: Array<GameStateChange | null>) {
		const hintStateChange = this.clearHint();
		const filteredStateChanges = stateChanges.filter(Boolean) as GameStateChange[];
		const allStateChanges = mergeStateChanges(hintStateChange, ...filteredStateChanges);

		if (allStateChanges) {
			this.layout.sync({
				...allStateChanges,
				// Retore current game state instead of using the hint state
				game: filteredStateChanges.length === 0 ? this.game : allStateChanges.game,
			});
		}
	}

	private playHint() {
		if (this.hintStateChange) {
			if (this.hint?.from === 'deck') {
				this.sounds.cardFlip.stopAndPlay();
			} else if (this.hint?.to === 'deck') {
				this.sounds.deckQuickBack.stopAndPlay();
			} else {
				this.sounds.cardPlay.stopAndPlay();
			}
			this.game = this.hintStateChange.game;
			this.history.add(this.hintStateChange);
			this._totalMoves += 1;
			this._hintsPlayed += 1;
			this.timer.start();
			this.clearHint(true);
			this.checkGameWin();
		}
	}

	/**
	 * @returns true if the game state changed due to a card drop
	 **/
	private onCardsDrop(cardIndex: number, from: SolitaireStack, to: SolitaireStack) {
		if (!this.canHandleUserEvents) {
			return false;
		}

		const stateChange = this.game.move(cardIndex, from, to);

		if (stateChange) {
			this.sounds.cardPlay.stopAndPlay();
			this.game = stateChange.game;
			this.history.add(stateChange);
			this._totalMoves += 1;
			this.timer.start();
		}

		this.sync(stateChange);

		this.checkGameWin();

		return !!stateChange;
	}

	private onCardTap(card: Card, index: number, stack: SolitaireStack) {
		if (!this.canHandleUserEvents) {
			return;
		}

		if (
			this.hintStateChange?.movingCards.some((hintCard) => getCardId(hintCard) === getCardId(card))
		) {
			// Play hint instead
			this.playHint();
			return;
		}

		const gameCard = this.cards[getCardId(card)];
		const stateChange =
			stack === 'deck'
				? this.game.drawCards()
				: this.game.moveCardsOnStackToABetterPosition(index, stack);

		if (stateChange) {
			this.sounds.cardPlay.stopAndPlay();
			this.game = stateChange.game;
			this.history.add(stateChange);
			this._totalMoves += 1;
			this.timer.start();
		} else if (!this.animationSystem.isPlaying(`move-${gameCard.id}`)) {
			const id = `shake-${gameCard.id}`;
			// Remove shake animation so the next one start from the correct position
			this.animationSystem.removeById(id);
			this.animationSystem.add({
				id,
				animation: shake({
					view: gameCard.view,
					duration: this.settings.shakeDuration,
				}),
				completeOnClear: true,
			});
		}

		this.sync(stateChange);
		this.checkGameWin();
	}

	private drawCards() {
		if (!this.canHandleUserEvents) {
			return;
		}

		let stateChange: GameStateChange | null = null;

		if (this.game.canDrawCards()) {
			this.sounds.cardFlip.stopAndPlay();
			stateChange = this.game.drawCards();
		} else if (this.game.canPutNextCardsBackOnDeck()) {
			this.sounds.deckQuickBack.play();
			stateChange = this.game.putNextCardsBackOnDeck();
		}

		if (stateChange) {
			this.game = stateChange.game;
			this.history.add(stateChange);
			this._totalMoves += 1;
			this.timer.start();
		}

		this.sync(stateChange);
		this.checkGameWin();
	}

	private onUndo() {
		if (!this.canUndo()) {
			return;
		}

		this.sounds.swoosh.stopAndPlay();
		const previousGameChange = this.history.state;
		this.history.undo();
		this.game = this.history.state.game;
		this._totalMoves += 1;
		this.sync({
			game: this.game,
			changedStacks: previousGameChange.changedStacks,
			movingCards: previousGameChange.movingCards,
		});
	}

	private onRedo() {
		if (!this.canRedo()) {
			return;
		}

		this.sounds.swoosh.stopAndPlay();
		this.history.redo();
		this.game = this.history.state.game;
		this._totalMoves += 1;
		this.sync(this.history.state);
	}

	private handleKeyDown = (event: KeyboardEvent) => {
		if (['INPUT', 'TEXTAREA', 'SELECT'].includes((event.target as HTMLElement).tagName)) {
			return;
		}

		const key = event.key.toLocaleLowerCase();

		if (key === 'd' && !event.shiftKey) {
			this.throttledDrawCards();
		}

		if (key === 'h') {
			this.throttledShowHint();
		}

		if (event.code === 'Digit1' && event.shiftKey) {
			this.throttledStartNewGame({
				draw: 1,
			});
		}

		if (event.code === 'Digit3' && event.shiftKey) {
			this.throttledStartNewGame({
				draw: 3,
			});
		}

		if (import.meta.env.DEV) {
			if (event.code === 'Digit0' && event.shiftKey) {
				this.throttledStartNewGame({
					draw: 1,
					animated: true,
					mode: 'debug',
				});
			}
		}
	};

	private clearState() {
		this.hint = null;
		this.hintStateChange = null;
		this._totalMoves = 0;
		this._hintsPlayed = 0;
		this._isFinishing = false;
		this.finishAutomaticallyAnimation.dispose();
		this.distributeCardsAnimation.dispose();
		this.winningAnimation?.dispose();
		this.winningAnimation = null;
		this.history.reset();
		this.timer.reset();
		this.sounds.deckShuffle.loop(false);
		this.sounds.deckShuffle.stop();
		this.sounds.applause.stop();
		this.clearHint();
	}

	private async animateCardsBackToDeck(animated: boolean) {
		const gameWithAllCardsOnDeck = new SolitaireGame(
			this.drawAmount,
			this.gameSettings.settings.shouldFinishAutomatically,
		);
		gameWithAllCardsOnDeck.deck = getStandardDeck();

		this.layout.sync({
			game: gameWithAllCardsOnDeck,
			animated,
		});

		if (animated) {
			this.sounds.deckQuickShuffle.stopAndPlay();
			await wait(this.moveDuration * 1.5);
		}
	}

	private async restartCurrentGame(animated: boolean = true) {
		if (this._isStarting) {
			return;
		}

		this._isStarting = true;
		this.game = this.history.timeline.past[0]?.game ?? this.history.timeline.present?.game;
		this.history.reset({
			game: this.game,
			changedStacks: [],
			movingCards: [],
		});
		this.clearState();

		await this.animateCardsBackToDeck(animated);

		this.sounds.deckShuffle.loop(false);
		this.sounds.deckShuffle.play();
		this.distributeCardsAnimation.start(this.game);
	}

	private async startNewGame({
		draw,
		animated = true,
		mode = this.gameSettings.settings.winnableGamesOnly ? 'winnable' : 'random',
		dailyGame,
	}: {
		draw: 1 | 3;
		animated?: boolean;
		mode?: GameMode;
		dailyGame?: SolitaireGame;
	}) {
		if (this._isStarting && mode !== 'daily') {
			return;
		}

		this._mode = mode;
		this.winnableGameFetchState = 'idle';
		this._isStarting = true;

		if (mode !== 'daily') {
			this.gameSettings.settings.drawAmount = draw;
		}

		this.game = new SolitaireGame(draw, this.gameSettings.settings.shouldFinishAutomatically);
		this.clearState();
		this.syncSettings();

		this.context.createGame({ isDaily: mode === 'daily' });

		await this.animateCardsBackToDeck(animated);

		let finalGame = this.game.clone();

		if (mode === 'debug') {
			finalGame.debugDistributeCardsNextToWin();
		} else if (mode === 'winnable') {
			try {
				this.winnableGameFetchState = 'loading';
				finalGame = await this.winnableSolitaireGameFactory.build(draw);
				this.winnableGameFetchState = 'success';
			} catch (error) {
				this.winnableGameFetchState = 'error';
				this._isStarting = false;
				return;
			}
		} else if (mode === 'daily' && dailyGame) {
			finalGame = dailyGame;
		} else {
			finalGame.distributeCards();
		}
		this.sounds.deckShuffle.play();
		this.distributeCardsAnimation.start(finalGame);
	}

	private addListeners() {
		window.addEventListener('keydown', this.handleKeyDown);
		this.undoableKeyboardListener.listen();
	}

	dispose() {
		this._isFinishing = false;
		this.context.dispose();
		this.cleanUpSettingsListener();
		window.removeEventListener('keydown', this.handleKeyDown);
		this.undoableKeyboardListener.dispose();
		this.winningAnimation?.dispose();
		this.finishAutomaticallyAnimation.dispose();
		this.distributeCardsAnimation.dispose();
		this.layout.dispose();
	}
}

function mergeStateChanges(...stateChanges: Array<GameStateChange | null>): GameStateChange | null {
	const filteredChanges = stateChanges.filter(Boolean) as GameStateChange[];

	if (filteredChanges.length === 0) {
		return null;
	}

	const lastGame = filteredChanges[filteredChanges.length - 1]?.game;

	return {
		game: lastGame,
		changedStacks: filteredChanges.map((change) => change.changedStacks).flatMap((_) => _),
		movingCards: filteredChanges.map((change) => change.movingCards).flatMap((_) => _),
	};
}
