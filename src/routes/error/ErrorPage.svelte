<script lang="ts">
	import Footer from '$lib/components/Footer.svelte';
	import Navbar from '$lib/components/Navbar/Navbar.svelte';
	import PageTransition from '$lib/components/PageTransition.svelte';
</script>

<PageTransition>
	<Navbar />

	<section
		class="min-h-screen-no-navbar container max-w-4xl flex flex-col items-center justify-center mx-auto p-4"
	>
		<div class="text-center">
			<h2 class="text-6xl font-extrabold">Oops! A Wild Error Appeared!</h2>

			<p class="mt-8 mb-10 text-xl">
				Don't worry — a team of highly trained NPCs are working to fix it. In the meantime, feel
				free to refresh the page or jump into another game.
			</p>

			<div class="grid sm:inline-grid grid-cols-1 sm:grid-cols-2 gap-4">
				<a rel="noopener noreferrer" href="/" class="btn-primary btn btn-outline py-3">
					Back to homepage
				</a>
				<button class="btn-primary btn py-3" onclick={() => location.reload()}>Refresh</button>
			</div>
		</div>
	</section>

	<Footer />
</PageTransition>
