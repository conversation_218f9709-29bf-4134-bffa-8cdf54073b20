[data-theme="light"][data-color-blind="true"],
[data-theme="light-classic"][data-color-blind="true"] {
  --color-game-tents-floor: var(--color-orange-300);
  --color-game-tents-grass: var(--color-green-600);
  --color-game-tents-grass-autofill: var(--color-green-700);
  --color-game-tents-tree-leaves: var(--color-green-800);
  --color-game-tents-tree-wood: var(--color-amber-900);
  --color-game-tents-tent-100: var(--color-amber-700);
  --color-game-tents-tent-200: var(--color-amber-800);
  --color-game-tents-tent-300: var(--color-amber-950);
  --color-game-tents-tent-error-100: var(--color-red-700);
  --color-game-tents-tent-error-200: var(--color-red-800);
  --color-game-tents-tent-error-300: var(--color-red-950);
  --color-game-tents-number-error: var(--color-red-900);
  --color-game-tents-number-success: var(--color-green-500);
  --color-game-tents-tent-error-indicator-stroke: var(--color-white);
  --color-game-tents-tent-error-indicator: var(--color-red-700);
  --color-game-wordle-not-present: var(--color-gray-400);
  --color-game-wordle-not-present-hover: var(--color-gray-500);
  --color-game-wordle-correct: var(--color-purple-900);
  --color-game-wordle-correct-hover: var(--color-purple-950);
  --color-game-wordle-misplaced: var(--color-orange-700);
  --color-game-wordle-misplaced-hover: var(--color-orange-800);
  --color-game-wordle-validated-text: var(--color-white);
  --color-game-wordle-border: var(--color-gray-500);
  --color-game-solitaire-field: #219653;
  --color-game-breakout-tile-1: var(--color-red-400);
  --color-game-breakout-tile-2: var(--color-orange-400);
  --color-game-breakout-tile-3: var(--color-yellow-400);
  --color-game-breakout-tile-4: var(--color-green-400);
  --color-game-breakout-tile-5: var(--color-blue-400);
  --color-game-breakout-tile-6: var(--color-purple-400);
  --color-game-tic-tac-toe-x: var(--color-orange-500);
  --color-game-tic-tac-toe-o: var(--color-purple-800);
  --color-game-color-memory-1: var(--color-emerald-800);
  --color-game-color-memory-2: var(--color-lime-600);
  --color-game-color-memory-3: var(--color-pink-500);
  --color-game-color-memory-4: var(--color-purple-950);
  --color-game-word-search-1: var(--color-red-500);
  --color-game-word-search-2: var(--color-orange-500);
  --color-game-word-search-3: var(--color-amber-500);
  --color-game-word-search-4: var(--color-yellow-500);
  --color-game-word-search-5: var(--color-lime-500);
  --color-game-word-search-6: var(--color-green-500);
  --color-game-word-search-7: var(--color-emerald-500);
  --color-game-word-search-8: var(--color-teal-500);
  --color-game-word-search-9: var(--color-cyan-500);
  --color-game-word-search-10: var(--color-sky-500);
  --color-game-word-search-11: var(--color-blue-500);
  --color-game-word-search-12: var(--color-indigo-500);
  --color-game-word-search-13: var(--color-violet-500);
  --color-game-word-search-14: var(--color-purple-500);
  --color-game-word-search-15: var(--color-fuchsia-500);
  --color-game-word-search-16: var(--color-pink-500);
  --color-game-word-search-17: var(--color-rose-500);
  --color-game-2048-2: var(--color-orange-300);
  --color-game-2048-4: var(--color-orange-400);
  --color-game-2048-8: var(--color-orange-600);
  --color-game-2048-16: var(--color-orange-600);
  --color-game-2048-32: var(--color-green-600);
  --color-game-2048-64: var(--color-green-700);
  --color-game-2048-128: var(--color-purple-700);
  --color-game-2048-512: var(--color-purple-800);
  --color-game-2048-4096: var(--color-violet-900);
  --color-game-2048-16384: var(--color-gray-600);
  --color-game-2048-text-below-8: var(--color-black);
  --color-game-2048-text-above-8: var(--color-white);
  --color-game-2048-bg: var(--color-orange-50);
  --color-game-sudoku-tile-normal: var(--color-base-100);
  --color-game-sudoku-tile-highlighted: var(--color-purple-500);
  --color-game-sudoku-tile-selected: var(--color-purple-300);
  --color-game-sudoku-tile-immutable: var(--color-gray-400);
  --color-game-sudoku-tile-hint: var(--color-orange-800);
  --color-game-sudoku-tile-error: var(--color-red-200);
  --color-game-sudoku-tile-border-match: var(--color-purple-800);
  --color-game-sudoku-tile-border-match-selected: var(--color-purple-950);
  --color-game-sudoku-number-normal: var(--color-base-content);
  --color-game-sudoku-number-hint: var(--color-orange-50);
  --color-game-sudoku-number-error: var(--color-red-900);
  --color-game-sudoku-check-line: var(--color-red-900);
  --color-game-sudoku-board-inner-lines: var(--color-base-content);
  --color-game-sudoku-board-outer-lines: var(--color-base-content);
  --color-game-minesweeper-grid: var(--color-base-content);
  --color-game-minesweeper-background: var(--color-base-300);
  --color-game-minesweeper-1: #3477F5;
  --color-game-minesweeper-2: #1FBD53;
  --color-game-minesweeper-3: #ED3C3C;
  --color-game-minesweeper-4: #9E4BF6;
  --color-game-minesweeper-5: #F86816;
  --color-game-minesweeper-6: #E7AA0C;
  --color-game-minesweeper-7: #D43DED;
  --color-game-minesweeper-8: #11B076;
  --color-game-minesweeper-highlight: var(--color-yellow-300);
  --color-game-minesweeper-highlight-border: var(--color-yellow-500);

  --confetti-1: var(--color-green-300);
  --confetti-2: var(--color-cyan-300);
  --confetti-3: var(--color-pink-300);
  --confetti-4: var(--color-purple-600);
  --confetti-5: var(--color-teal-300);
  --confetti-6: var(--color-yellow-300);
}