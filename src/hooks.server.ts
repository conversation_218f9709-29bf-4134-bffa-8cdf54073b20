import { sequence } from '@sveltejs/kit/hooks';
import { handleErrorWithSentry, sentryHandle, initCloudflareSentryHandle } from '@sentry/sveltekit';

// If you have custom handlers, make sure to place them after `sentryHandle()` in the `sequence` function.
export const handle = sequence(
	initCloudflareSentryHandle({
		dsn: 'https://<EMAIL>/4507924882456576',

		tracesSampleRate: 1.0,

		// uncomment the line below to enable Spotlight (https://spotlightjs.com)
		// spotlight: import.meta.env.DEV,
		enabled: import.meta.env.PROD,
		environment: import.meta.env.PROD ? 'production' : 'development',
		integrations: [],
	}),
	sentryHandle(),
);

// If you have a custom error handler, pass it to `handleErrorWithSentry`
export const handleError = handleErrorWithSentry();
